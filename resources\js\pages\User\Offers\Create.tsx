import { Head, useForm, <PERSON> } from '@inertiajs/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { ArrowLeft, DollarSign } from 'lucide-react';
import MainLayout from '@/layouts/MainLayout';
import { PageProps } from '@/types';

interface Advertisement {
    id: number;
    title: string;
    price: number;
    description: string;
    location: string;
    vehicle: {
        brand: {
            name: string;
        };
        model: string;
        year_manufacture: number;
    };
    user: {
        id: number;
        name: string;
    };
    images: Array<{
        url: string;
    }>;
}

interface CreateOfferProps extends PageProps {
    advertisement: Advertisement;
}

export default function CreateOffer({ advertisement }: CreateOfferProps) {
    const { data, setData, post, processing, errors } = useForm({
        amount: '',
        message: '',
    });

    const formatPrice = (price: number) => {
        return new Intl.NumberFormat('pt-BR', {
            style: 'currency',
            currency: 'BRL',
        }).format(price);
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        post(`/minha-conta/ofertas/${advertisement.id}`);
    };

    return (
        <MainLayout>
            <Head title={`Fazer Oferta - ${advertisement.title}`} />
            
            <div className="min-h-screen bg-gray-50">
                <div className="container mx-auto px-4 py-6">
                    {/* Header */}
                    <div className="mb-6">
                        <Link href="/minha-conta/ofertas">
                            <Button variant="outline" className="mb-4">
                                <ArrowLeft className="mr-2 h-4 w-4" />
                                Voltar para ofertas
                            </Button>
                        </Link>
                        
                        <h1 className="text-3xl font-bold">Fazer Oferta</h1>
                        <p className="text-gray-600">
                            Envie uma proposta para o vendedor
                        </p>
                    </div>

                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        {/* Advertisement Info */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Anúncio</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="flex gap-4">
                                    <img
                                        src={advertisement.images[0]?.url || '/placeholder-car.jpg'}
                                        alt={advertisement.title}
                                        className="w-24 h-24 object-cover rounded"
                                    />
                                    <div className="flex-1">
                                        <h3 className="font-semibold text-lg">
                                            {advertisement.title}
                                        </h3>
                                        <p className="text-gray-600">
                                            {advertisement.vehicle.brand.name} {advertisement.vehicle.model} {advertisement.vehicle.year_manufacture}
                                        </p>
                                        <p className="text-2xl font-bold text-green-600">
                                            {formatPrice(advertisement.price)}
                                        </p>
                                        <p className="text-sm text-gray-500">
                                            {advertisement.location}
                                        </p>
                                    </div>
                                </div>
                                
                                <div className="mt-4 pt-4 border-t">
                                    <h4 className="font-medium mb-2">Vendedor</h4>
                                    <p className="text-gray-600">{advertisement.user.name}</p>
                                </div>
                            </CardContent>
                        </Card>

                        {/* Offer Form */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <DollarSign className="h-5 w-5" />
                                    Sua Oferta
                                </CardTitle>
                                <CardDescription>
                                    Faça uma proposta justa e atrativa
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                <form onSubmit={handleSubmit} className="space-y-4">
                                    <div>
                                        <Label htmlFor="amount">Valor da Oferta *</Label>
                                        <Input
                                            id="amount"
                                            type="number"
                                            step="0.01"
                                            min="1"
                                            placeholder="Ex: 150000.00"
                                            value={data.amount}
                                            onChange={(e) => setData('amount', e.target.value)}
                                            className={errors.amount ? 'border-red-500' : ''}
                                        />
                                        {errors.amount && (
                                            <p className="text-sm text-red-500 mt-1">
                                                {errors.amount}
                                            </p>
                                        )}
                                        <p className="text-sm text-gray-500 mt-1">
                                            Preço original: {formatPrice(advertisement.price)}
                                        </p>
                                    </div>

                                    <div>
                                        <Label htmlFor="message">Mensagem (opcional)</Label>
                                        <Textarea
                                            id="message"
                                            placeholder="Adicione uma mensagem para o vendedor..."
                                            value={data.message}
                                            onChange={(e) => setData('message', e.target.value)}
                                            className={errors.message ? 'border-red-500' : ''}
                                            rows={4}
                                        />
                                        {errors.message && (
                                            <p className="text-sm text-red-500 mt-1">
                                                {errors.message}
                                            </p>
                                        )}
                                    </div>

                                    <div className="pt-4">
                                        <Button 
                                            type="submit" 
                                            disabled={processing || !data.amount}
                                            className="w-full"
                                        >
                                            {processing ? 'Enviando...' : 'Enviar Oferta'}
                                        </Button>
                                    </div>
                                </form>
                            </CardContent>
                        </Card>
                    </div>
                </div>
            </div>
        </MainLayout>
    );
}
