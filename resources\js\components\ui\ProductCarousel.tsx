import { ReactNode } from "react";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";
import { cn } from "@/lib/utils";

interface ProductCarouselProps {
  children: ReactNode[];
  className?: string;
  itemClassName?: string;
  showNavigation?: boolean;
  emptyMessage?: string;
  emptyIcon?: string;
  itemsPerView?: number;
}

export default function ProductCarousel({
  children,
  className,
  itemClassName,
  showNavigation = true,
  emptyMessage = "Nenhum produto disponível",
  emptyIcon = "📦",
  itemsPerView = 4,
}: ProductCarouselProps) {
  if (children.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-400 text-6xl mb-4">{emptyIcon}</div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          {emptyMessage}
        </h3>
        <p className="text-gray-600">
          Volte em breve para ver novos produtos.
        </p>
      </div>
    );
  }

  // If we have fewer items than itemsPerView, show all items without carousel
  if (children.length <= itemsPerView) {
    return (
      <div className={cn("flex w-full gap-4", className)}>
        {children.map((child, index) => (
          <div 
            key={index} 
            className="flex-shrink-0"
            style={{
              flex: `0 0 calc(100% / ${itemsPerView})`,
              maxWidth: `calc(100% / ${itemsPerView})`
            }}
          >
            {child}
          </div>
        ))}
      </div>
    );
  }

  return (
    <Carousel
      className={cn("w-full overflow-visible", className)}
      opts={{
        align: "start",
        loop: false,
        startIndex: 0,
      }}
    >
      <CarouselContent className="ml-0">
        {children.map((child, index) => (
          <CarouselItem 
            key={index} 
            className={cn("pr-4", itemClassName)}
            style={{ 
              flex: `0 0 calc(100% / ${itemsPerView})`,
              maxWidth: `calc(100% / ${itemsPerView})`
            }}
          >
            {child}
          </CarouselItem>
        ))}
      </CarouselContent>
      {showNavigation && (
        <>
          <CarouselPrevious className="left-0" />
          <CarouselNext className="right-0" />
        </>
      )}
    </Carousel>
  );
}

// Specialized carousel for different screen sizes
interface ResponsiveProductCarouselProps extends Omit<ProductCarouselProps, 'itemsPerView'> {
  itemsPerView?: {
    mobile?: number;
    tablet?: number;
    desktop?: number;
  };
}

export function ResponsiveProductCarousel({
  children,
  className,
  itemClassName,
  showNavigation = true,
  emptyMessage = "Nenhum produto disponível",
  emptyIcon = "📦",
  itemsPerView = { mobile: 1, tablet: 2, desktop: 4 },
}: ResponsiveProductCarouselProps) {
  if (children.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-400 text-6xl mb-4">{emptyIcon}</div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          {emptyMessage}
        </h3>
        <p className="text-gray-600">
          Volte em breve para ver novos produtos.
        </p>
      </div>
    );
  }

  return (
    <Carousel
      className={cn("w-full", className)}
      opts={{
        align: "start",
        loop: false,
      }}
    >
      <CarouselContent className="-ml-2 md:-ml-4">
        {children.map((child, index) => (
          <CarouselItem 
            key={index} 
            className={cn(
              "pl-2 md:pl-4",
              "basis-full", // mobile: 1 item
              `sm:basis-1/${itemsPerView.tablet || 2}`, // tablet: 2 items
              `lg:basis-1/${itemsPerView.desktop || 4}`, // desktop: 4 items
              itemClassName
            )}
          >
            {child}
          </CarouselItem>
        ))}
      </CarouselContent>
      {showNavigation && (
        <>
          <CarouselPrevious className="left-0" />
          <CarouselNext className="right-0" />
        </>
      )}
    </Carousel>
  );
}
