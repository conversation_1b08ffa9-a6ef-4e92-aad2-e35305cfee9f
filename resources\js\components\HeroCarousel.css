/* Estilo personalizado para as setas de navegação do Swiper */
.swiper-button-next,
.swiper-button-prev {
    color: var(--color-primary) !important;
    /* Usando a variável de cor primária */
}

/* Estilo para o hover */
.swiper-button-next:hover,
.swiper-button-prev:hover {
    color: var(--color-primary-foreground) !important;
    opacity: 1 !important;
}

/* Estilo para os ícones dentro dos botões */
.swiper-button-next:after,
.swiper-button-prev:after {
    font-size: 2rem !important;
    /* Tamanho do ícone */
}

/* Estilo para quando o botão estiver desabilitado */
.swiper-button-disabled {
    opacity: 0.35 !important;
    cursor: not-allowed !important;
    pointer-events: auto !important;
}