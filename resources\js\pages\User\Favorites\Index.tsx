import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select';
import MainLayout from '@/layouts/MainLayout';
import { PageProps } from '@/types';
import { Head, Link, router } from '@inertiajs/react';
import {
    Filter,
    Grid,
    Heart,
    List,
    Search,
    SortAsc,
    SortDesc,
    Trash2,
} from 'lucide-react';
import { useState } from 'react';

interface Brand {
    id: number;
    name: string;
}

interface Favorite {
    id: number;
    created_at: string;
    advertisement: {
        id: number;
        title: string;
        price: number;
        location: string;
        vehicle: {
            brand: {
                name: string;
            };
            model: string;
            year: number;
            mileage: number;
            fuel_type: string;
        };
        featured_image?: {
            url: string;
        };
        user: {
            id: number;
            name: string;
        };
    };
}

interface FavoritesIndexProps extends PageProps {
    favorites: {
        data: Favorite[];
        links: any;
        meta: any;
    };
    brands: Brand[];
    filters: {
        search?: string;
        min_price?: string;
        max_price?: string;
        brand_id?: string;
        sort?: string;
        order?: string;
    };
}

export default function FavoritesIndex({
    favorites,
    brands = [],
    filters = {},
}: FavoritesIndexProps) {
    // Verificação de segurança
    if (!favorites) {
        return (
            <MainLayout>
                <Head title="Meus Favoritos" />
                <div className="min-h-screen bg-gray-50">
                    <div className="container mx-auto px-4 py-6">
                        <div className="text-center">Carregando...</div>
                    </div>
                </div>
            </MainLayout>
        );
    }

    const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
    const [selectedFavorites, setSelectedFavorites] = useState<number[]>([]);
    const [searchQuery, setSearchQuery] = useState(filters?.search || '');
    const [minPrice, setMinPrice] = useState(filters?.min_price || '');
    const [maxPrice, setMaxPrice] = useState(filters?.max_price || '');
    const [selectedBrand, setSelectedBrand] = useState(filters?.brand_id || '');
    const [sortBy, setSortBy] = useState(filters?.sort || 'created_at');
    const [sortOrder, setSortOrder] = useState(filters?.order || 'desc');

    const handleSearch = () => {
        const params = new URLSearchParams();

        if (searchQuery) params.append('search', searchQuery);
        if (minPrice) params.append('min_price', minPrice);
        if (maxPrice) params.append('max_price', maxPrice);
        if (selectedBrand) params.append('brand_id', selectedBrand);
        if (sortBy) params.append('sort', sortBy);
        if (sortOrder) params.append('order', sortOrder);

        router.get('/minha-conta/favoritos', Object.fromEntries(params));
    };

    const handleSort = (field: string) => {
        const newOrder =
            sortBy === field && sortOrder === 'asc' ? 'desc' : 'asc';
        setSortBy(field);
        setSortOrder(newOrder);

        const params = new URLSearchParams();
        if (searchQuery) params.append('search', searchQuery);
        if (minPrice) params.append('min_price', minPrice);
        if (maxPrice) params.append('max_price', maxPrice);
        if (selectedBrand) params.append('brand_id', selectedBrand);
        params.append('sort', field);
        params.append('order', newOrder);

        router.get('/minha-conta/favoritos', Object.fromEntries(params));
    };

    const handleSelectAll = () => {
        if (!favorites?.data) return;

        if (selectedFavorites.length === favorites.data.length) {
            setSelectedFavorites([]);
        } else {
            setSelectedFavorites(
                favorites.data.map((fav) => fav.advertisement.id),
            );
        }
    };

    const handleSelectFavorite = (advertisementId: number) => {
        setSelectedFavorites((prev) =>
            prev.includes(advertisementId)
                ? prev.filter((id) => id !== advertisementId)
                : [...prev, advertisementId],
        );
    };

    const handleRemoveSelected = () => {
        if (selectedFavorites.length === 0) return;

        router.delete('/minha-conta/favoritos', {
            data: { advertisement_ids: selectedFavorites },
            onSuccess: () => {
                setSelectedFavorites([]);
            },
        });
    };

    const handleRemoveFavorite = (advertisementId: number) => {
        router.delete(`/minha-conta/favoritos/${advertisementId}`);
    };

    const formatPrice = (price: number) => {
        return new Intl.NumberFormat('pt-BR', {
            style: 'currency',
            currency: 'BRL',
        }).format(price);
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('pt-BR');
    };

    return (
        <MainLayout>
            <Head title="Meus Favoritos" />

            <div className="min-h-screen bg-gray-50">
                <div className="container mx-auto px-4 py-6">
                    {/* Header */}
                    <div className="mb-6">
                        <h1 className="flex items-center gap-2 text-3xl font-bold">
                            <Heart className="h-8 w-8 text-red-500" />
                            Meus Favoritos
                        </h1>
                        <p className="text-gray-600">
                            Gerencie seus anúncios favoritos
                        </p>
                    </div>

                    <div className="grid grid-cols-1 gap-6 lg:grid-cols-4">
                        {/* Sidebar com filtros */}
                        <div className="space-y-4 lg:col-span-1">
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <Filter className="h-5 w-5" />
                                        Filtros
                                    </CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div>
                                        <Label htmlFor="search">Buscar</Label>
                                        <Input
                                            id="search"
                                            placeholder="Buscar por título, marca..."
                                            value={searchQuery}
                                            onChange={(e) =>
                                                setSearchQuery(e.target.value)
                                            }
                                        />
                                    </div>

                                    <div className="grid grid-cols-2 gap-2">
                                        <div>
                                            <Label htmlFor="min_price">
                                                Preço mín.
                                            </Label>
                                            <Input
                                                id="min_price"
                                                type="number"
                                                placeholder="0"
                                                value={minPrice}
                                                onChange={(e) =>
                                                    setMinPrice(e.target.value)
                                                }
                                            />
                                        </div>
                                        <div>
                                            <Label htmlFor="max_price">
                                                Preço máx.
                                            </Label>
                                            <Input
                                                id="max_price"
                                                type="number"
                                                placeholder="999999"
                                                value={maxPrice}
                                                onChange={(e) =>
                                                    setMaxPrice(e.target.value)
                                                }
                                            />
                                        </div>
                                    </div>

                                    <div>
                                        <Label htmlFor="brand">Marca</Label>
                                        <Select
                                            value={selectedBrand}
                                            onValueChange={setSelectedBrand}
                                        >
                                            <SelectTrigger>
                                                <SelectValue placeholder="Todas as marcas" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="">
                                                    Todas as marcas
                                                </SelectItem>
                                                {brands?.map((brand) => (
                                                    <SelectItem
                                                        key={brand.id}
                                                        value={brand.id.toString()}
                                                    >
                                                        {brand.name}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                    </div>

                                    <Button
                                        onClick={handleSearch}
                                        className="w-full"
                                    >
                                        <Search className="mr-2 h-4 w-4" />
                                        Buscar
                                    </Button>
                                </CardContent>
                            </Card>

                            <Card>
                                <CardHeader>
                                    <CardTitle>Estatísticas</CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-2">
                                    <div className="flex justify-between">
                                        <span className="text-sm text-gray-600">
                                            Total de favoritos:
                                        </span>
                                        <span className="font-medium">
                                            {favorites.meta.total}
                                        </span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span className="text-sm text-gray-600">
                                            Selecionados:
                                        </span>
                                        <span className="font-medium text-orange-600">
                                            {selectedFavorites.length}
                                        </span>
                                    </div>
                                </CardContent>
                            </Card>
                        </div>

                        {/* Lista de favoritos */}
                        <div className="lg:col-span-3">
                            {/* Toolbar */}
                            <Card className="mb-4">
                                <CardContent className="py-4">
                                    <div className="flex items-center justify-between">
                                        <div className="flex items-center gap-4">
                                            <div className="flex items-center gap-2">
                                                <Checkbox
                                                    checked={
                                                        selectedFavorites.length ===
                                                            favorites.data
                                                                .length &&
                                                        favorites.data.length >
                                                            0
                                                    }
                                                    onCheckedChange={
                                                        handleSelectAll
                                                    }
                                                />
                                                <span className="text-sm">
                                                    Selecionar todos
                                                </span>
                                            </div>

                                            {selectedFavorites.length > 0 && (
                                                <Button
                                                    variant="destructive"
                                                    size="sm"
                                                    onClick={
                                                        handleRemoveSelected
                                                    }
                                                >
                                                    <Trash2 className="mr-2 h-4 w-4" />
                                                    Remover selecionados (
                                                    {selectedFavorites.length})
                                                </Button>
                                            )}
                                        </div>

                                        <div className="flex items-center gap-2">
                                            <Select
                                                value={sortBy}
                                                onValueChange={(value) =>
                                                    handleSort(value)
                                                }
                                            >
                                                <SelectTrigger className="w-40">
                                                    <SelectValue />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem value="created_at">
                                                        Data de adição
                                                    </SelectItem>
                                                    <SelectItem value="price">
                                                        Preço
                                                    </SelectItem>
                                                    <SelectItem value="title">
                                                        Título
                                                    </SelectItem>
                                                </SelectContent>
                                            </Select>

                                            <Button
                                                variant="outline"
                                                size="sm"
                                                onClick={() =>
                                                    handleSort(sortBy)
                                                }
                                            >
                                                {sortOrder === 'asc' ? (
                                                    <SortAsc className="h-4 w-4" />
                                                ) : (
                                                    <SortDesc className="h-4 w-4" />
                                                )}
                                            </Button>

                                            <div className="flex rounded-md border">
                                                <Button
                                                    variant={
                                                        viewMode === 'grid'
                                                            ? 'default'
                                                            : 'ghost'
                                                    }
                                                    size="sm"
                                                    onClick={() =>
                                                        setViewMode('grid')
                                                    }
                                                    className="rounded-r-none"
                                                >
                                                    <Grid className="h-4 w-4" />
                                                </Button>
                                                <Button
                                                    variant={
                                                        viewMode === 'list'
                                                            ? 'default'
                                                            : 'ghost'
                                                    }
                                                    size="sm"
                                                    onClick={() =>
                                                        setViewMode('list')
                                                    }
                                                    className="rounded-l-none"
                                                >
                                                    <List className="h-4 w-4" />
                                                </Button>
                                            </div>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>

                            {/* Conteúdo */}
                            {!favorites?.data || favorites.data.length === 0 ? (
                                <Card>
                                    <CardContent className="py-12 text-center">
                                        <Heart className="mx-auto mb-4 h-12 w-12 text-gray-400" />
                                        <h3 className="mb-2 text-lg font-medium text-gray-900">
                                            Nenhum favorito ainda
                                        </h3>
                                        <p className="mb-4 text-gray-600">
                                            Quando você favoritar anúncios, eles
                                            aparecerão aqui.
                                        </p>
                                        <Link href="/anuncios">
                                            <Button>Explorar anúncios</Button>
                                        </Link>
                                    </CardContent>
                                </Card>
                            ) : (
                                <div
                                    className={
                                        viewMode === 'grid'
                                            ? 'grid grid-cols-1 gap-4 md:grid-cols-2 xl:grid-cols-3'
                                            : 'space-y-4'
                                    }
                                >
                                    {favorites?.data?.map((favorite) => (
                                        <Card
                                            key={favorite.id}
                                            className="transition-shadow hover:shadow-md"
                                        >
                                            <CardContent className="p-4">
                                                <div
                                                    className={
                                                        viewMode === 'grid'
                                                            ? 'space-y-3'
                                                            : 'flex gap-4'
                                                    }
                                                >
                                                    <div className="flex items-start gap-3">
                                                        <Checkbox
                                                            checked={selectedFavorites.includes(
                                                                favorite
                                                                    .advertisement
                                                                    .id,
                                                            )}
                                                            onCheckedChange={() =>
                                                                handleSelectFavorite(
                                                                    favorite
                                                                        .advertisement
                                                                        .id,
                                                                )
                                                            }
                                                        />

                                                        <img
                                                            src={
                                                                favorite
                                                                    .advertisement
                                                                    .featured_image
                                                                    ?.url ||
                                                                '/placeholder-car.jpg'
                                                            }
                                                            alt={
                                                                favorite
                                                                    .advertisement
                                                                    .title
                                                            }
                                                            className={
                                                                viewMode ===
                                                                'grid'
                                                                    ? 'h-48 w-full rounded-lg object-cover'
                                                                    : 'h-24 w-24 rounded-lg object-cover'
                                                            }
                                                        />
                                                    </div>

                                                    <div className="flex-1 space-y-2">
                                                        <div className="flex items-start justify-between">
                                                            <h3 className="line-clamp-2 font-medium text-gray-900">
                                                                {
                                                                    favorite
                                                                        .advertisement
                                                                        .title
                                                                }
                                                            </h3>
                                                            <Button
                                                                variant="ghost"
                                                                size="sm"
                                                                onClick={() =>
                                                                    handleRemoveFavorite(
                                                                        favorite
                                                                            .advertisement
                                                                            .id,
                                                                    )
                                                                }
                                                                className="text-red-600 hover:text-red-700"
                                                            >
                                                                <Heart className="h-4 w-4 fill-current" />
                                                            </Button>
                                                        </div>

                                                        <p className="text-sm text-gray-600">
                                                            {
                                                                favorite
                                                                    .advertisement
                                                                    .vehicle
                                                                    .brand.name
                                                            }{' '}
                                                            {
                                                                favorite
                                                                    .advertisement
                                                                    .vehicle
                                                                    .model
                                                            }{' '}
                                                            •{' '}
                                                            {
                                                                favorite
                                                                    .advertisement
                                                                    .vehicle
                                                                    .year
                                                            }
                                                        </p>

                                                        <p className="text-lg font-bold text-green-600">
                                                            {formatPrice(
                                                                favorite
                                                                    .advertisement
                                                                    .price,
                                                            )}
                                                        </p>

                                                        <p className="text-sm text-gray-500">
                                                            {
                                                                favorite
                                                                    .advertisement
                                                                    .location
                                                            }{' '}
                                                            • Adicionado em{' '}
                                                            {formatDate(
                                                                favorite.created_at,
                                                            )}
                                                        </p>

                                                        <div className="flex gap-2 pt-2">
                                                            <Link
                                                                href={`/anuncio/${favorite.advertisement.id}`}
                                                            >
                                                                <Button
                                                                    size="sm"
                                                                    className="flex-1"
                                                                >
                                                                    Ver anúncio
                                                                </Button>
                                                            </Link>

                                                            <Link
                                                                href={`/minha-conta/chat/start/${favorite.advertisement.id}`}
                                                            >
                                                                <Button
                                                                    size="sm"
                                                                    variant="outline"
                                                                >
                                                                    Conversar
                                                                </Button>
                                                            </Link>
                                                        </div>
                                                    </div>
                                                </div>
                                            </CardContent>
                                        </Card>
                                    ))}
                                </div>
                            )}

                            {/* Paginação */}
                            {favorites.links && (
                                <div className="mt-6 flex justify-center">
                                    {/* Implementar paginação aqui se necessário */}
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            </div>
        </MainLayout>
    );
}
