"use client"

import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"

interface VehicleDetailsStepProps {
  formData: any
  updateFormData: (data: any) => void
  onNext: () => void
  onPrev: () => void
}

const fuelTypes = ["Flex", "Gasolina", "Diesel", "Elétrico", "Híbrido", "GNV", "Álcool"]

const transmissionTypes = ["Manual", "Automático", "CVT", "Automatizada"]

const colors = [
  "Branco",
  "Prata",
  "Preto",
  "Cinza",
  "Azul",
  "Vermelho",
  "Verde",
  "Amarelo",
  "Marrom",
  "Bege",
  "Dourado",
  "Outro",
]

const features = [
  "Ar condicionado",
  "Direção hidráulica",
  "Direção elétrica",
  "Vidros elétricos",
  "Travas elétricas",
  "Alarme",
  "Som",
  "CD Player",
  "DVD Player",
  "GPS",
  "Câmera de ré",
  "Sensores de estacionamento",
  "Airbag",
  "Freios ABS",
  "Controle de estabilidade",
  "Controle de tração",
  "Bancos de couro",
  "Ar quente",
  "Desembaçador traseiro",
  "Limpador traseiro",
  "Teto solar",
  "Rodas de liga leve",
  "Pneus novos",
]

export function VehicleDetailsStep({ formData, updateFormData }: VehicleDetailsStepProps) {
  const handleFeatureChange = (feature: string, checked: boolean) => {
    const currentFeatures = formData.features || []
    if (checked) {
      updateFormData({ features: [...currentFeatures, feature] })
    } else {
      updateFormData({ features: currentFeatures.filter((f: string) => f !== feature) })
    }
  }

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Mileage */}
        <div className="space-y-2">
          <Label htmlFor="mileage">Quilometragem *</Label>
          <div className="relative">
            <Input
              id="mileage"
              type="number"
              placeholder="0"
              value={formData.mileage}
              onChange={(e) => updateFormData({ mileage: e.target.value })}
            />
            <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground text-sm">
              km
            </span>
          </div>
        </div>

        {/* Fuel */}
        <div className="space-y-2">
          <Label htmlFor="fuel">Combustível *</Label>
          <Select value={formData.fuel} onValueChange={(value) => updateFormData({ fuel: value })}>
            <SelectTrigger>
              <SelectValue placeholder="Selecione o combustível" />
            </SelectTrigger>
            <SelectContent>
              {fuelTypes.map((fuel) => (
                <SelectItem key={fuel} value={fuel.toLowerCase()}>
                  {fuel}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Transmission */}
        <div className="space-y-2">
          <Label htmlFor="transmission">Transmissão *</Label>
          <Select value={formData.transmission} onValueChange={(value) => updateFormData({ transmission: value })}>
            <SelectTrigger>
              <SelectValue placeholder="Selecione a transmissão" />
            </SelectTrigger>
            <SelectContent>
              {transmissionTypes.map((transmission) => (
                <SelectItem key={transmission} value={transmission.toLowerCase()}>
                  {transmission}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Color */}
        <div className="space-y-2">
          <Label htmlFor="color">Cor *</Label>
          <Select value={formData.color} onValueChange={(value) => updateFormData({ color: value })}>
            <SelectTrigger>
              <SelectValue placeholder="Selecione a cor" />
            </SelectTrigger>
            <SelectContent>
              {colors.map((color) => (
                <SelectItem key={color} value={color.toLowerCase()}>
                  {color}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Doors */}
        <div className="space-y-2">
          <Label htmlFor="doors">Portas</Label>
          <Select value={formData.doors} onValueChange={(value) => updateFormData({ doors: value })}>
            <SelectTrigger>
              <SelectValue placeholder="Número de portas" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="2">2 portas</SelectItem>
              <SelectItem value="3">3 portas</SelectItem>
              <SelectItem value="4">4 portas</SelectItem>
              <SelectItem value="5">5 portas</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Engine */}
        <div className="space-y-2">
          <Label htmlFor="engine">Motor</Label>
          <Input
            id="engine"
            placeholder="Ex: 1.0, 1.6 16V, 2.0 Turbo"
            value={formData.engine}
            onChange={(e) => updateFormData({ engine: e.target.value })}
          />
        </div>
      </div>

      {/* Features */}
      <div className="space-y-2">
        <Label>Equipamentos e Opcionais</Label>
        <div className="grid grid-cols-2 md:grid-cols-3 gap-3 max-h-60 overflow-y-auto p-4 border rounded-lg">
          {features.map((feature) => (
            <div key={feature} className="flex items-center space-x-2">
              <Checkbox
                id={feature}
                checked={formData.features?.includes(feature) || false}
                onCheckedChange={(checked) => handleFeatureChange(feature, checked as boolean)}
              />
              <Label htmlFor={feature} className="text-sm font-normal cursor-pointer">
                {feature}
              </Label>
            </div>
          ))}
        </div>
      </div>

      {/* Description */}
      <div className="space-y-2">
        <Label htmlFor="description">Descrição *</Label>
        <Textarea
          id="description"
          placeholder="Descreva o veículo, seu estado de conservação, histórico, motivo da venda, etc."
          rows={5}
          value={formData.description}
          onChange={(e) => updateFormData({ description: e.target.value })}
        />
        <p className="text-sm text-muted-foreground">
          Uma boa descrição aumenta as chances de venda. Seja honesto sobre o estado do veículo.
        </p>
      </div>
    </div>
  )
}
