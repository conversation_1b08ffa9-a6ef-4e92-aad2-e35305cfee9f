<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class NotificationController extends Controller
{
    /**
     * Display a listing of the user's notifications.
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        
        $notifications = $user->notifications()
            ->when($request->filter === 'unread', function ($query) {
                return $query->whereNull('read_at');
            })
            ->when($request->filter === 'read', function ($query) {
                return $query->whereNotNull('read_at');
            })
            ->when($request->type, function ($query, $type) {
                return $query->where('type', $type);
            })
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        $unreadCount = $user->unreadNotifications()->count();
        
        $notificationTypes = [
            'App\Notifications\NewMessage' => 'Nova Mensagem',
            'App\Notifications\NewOffer' => 'Nova Oferta',
            'App\Notifications\AdvertisementExpired' => 'Anúncio Expirado',
            'App\Notifications\AdvertisementApproved' => 'Anúncio Aprovado',
            'App\Notifications\AdvertisementRejected' => 'Anúncio Rejeitado',
            'App\Notifications\FavoriteAdvertisementUpdated' => 'Favorito Atualizado',
        ];

        return Inertia::render('User/Notifications/Index', [
            'notifications' => $notifications,
            'unreadCount' => $unreadCount,
            'notificationTypes' => $notificationTypes,
            'filters' => [
                'filter' => $request->filter,
                'type' => $request->type,
            ],
        ]);
    }

    /**
     * Mark a notification as read.
     */
    public function markAsRead(Request $request, $id)
    {
        $user = Auth::user();
        
        $notification = $user->notifications()->findOrFail($id);
        
        if (!$notification->read_at) {
            $notification->markAsRead();
        }

        return response()->json(['success' => true]);
    }

    /**
     * Mark all notifications as read.
     */
    public function markAllAsRead(Request $request)
    {
        $user = Auth::user();
        
        $user->unreadNotifications()->update(['read_at' => now()]);

        return response()->json(['success' => true]);
    }

    /**
     * Delete a notification.
     */
    public function destroy(Request $request, $id)
    {
        $user = Auth::user();
        
        $notification = $user->notifications()->findOrFail($id);
        $notification->delete();

        return response()->json(['success' => true]);
    }

    /**
     * Delete all read notifications.
     */
    public function deleteAllRead(Request $request)
    {
        $user = Auth::user();
        
        $user->readNotifications()->delete();

        return response()->json(['success' => true]);
    }

    /**
     * Get notification count for header.
     */
    public function getCount()
    {
        $user = Auth::user();
        
        return response()->json([
            'unread_count' => $user->unreadNotifications()->count(),
            'total_count' => $user->notifications()->count(),
        ]);
    }

    /**
     * Get recent notifications for dropdown.
     */
    public function getRecent()
    {
        $user = Auth::user();
        
        $notifications = $user->notifications()
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get()
            ->map(function ($notification) {
                return [
                    'id' => $notification->id,
                    'type' => $notification->type,
                    'data' => $notification->data,
                    'read_at' => $notification->read_at,
                    'created_at' => $notification->created_at,
                    'formatted_time' => $notification->created_at->diffForHumans(),
                    'title' => $this->getNotificationTitle($notification),
                    'message' => $this->getNotificationMessage($notification),
                    'icon' => $this->getNotificationIcon($notification),
                    'url' => $this->getNotificationUrl($notification),
                ];
            });

        return response()->json([
            'notifications' => $notifications,
            'unread_count' => $user->unreadNotifications()->count(),
        ]);
    }

    /**
     * Get notification title based on type.
     */
    private function getNotificationTitle($notification)
    {
        switch ($notification->type) {
            case 'App\Notifications\NewMessage':
                return 'Nova Mensagem';
            case 'App\Notifications\NewOffer':
                return 'Nova Oferta';
            case 'App\Notifications\AdvertisementExpired':
                return 'Anúncio Expirado';
            case 'App\Notifications\AdvertisementApproved':
                return 'Anúncio Aprovado';
            case 'App\Notifications\AdvertisementRejected':
                return 'Anúncio Rejeitado';
            case 'App\Notifications\FavoriteAdvertisementUpdated':
                return 'Favorito Atualizado';
            default:
                return 'Notificação';
        }
    }

    /**
     * Get notification message based on type and data.
     */
    private function getNotificationMessage($notification)
    {
        $data = $notification->data;
        
        switch ($notification->type) {
            case 'App\Notifications\NewMessage':
                return "Nova mensagem de {$data['sender_name']}";
            case 'App\Notifications\NewOffer':
                return "Nova oferta de R$ {$data['amount']} para {$data['advertisement_title']}";
            case 'App\Notifications\AdvertisementExpired':
                return "Seu anúncio '{$data['advertisement_title']}' expirou";
            case 'App\Notifications\AdvertisementApproved':
                return "Seu anúncio '{$data['advertisement_title']}' foi aprovado";
            case 'App\Notifications\AdvertisementRejected':
                return "Seu anúncio '{$data['advertisement_title']}' foi rejeitado";
            case 'App\Notifications\FavoriteAdvertisementUpdated':
                return "Um dos seus favoritos foi atualizado: {$data['advertisement_title']}";
            default:
                return $data['message'] ?? 'Você tem uma nova notificação';
        }
    }

    /**
     * Get notification icon based on type.
     */
    private function getNotificationIcon($notification)
    {
        switch ($notification->type) {
            case 'App\Notifications\NewMessage':
                return 'MessageCircle';
            case 'App\Notifications\NewOffer':
                return 'DollarSign';
            case 'App\Notifications\AdvertisementExpired':
                return 'Clock';
            case 'App\Notifications\AdvertisementApproved':
                return 'CheckCircle';
            case 'App\Notifications\AdvertisementRejected':
                return 'XCircle';
            case 'App\Notifications\FavoriteAdvertisementUpdated':
                return 'Heart';
            default:
                return 'Bell';
        }
    }

    /**
     * Get notification URL based on type and data.
     */
    private function getNotificationUrl($notification)
    {
        $data = $notification->data;
        
        switch ($notification->type) {
            case 'App\Notifications\NewMessage':
                return "/minha-conta/chat/{$data['chat_id']}";
            case 'App\Notifications\NewOffer':
                return "/minha-conta/ofertas/{$data['offer_id']}";
            case 'App\Notifications\AdvertisementExpired':
            case 'App\Notifications\AdvertisementApproved':
            case 'App\Notifications\AdvertisementRejected':
                return "/minha-conta/anuncios/{$data['advertisement_id']}";
            case 'App\Notifications\FavoriteAdvertisementUpdated':
                return "/anuncios/{$data['advertisement_slug']}";
            default:
                return '/minha-conta/notificacoes';
        }
    }

    /**
     * Update notification preferences.
     */
    public function updatePreferences(Request $request)
    {
        $request->validate([
            'email_notifications' => 'boolean',
            'sms_notifications' => 'boolean',
            'marketing_emails' => 'boolean',
            'new_message_email' => 'boolean',
            'new_offer_email' => 'boolean',
            'advertisement_updates' => 'boolean',
            'favorite_updates' => 'boolean',
        ]);

        $user = Auth::user();
        
        $preferences = $request->only([
            'email_notifications',
            'sms_notifications',
            'marketing_emails',
            'new_message_email',
            'new_offer_email',
            'advertisement_updates',
            'favorite_updates',
        ]);

        $user->update([
            'notification_preferences' => $preferences,
        ]);

        return response()->json(['success' => true]);
    }
}
