import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ProductImage } from '@/components/ui/responsive-image';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select';
import { Head, Link, router } from '@inertiajs/react';
import {
    ArrowUpDown,
    CheckCircle,
    Clock,
    DollarSign,
    Eye,
    MessageCircle,
    TrendingDown,
    TrendingUp,
    XCircle,
} from 'lucide-react';

interface Offer {
    id: number;
    amount: number;
    message?: string;
    status: 'pending' | 'accepted' | 'rejected' | 'cancelled' | 'countered';
    created_at: string;
    responded_at?: string;
    rejection_reason?: string;
    user: {
        id: number;
        name: string;
        avatar?: string;
    };
    advertisement: {
        id: number;
        title: string;
        price: number;
        slug: string;
        images: any[];
        user: {
            id: number;
            name: string;
        };
    };
    formatted_amount: string;
    status_label: string;
    status_color: string;
    price_percentage: number;
}

interface PaginatedOffers {
    data: Offer[];
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
    from: number;
    to: number;
}

interface Stats {
    total: number;
    pending: number;
    accepted: number;
    rejected: number;
    received_total: number;
    received_pending: number;
}

interface Props {
    offers: PaginatedOffers;
    stats: Stats;
    filters: {
        type?: string;
        status?: string;
    };
}

export default function OffersIndex({ offers, stats, filters }: Props) {
    const handleFilterChange = (key: string, value: string) => {
        router.get(
            '/minha-conta/ofertas',
            { ...filters, [key]: value },
            { preserveState: true },
        );
    };

    const getStatusBadge = (status: string, color: string) => {
        const variants = {
            orange: 'default' as const,
            green: 'default' as const,
            red: 'destructive' as const,
            gray: 'secondary' as const,
            blue: 'default' as const,
        };

        return (
            <Badge
                variant={
                    variants[color as keyof typeof variants] || 'secondary'
                }
            >
                {status}
            </Badge>
        );
    };

    const getStatusIcon = (status: string) => {
        switch (status) {
            case 'pending':
                return <Clock className="h-4 w-4 text-orange-500" />;
            case 'accepted':
                return <CheckCircle className="h-4 w-4 text-green-500" />;
            case 'rejected':
                return <XCircle className="h-4 w-4 text-red-500" />;
            case 'cancelled':
                return <XCircle className="h-4 w-4 text-gray-500" />;
            case 'countered':
                return <ArrowUpDown className="h-4 w-4 text-blue-500" />;
            default:
                return <Clock className="h-4 w-4 text-gray-500" />;
        }
    };

    const formatPrice = (price: number) => {
        return new Intl.NumberFormat('pt-BR', {
            style: 'currency',
            currency: 'BRL',
        }).format(price);
    };

    const formatPercentage = (percentage: number) => {
        const isPositive = percentage >= 0;
        return (
            <span
                className={`flex items-center gap-1 ${isPositive ? 'text-green-600' : 'text-red-600'}`}
            >
                {isPositive ? (
                    <TrendingUp className="h-3 w-3" />
                ) : (
                    <TrendingDown className="h-3 w-3" />
                )}
                {Math.abs(percentage).toFixed(1)}%
            </span>
        );
    };

    return (
        <>
            <Head title="Minhas Ofertas" />

            <div className="space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold tracking-tight">
                            Minhas Ofertas
                        </h1>
                        <p className="text-muted-foreground">
                            Gerencie suas ofertas enviadas e recebidas
                        </p>
                    </div>
                </div>

                {/* Stats Cards */}
                <div className="grid gap-4 md:grid-cols-3 lg:grid-cols-6">
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">
                                Enviadas
                            </CardTitle>
                            <DollarSign className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">
                                {stats.total}
                            </div>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">
                                Pendentes
                            </CardTitle>
                            <Clock className="h-4 w-4 text-orange-500" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-orange-600">
                                {stats.pending}
                            </div>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">
                                Aceitas
                            </CardTitle>
                            <CheckCircle className="h-4 w-4 text-green-500" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-green-600">
                                {stats.accepted}
                            </div>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">
                                Rejeitadas
                            </CardTitle>
                            <XCircle className="h-4 w-4 text-red-500" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-red-600">
                                {stats.rejected}
                            </div>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">
                                Recebidas
                            </CardTitle>
                            <DollarSign className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">
                                {stats.received_total}
                            </div>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">
                                Aguardando
                            </CardTitle>
                            <Clock className="h-4 w-4 text-orange-500" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-orange-600">
                                {stats.received_pending}
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Filters */}
                <Card>
                    <CardHeader>
                        <CardTitle>Filtros</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="grid gap-4 md:grid-cols-2">
                            <Select
                                value={filters.type || ''}
                                onValueChange={(value) =>
                                    handleFilterChange('type', value)
                                }
                            >
                                <SelectTrigger>
                                    <SelectValue placeholder="Tipo de oferta" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">Todas</SelectItem>
                                    <SelectItem value="sent">
                                        Enviadas por mim
                                    </SelectItem>
                                    <SelectItem value="received">
                                        Recebidas por mim
                                    </SelectItem>
                                </SelectContent>
                            </Select>

                            <Select
                                value={filters.status || ''}
                                onValueChange={(value) =>
                                    handleFilterChange('status', value)
                                }
                            >
                                <SelectTrigger>
                                    <SelectValue placeholder="Status" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">
                                        Todos os status
                                    </SelectItem>
                                    <SelectItem value="pending">
                                        Pendente
                                    </SelectItem>
                                    <SelectItem value="accepted">
                                        Aceita
                                    </SelectItem>
                                    <SelectItem value="rejected">
                                        Rejeitada
                                    </SelectItem>
                                    <SelectItem value="cancelled">
                                        Cancelada
                                    </SelectItem>
                                    <SelectItem value="countered">
                                        Contra-oferta
                                    </SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                    </CardContent>
                </Card>

                {/* Offers List */}
                <Card>
                    <CardHeader>
                        <CardTitle>
                            Ofertas ({offers.from} - {offers.to} de{' '}
                            {offers.total})
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        {offers.data.length === 0 ? (
                            <div className="py-12 text-center">
                                <DollarSign className="mx-auto mb-4 h-12 w-12 text-muted-foreground" />
                                <h3 className="mb-2 text-lg font-medium">
                                    Nenhuma oferta encontrada
                                </h3>
                                <p className="text-muted-foreground">
                                    Você ainda não fez ou recebeu ofertas.
                                </p>
                            </div>
                        ) : (
                            <div className="space-y-4">
                                {offers.data.map((offer) => (
                                    <div
                                        key={offer.id}
                                        className="flex items-center gap-4 rounded-lg border p-4 transition-colors hover:bg-muted/50"
                                    >
                                        <ProductImage
                                            src={
                                                offer.advertisement.images[0]
                                                    ?.url
                                            }
                                            alt={offer.advertisement.title}
                                            className="h-16 w-16 rounded"
                                            featured={false}
                                        />

                                        <div className="min-w-0 flex-1">
                                            <div className="mb-2 flex items-start justify-between">
                                                <div>
                                                    <h4 className="truncate font-medium">
                                                        {
                                                            offer.advertisement
                                                                .title
                                                        }
                                                    </h4>
                                                    <p className="text-sm text-muted-foreground">
                                                        Vendedor:{' '}
                                                        {
                                                            offer.advertisement
                                                                .user.name
                                                        }
                                                    </p>
                                                </div>
                                                <div className="flex items-center gap-2">
                                                    {getStatusIcon(
                                                        offer.status,
                                                    )}
                                                    {getStatusBadge(
                                                        offer.status_label,
                                                        offer.status_color,
                                                    )}
                                                </div>
                                            </div>

                                            <div className="grid grid-cols-2 gap-4 text-sm md:grid-cols-4">
                                                <div>
                                                    <span className="text-muted-foreground">
                                                        Preço original:
                                                    </span>
                                                    <p className="font-medium">
                                                        {formatPrice(
                                                            offer.advertisement
                                                                .price,
                                                        )}
                                                    </p>
                                                </div>
                                                <div>
                                                    <span className="text-muted-foreground">
                                                        Sua oferta:
                                                    </span>
                                                    <p className="font-medium text-primary">
                                                        {offer.formatted_amount}
                                                    </p>
                                                </div>
                                                <div>
                                                    <span className="text-muted-foreground">
                                                        Diferença:
                                                    </span>
                                                    <p>
                                                        {formatPercentage(
                                                            offer.price_percentage,
                                                        )}
                                                    </p>
                                                </div>
                                                <div>
                                                    <span className="text-muted-foreground">
                                                        Data:
                                                    </span>
                                                    <p>
                                                        {new Date(
                                                            offer.created_at,
                                                        ).toLocaleDateString()}
                                                    </p>
                                                </div>
                                            </div>

                                            {offer.message && (
                                                <div className="mt-2 rounded bg-muted p-2 text-sm">
                                                    <span className="text-muted-foreground">
                                                        Mensagem:
                                                    </span>
                                                    <p className="mt-1">
                                                        {offer.message}
                                                    </p>
                                                </div>
                                            )}

                                            {offer.rejection_reason && (
                                                <div className="mt-2 rounded border border-red-200 bg-red-50 p-2 text-sm">
                                                    <span className="font-medium text-red-700">
                                                        Motivo da rejeição:
                                                    </span>
                                                    <p className="mt-1 text-red-600">
                                                        {offer.rejection_reason}
                                                    </p>
                                                </div>
                                            )}
                                        </div>

                                        <div className="flex flex-col gap-2">
                                            <Button
                                                size="sm"
                                                variant="outline"
                                                asChild
                                            >
                                                <Link
                                                    href={`/minha-conta/ofertas/${offer.id}`}
                                                >
                                                    <Eye className="mr-1 h-4 w-4" />
                                                    Ver
                                                </Link>
                                            </Button>
                                            <Button
                                                size="sm"
                                                variant="outline"
                                                asChild
                                            >
                                                <Link
                                                    href={`/anuncios/${offer.advertisement.slug}`}
                                                >
                                                    Ver Anúncio
                                                </Link>
                                            </Button>
                                            {offer.status === 'accepted' && (
                                                <Button size="sm" asChild>
                                                    <Link
                                                        href={`/minha-conta/chat?advertisement=${offer.advertisement.id}`}
                                                    >
                                                        <MessageCircle className="mr-1 h-4 w-4" />
                                                        Chat
                                                    </Link>
                                                </Button>
                                            )}
                                        </div>
                                    </div>
                                ))}
                            </div>
                        )}

                        {/* Pagination */}
                        {offers.last_page > 1 && (
                            <div className="mt-6 flex items-center justify-center gap-2">
                                {offers.current_page > 1 && (
                                    <Button
                                        variant="outline"
                                        onClick={() =>
                                            router.get('/minha-conta/ofertas', {
                                                ...filters,
                                                page: offers.current_page - 1,
                                            })
                                        }
                                    >
                                        Anterior
                                    </Button>
                                )}

                                <span className="text-sm text-muted-foreground">
                                    Página {offers.current_page} de{' '}
                                    {offers.last_page}
                                </span>

                                {offers.current_page < offers.last_page && (
                                    <Button
                                        variant="outline"
                                        onClick={() =>
                                            router.get('/minha-conta/ofertas', {
                                                ...filters,
                                                page: offers.current_page + 1,
                                            })
                                        }
                                    >
                                        Próxima
                                    </Button>
                                )}
                            </div>
                        )}
                    </CardContent>
                </Card>
            </div>
        </>
    );
}
