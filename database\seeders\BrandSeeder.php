<?php

namespace Database\Seeders;

use App\Models\Brand;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class BrandSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $brands = [
            // Marcas de Carros
            [
                'name' => 'Volkswagen',
                'slug' => 'volkswagen',
                'description' => 'Marca alemã conhecida por carros populares e de alta qualidade',
                'website' => 'https://www.vw.com.br',
                'country' => 'DE',
                'founded_year' => 1937,
                'order' => 1,
            ],
            [
                'name' => 'Chevrolet',
                'slug' => 'chevrolet',
                'description' => 'Marca americana com presença forte no Brasil',
                'website' => 'https://www.chevrolet.com.br',
                'country' => 'US',
                'founded_year' => 1911,
                'order' => 2,
            ],
            [
                'name' => 'Ford',
                'slug' => 'ford',
                'description' => 'Marca americana pioneira na indústria automotiva',
                'website' => 'https://www.ford.com.br',
                'country' => 'US',
                'founded_year' => 1903,
                'order' => 3,
            ],
            [
                'name' => 'Toyota',
                'slug' => 'toyota',
                'description' => 'Marca japonesa líder em confiabilidade e eficiência',
                'website' => 'https://www.toyota.com.br',
                'country' => 'JP',
                'founded_year' => 1937,
                'order' => 4,
            ],
            [
                'name' => 'Honda',
                'slug' => 'honda',
                'description' => 'Marca japonesa conhecida por tecnologia e inovação',
                'website' => 'https://www.honda.com.br',
                'country' => 'JP',
                'founded_year' => 1948,
                'order' => 5,
            ],
            [
                'name' => 'Hyundai',
                'slug' => 'hyundai',
                'description' => 'Marca coreana com design moderno e tecnologia avançada',
                'website' => 'https://www.hyundai.com.br',
                'country' => 'KR',
                'founded_year' => 1967,
                'order' => 6,
            ],
            [
                'name' => 'Fiat',
                'slug' => 'fiat',
                'description' => 'Marca italiana popular no Brasil por carros compactos',
                'website' => 'https://www.fiat.com.br',
                'country' => 'IT',
                'founded_year' => 1899,
                'order' => 7,
            ],
            [
                'name' => 'Renault',
                'slug' => 'renault',
                'description' => 'Marca francesa com foco em segurança e conforto',
                'website' => 'https://www.renault.com.br',
                'country' => 'FR',
                'founded_year' => 1899,
                'order' => 8,
            ],
            [
                'name' => 'Nissan',
                'slug' => 'nissan',
                'description' => 'Marca japonesa com inovação e tecnologia',
                'website' => 'https://www.nissan.com.br',
                'country' => 'JP',
                'founded_year' => 1933,
                'order' => 9,
            ],
            [
                'name' => 'Jeep',
                'slug' => 'jeep',
                'description' => 'Marca americana especializada em SUVs e off-road',
                'website' => 'https://www.jeep.com.br',
                'country' => 'US',
                'founded_year' => 1941,
                'order' => 10,
            ],
            
            // Marcas de Motos
            [
                'name' => 'Honda Motos',
                'slug' => 'honda-motos',
                'description' => 'Líder mundial em fabricação de motocicletas',
                'website' => 'https://www.honda.com.br/motos',
                'country' => 'JP',
                'founded_year' => 1948,
                'order' => 11,
            ],
            [
                'name' => 'Yamaha',
                'slug' => 'yamaha',
                'description' => 'Marca japonesa conhecida por desempenho e esportividade',
                'website' => 'https://www.yamahamotos.com.br',
                'country' => 'JP',
                'founded_year' => 1955,
                'order' => 12,
            ],
            [
                'name' => 'Suzuki',
                'slug' => 'suzuki',
                'description' => 'Marca japonesa com motos leves e eficientes',
                'website' => 'https://www.suzuki.com.br',
                'country' => 'JP',
                'founded_year' => 1909,
                'order' => 13,
            ],
            [
                'name' => 'Kawasaki',
                'slug' => 'kawasaki',
                'description' => 'Marca japonesa focada em motos esportivas e de alta performance',
                'website' => 'https://www.kawasaki.com.br',
                'country' => 'JP',
                'founded_year' => 1896,
                'order' => 14,
            ],
            [
                'name' => 'BMW Motorrad',
                'slug' => 'bmw-motorrad',
                'description' => 'Marca alemã de motos premium e touring',
                'website' => 'https://www.bmw-motorrad.com.br',
                'country' => 'DE',
                'founded_year' => 1923,
                'order' => 15,
            ],
            [
                'name' => 'Harley-Davidson',
                'slug' => 'harley-davidson',
                'description' => 'Marca americana icônica de motos custom',
                'website' => 'https://www.harley-davidson.com.br',
                'country' => 'US',
                'founded_year' => 1903,
                'order' => 16,
            ],
            [
                'name' => 'Dafra',
                'slug' => 'dafra',
                'description' => 'Marca brasileira popular por motos econômicas',
                'website' => 'https://www.dafra.com.br',
                'country' => 'BR',
                'founded_year' => 1998,
                'order' => 17,
            ],
            
            // Marcas de Peças
            [
                'name' => 'Bosch',
                'slug' => 'bosch',
                'description' => 'Líder mundial em autopeças e tecnologia automotiva',
                'website' => 'https://www.bosch.com.br',
                'country' => 'DE',
                'founded_year' => 1886,
                'order' => 18,
            ],
            [
                'name' => 'Mahle',
                'slug' => 'mahle',
                'description' => 'Especialista em sistemas de motor e filtragem',
                'website' => 'https://www.mahle.com',
                'country' => 'DE',
                'founded_year' => 1920,
                'order' => 19,
            ],
            [
                'name' => 'Valeo',
                'slug' => 'valeo',
                'description' => 'Fabricante de componentes automotivos e sistemas elétricos',
                'website' => 'https://www.valeo.com',
                'country' => 'FR',
                'founded_year' => 1923,
                'order' => 20,
            ],
            [
                'name' => 'NGK',
                'slug' => 'ngk',
                'description' => 'Especialista em velas de ignição e sensores',
                'website' => 'https://www.ngkntk.com.br',
                'country' => 'JP',
                'founded_year' => 1936,
                'order' => 21,
            ],
            [
                'name' => 'Mann+Hummel',
                'slug' => 'mann-hummel',
                'description' => 'Líder em filtros e sistemas de filtragem',
                'website' => 'https://www.mann-hummel.com',
                'country' => 'DE',
                'founded_year' => 1941,
                'order' => 22,
            ],
            [
                'name' => 'Continental',
                'slug' => 'continental',
                'description' => 'Tecnologia automotiva e pneus de alta performance',
                'website' => 'https://www.continental.com.br',
                'country' => 'DE',
                'founded_year' => 1871,
                'order' => 23,
            ],
            [
                'name' => 'Delphi',
                'slug' => 'delphi',
                'description' => 'Peças eletrônicas e sistemas de injeção',
                'website' => 'https://www.delphi.com',
                'country' => 'US',
                'founded_year' => 1994,
                'order' => 24,
            ],
        ];

        foreach ($brands as $brand) {
            Brand::firstOrCreate(
                ['slug' => $brand['slug']],
                $brand
            );
        }
    }
}
