<?php

namespace Database\Seeders;

use App\Models\Vehicle;
use App\Models\Category;
use App\Models\Brand;
use App\Models\User;
use App\Models\Image;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;

class VehicleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Limpar a tabela de veículos (sem foreign key checks para SQLite)
        Vehicle::query()->delete();

        // Obter categorias, marcas e usuários
        $carCategory = Category::where('slug', 'carros')->first();
        $motorcycleCategory = Category::where('slug', 'motos')->first();
        $users = User::take(10)->get();
        
        if (!$carCategory || !$motorcycleCategory || $users->isEmpty()) {
            $this->command->error('É necessário executar os seeders de Category e User primeiro!');
            return;
        }

        // Marcas de carros
        $carBrands = Brand::whereIn('slug', [
            'volkswagen', 'chevrolet', 'ford', 'toyota', 'honda', 
            'hyundai', 'fiat', 'renault', 'nissan', 'jeep'
        ])->get();

        // Marcas de motos
        $motorcycleBrands = Brand::whereIn('slug', [
            'honda-motos', 'yamaha', 'suzuki', 'kawasaki', 
            'bmw-motorrad', 'harley-davidson', 'dafra'
        ])->get();

        // Dados de veículos para seed
        $vehicles = [
            // Carros
            [
                'brand_slug' => 'volkswagen',
                'model' => 'Golf GTI',
                'year_manufacture' => 2022,
                'model_year' => 2023,
                'price' => 185000.00,
                'promotional_price' => 175000.00,
                'color' => 'Preto',
                'fuel_type' => 'Gasolina',
                'transmission' => 'Automático',
                'mileage' => 15000,
                'is_featured' => true,
                'is_negotiable' => false,
                'description' => 'Golf GTI em excelente estado, único dono, todos os opcionais. Motor 2.0 TSI com 230cv, câmbio DSG, teto solar, bancos em couro, multimídia com Apple CarPlay/Android Auto.',
                'status' => 'published',
                'views' => rand(50, 500),
            ],
            [
                'brand_slug' => 'toyota',
                'model' => 'Corolla XEI',
                'year_manufacture' => 2023,
                'model_year' => 2023,
                'price' => 145000.00,
                'color' => 'Prata',
                'fuel_type' => 'Híbrido',
                'transmission' => 'Automático CVT',
                'mileage' => 8000,
                'is_featured' => true,
                'is_negotiable' => true,
                'description' => 'Corolla híbrido 2023, economia excepcional e conforto premium. Completo com multimídia, câmera de ré, sensores de estacionamento, controle de cruzeiro adaptativo.',
                'status' => 'published',
                'views' => rand(30, 300),
            ],
            [
                'brand_slug' => 'honda',
                'model' => 'Civic EXL',
                'year_manufacture' => 2022,
                'model_year' => 2022,
                'price' => 165000.00,
                'promotional_price' => 155000.00,
                'color' => 'Branco',
                'fuel_type' => 'Gasolina',
                'transmission' => 'Automático',
                'mileage' => 25000,
                'is_featured' => true,
                'is_negotiable' => false,
                'description' => 'Civic Touring 2022, topo de linha com motor 1.5 Turbo. Bancos em couro, sistema de som premium, assistente de permanência em faixa, freio automático de emergência.',
                'status' => 'published',
                'views' => rand(40, 400),
            ],
            [
                'brand_slug' => 'chevrolet',
                'model' => 'Onix Premier',
                'year_manufacture' => 2023,
                'model_year' => 2023,
                'price' => 89000.00,
                'color' => 'Vermelho',
                'fuel_type' => 'Flex',
                'transmission' => 'Automático',
                'mileage' => 5000,
                'is_featured' => false,
                'is_negotiable' => true,
                'description' => 'Onix Premier 2023, econômico e bem equipado. Motor 1.0 Turbo, multimídia MyLink, ar condicionado digital, direção elétrica, vidros e travas elétricas.',
                'status' => 'published',
                'views' => rand(20, 200),
            ],
            [
                'brand_slug' => 'hyundai',
                'model' => 'HB20 Diamond',
                'year_manufacture' => 2023,
                'model_year' => 2023,
                'price' => 95000.00,
                'promotional_price' => 89000.00,
                'color' => 'Azul',
                'fuel_type' => 'Flex',
                'transmission' => 'Automático',
                'mileage' => 12000,
                'is_featured' => false,
                'is_negotiable' => false,
                'description' => 'HB20 Diamond 2023, design moderno e tecnologia avançada. Motor 1.6, central multimídia com tela de 10,25", câmera de ré, sensores de estacionamento.',
                'status' => 'published',
                'views' => rand(25, 250),
            ],
            [
                'brand_slug' => 'jeep',
                'model' => 'Renegade Trailhawk',
                'year_manufacture' => 2022,
                'model_year' => 2022,
                'price' => 195000.00,
                'color' => 'Verde',
                'fuel_type' => 'Diesel',
                'transmission' => 'Automático 9 marchas',
                'mileage' => 35000,
                'is_featured' => true,
                'is_negotiable' => false,
                'description' => 'Renegade Trailhawk 2022, versão off-road com tração 4x4. Motor 2.0 Turbo Diesel, suspensão elevada, proteções submotor, modo off-road avançado.',
                'status' => 'published',
                'views' => rand(60, 600),
            ],
            [
                'brand_slug' => 'nissan',
                'model' => 'Kicks SL',
                'year_manufacture' => 2023,
                'model_year' => 2023,
                'price' => 115000.00,
                'color' => 'Cinza',
                'fuel_type' => 'Flex',
                'transmission' => 'Automático CVT',
                'mileage' => 15000,
                'is_featured' => false,
                'is_negotiable' => true,
                'description' => 'Kicks SL 2023, SUV compacto com design esportivo. Motor 1.6 16V, central multimídia, ar condicionado digital, controle de tração.',
                'status' => 'published',
                'views' => rand(30, 300),
            ],
            [
                'brand_slug' => 'fiat',
                'model' => 'Pulse Audace',
                'year_manufacture' => 2023,
                'model_year' => 2023,
                'price' => 105000.00,
                'promotional_price' => 99000.00,
                'color' => 'Branco',
                'fuel_type' => 'Flex',
                'transmission' => 'Automático',
                'mileage' => 8000,
                'is_featured' => false,
                'is_negotiable' => false,
                'description' => 'Pulse Audace 2023, SUV compacto com design italiano. Motor 1.3 Turbo, central multimídia com tela de 10,1", ar condicionado digital, direção elétrica.',
                'status' => 'published',
                'views' => rand(25, 250),
            ],
            [
                'brand_slug' => 'ford',
                'model' => 'EcoSport Titanium',
                'year_manufacture' => 2022,
                'model_year' => 2022,
                'price' => 125000.00,
                'color' => 'Preto',
                'fuel_type' => 'Flex',
                'transmission' => 'Automático',
                'mileage' => 45000,
                'is_featured' => false,
                'is_negotiable' => true,
                'description' => 'EcoSport Titanium 2022, SUV compacto com bom espaço interno. Motor 1.5, central multimídia SYNC, ar condicionado digital, teto solar, rodas de liga leve.',
                'status' => 'published',
                'views' => rand(35, 350),
            ],
            [
                'brand_slug' => 'renault',
                'model' => 'Duster Intens',
                'year_manufacture' => 2023,
                'model_year' => 2023,
                'price' => 135000.00,
                'color' => 'Marrom',
                'fuel_type' => 'Flex',
                'transmission' => 'Automático',
                'mileage' => 20000,
                'is_featured' => false,
                'is_negotiable' => false,
                'description' => 'Duster Intens 2023, SUV robusto com bom custo-benefício. Motor 1.6 16V, central multimídia, ar condicionado digital, controle de tração, rodas de liga leve.',
                'status' => 'published',
                'views' => rand(30, 300),
            ],

            // Motos
            [
                'brand_slug' => 'honda-motos',
                'model' => 'CB 650R',
                'year_manufacture' => 2023,
                'model_year' => 2023,
                'price' => 65000.00,
                'promotional_price' => 62000.00,
                'color' => 'Vermelho',
                'fuel_type' => 'Gasolina',
                'transmission' => 'Manual 6 marchas',
                'mileage' => 5000,
                'is_featured' => true,
                'is_negotiable' => false,
                'description' => 'CB 650R 2023, naked esportiva com motor 4 cilindros. Design moderno, freios ABS, controle de tração, modos de condução, painel digital completo.',
                'status' => 'published',
                'views' => rand(40, 400),
            ],
            [
                'brand_slug' => 'yamaha',
                'model' => 'MT-07',
                'year_manufacture' => 2022,
                'model_year' => 2022,
                'price' => 58000.00,
                'color' => 'Azul',
                'fuel_type' => 'Gasolina',
                'transmission' => 'Manual 6 marchas',
                'mileage' => 12000,
                'is_featured' => true,
                'is_negotiable' => true,
                'description' => 'MT-07 2022, naked com motor twin de 689cc. Agilidade excepcional, freios ABS, controle de tração, design agressivo, painel LCD.',
                'status' => 'published',
                'views' => rand(35, 350),
            ],
            [
                'brand_slug' => 'bmw-motorrad',
                'model' => 'R 1250 GS',
                'year_manufacture' => 2023,
                'model_year' => 2023,
                'price' => 95000.00,
                'color' => 'Preto',
                'fuel_type' => 'Gasolina',
                'transmission' => 'Manual 6 marchas',
                'mileage' => 8000,
                'is_featured' => true,
                'is_negotiable' => false,
                'description' => 'R 1250 GS 2023, adventure premium com motor boxer. Tecnologia BMW, freios ABS Pro, controle de tração, modos de condução, suspensão eletrônica.',
                'status' => 'published',
                'views' => rand(50, 500),
            ],
            [
                'brand_slug' => 'harley-davidson',
                'model' => 'Iron 883',
                'year_manufacture' => 2022,
                'model_year' => 2022,
                'price' => 75000.00,
                'promotional_price' => 71000.00,
                'color' => 'Preto',
                'fuel_type' => 'Gasolina',
                'transmission' => 'Manual 6 marchas',
                'mileage' => 15000,
                'is_featured' => true,
                'is_negotiable' => false,
                'description' => 'Iron 883 2022, custom com motor Evolution. Estilo dark custom, motor 883cc, freios ABS, injeção eletrônica, design icônico Harley-Davidson.',
                'status' => 'published',
                'views' => rand(45, 450),
            ],
            [
                'brand_slug' => 'kawasaki',
                'model' => 'Ninja 400',
                'year_manufacture' => 2023,
                'model_year' => 2023,
                'price' => 35000.00,
                'color' => 'Verde',
                'fuel_type' => 'Gasolina',
                'transmission' => 'Manual 6 marchas',
                'mileage' => 3000,
                'is_featured' => false,
                'is_negotiable' => true,
                'description' => 'Ninja 400 2023, esportiva de entrada. Motor 399cc, freios ABS, injeção eletrônica, painel digital, design aerodinâmico.',
                'status' => 'published',
                'views' => rand(30, 300),
            ],
            [
                'brand_slug' => 'suzuki',
                'model' => 'GSX-S 750',
                'year_manufacture' => 2022,
                'model_year' => 2022,
                'price' => 48000.00,
                'color' => 'Branco',
                'fuel_type' => 'Gasolina',
                'transmission' => 'Manual 6 marchas',
                'mileage' => 18000,
                'is_featured' => false,
                'is_negotiable' => true,
                'description' => 'GSX-S 750 2022, naked com motor 4 cilindros. Desempenho equilibrado, freios ABS, controle de tração, painel LCD, design esportivo.',
                'status' => 'published',
                'views' => rand(25, 250),
            ],
            [
                'brand_slug' => 'dafra',
                'model' => 'Speed 150',
                'year_manufacture' => 2023,
                'model_year' => 2023,
                'price' => 12000.00,
                'promotional_price' => 11000.00,
                'color' => 'Vermelho',
                'fuel_type' => 'Gasolina',
                'transmission' => 'Manual 5 marchas',
                'mileage' => 2000,
                'is_featured' => false,
                'is_negotiable' => false,
                'description' => 'Speed 150 2023, esportiva econômica. Motor 150cc, freios ABS, injeção eletrônica, painel digital, design jovem e esportivo.',
                'status' => 'published',
                'views' => rand(20, 200),
            ],
        ];

        // Criar veículos
        foreach ($vehicles as $vehicleData) {
            $brand = $vehicleData['brand_slug'] === 'honda-motos' ? 
                $motorcycleBrands->where('slug', 'honda-motos')->first() :
                ($carBrands->where('slug', $vehicleData['brand_slug'])->first() ?? 
                 $motorcycleBrands->where('slug', $vehicleData['brand_slug'])->first());

            if (!$brand) continue;

            $category = $motorcycleBrands->contains('slug', $vehicleData['brand_slug']) ? 
                $motorcycleCategory : $carCategory;

            $user = $users->random();

            $vehicle = Vehicle::create([
                'user_id' => $user->id,
                'category_id' => $category->id,
                'brand_id' => $brand->id,
                'model' => $vehicleData['model'],
                'slug' => Str::slug($vehicleData['model'] . '-' . rand(1000, 9999)),
                'year_manufacture' => $vehicleData['year_manufacture'],
                'model_year' => $vehicleData['model_year'],
                'license_plate' => $this->generateLicensePlate(),
                'chassis_number' => $this->generateChassisNumber(),
                'color' => $vehicleData['color'],
                'fuel_type' => $vehicleData['fuel_type'],
                'transmission' => $vehicleData['transmission'],
                'mileage' => $vehicleData['mileage'],
                'description' => $vehicleData['description'],
                'price' => $vehicleData['price'],
                'promotional_price' => $vehicleData['promotional_price'] ?? null,
                'is_negotiable' => $vehicleData['is_negotiable'],
                'is_featured' => $vehicleData['is_featured'],
                'status' => $vehicleData['status'],
                'views' => $vehicleData['views'],
                'seo_title' => $vehicleData['model'] . ' ' . $vehicleData['year_manufacture'] . ' - ' . $brand->name,
                'seo_description' => $vehicleData['description'],
                'seo_keywords' => $brand->name . ',' . $vehicleData['model'] . ',' . $category->name . ',usado,seminovo',
            ]);

            // Adicionar imagens (simulação)
            $this->addVehicleImages($vehicle);
        }

        $this->command->info(count($vehicles) . ' veículos criados com sucesso!');
    }

    /**
     * Gerar placa de veículo válida
     */
    private function generateLicensePlate(): string
    {
        $letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $numbers = '**********';
        
        // Formato Mercosul: ABC1D23
        return substr(str_shuffle($letters), 0, 3) . 
               substr(str_shuffle($numbers), 0, 1) . 
               substr(str_shuffle($letters), 0, 1) . 
               substr(str_shuffle($numbers), 0, 2);
    }

    /**
     * Gerar número de chassi
     */
    private function generateChassisNumber(): string
    {
        $chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ**********';
        return substr(str_shuffle(str_repeat($chars, 17)), 0, 17);
    }

    /**
     * Adicionar imagens ao veículo
     */
    private function addVehicleImages(Vehicle $vehicle): void
    {
        // Simular criação de imagens
        $imageCount = rand(3, 8);
        
        for ($i = 0; $i < $imageCount; $i++) {
            Image::create([
                'imageable_type' => Vehicle::class,
                'imageable_id' => $vehicle->id,
                'path' => 'vehicles/' . $vehicle->id . '/image_' . ($i + 1) . '.jpg',
                'url' => 'storage/vehicles/' . $vehicle->id . '/image_' . ($i + 1) . '.jpg',
                'mime_type' => 'image/jpeg',
                'size' => rand(50000, 500000),
                'width' => 800,
                'height' => 600,
                'alt_text' => $vehicle->model . ' - Imagem ' . ($i + 1),
                'title' => $vehicle->model . ' - Foto ' . ($i + 1),
                'is_main' => $i === 0,
                'order' => $i,
            ]);
        }
    }
}
