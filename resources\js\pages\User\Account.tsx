import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import MainLayout from '@/layouts/MainLayout';
import { <PERSON>, <PERSON> } from '@inertiajs/react';
import { 
    User, 
    ShoppingBag, 
    Heart, 
    MessageCircle, 
    Settings, 
    Plus,
    Eye,
    Clock,
    CheckCircle,
    AlertCircle
} from 'lucide-react';

interface Props {
    auth: {
        user: {
            id: number;
            name: string;
            email: string;
            avatar?: string;
            created_at: string;
        };
    };
    stats: {
        total_advertisements: number;
        active_advertisements: number;
        pending_advertisements: number;
        total_favorites: number;
        total_chats: number;
        unread_messages: number;
    };
    recentAdvertisements: Array<{
        id: number;
        title: string;
        price: number;
        status: string;
        views: number;
        created_at: string;
        featured_image_url?: string;
    }>;
}

export default function UserAccount({ auth, stats, recentAdvertisements }: Props) {
    const formatPrice = (price: number) => {
        return new Intl.NumberFormat('pt-BR', {
            style: 'currency',
            currency: 'BRL',
        }).format(price);
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('pt-BR');
    };

    const getStatusBadge = (status: string) => {
        switch (status) {
            case 'published':
                return <Badge className="bg-green-100 text-green-800">Ativo</Badge>;
            case 'pending_review':
                return <Badge className="bg-yellow-100 text-yellow-800">Pendente</Badge>;
            case 'rejected':
                return <Badge className="bg-red-100 text-red-800">Rejeitado</Badge>;
            default:
                return <Badge className="bg-gray-100 text-gray-800">Rascunho</Badge>;
        }
    };

    const getUserInitials = (name: string) => {
        return name
            .split(' ')
            .map(word => word.charAt(0))
            .join('')
            .toUpperCase()
            .slice(0, 2);
    };

    return (
        <MainLayout>
            <Head title="Minha Conta" />
            
            <div className="min-h-screen bg-gray-50 py-8">
                <div className="container mx-auto px-4 max-w-6xl">
                    {/* Header */}
                    <div className="mb-8">
                        <div className="flex items-center space-x-4">
                            <div className="flex h-16 w-16 items-center justify-center rounded-full bg-primary text-white text-xl font-bold">
                                {auth.user.avatar ? (
                                    <img 
                                        src={auth.user.avatar} 
                                        alt={auth.user.name}
                                        className="h-16 w-16 rounded-full object-cover"
                                    />
                                ) : (
                                    getUserInitials(auth.user.name)
                                )}
                            </div>
                            <div>
                                <h1 className="text-3xl font-bold text-gray-900">Olá, {auth.user.name}!</h1>
                                <p className="text-gray-600">Bem-vindo à sua área pessoal</p>
                            </div>
                        </div>
                    </div>

                    {/* Quick Actions */}
                    <div className="mb-8 grid gap-4 md:grid-cols-4">
                        <Button asChild className="h-auto p-4">
                            <Link href="/anunciar" className="flex flex-col items-center space-y-2">
                                <Plus className="h-6 w-6" />
                                <span>Anunciar</span>
                            </Link>
                        </Button>
                        <Button variant="outline" asChild className="h-auto p-4">
                            <Link href="/minha-conta/anuncios" className="flex flex-col items-center space-y-2">
                                <ShoppingBag className="h-6 w-6" />
                                <span>Meus Anúncios</span>
                            </Link>
                        </Button>
                        <Button variant="outline" asChild className="h-auto p-4">
                            <Link href="/minha-conta/favoritos" className="flex flex-col items-center space-y-2">
                                <Heart className="h-6 w-6" />
                                <span>Favoritos</span>
                            </Link>
                        </Button>
                        <Button variant="outline" asChild className="h-auto p-4">
                            <Link href="/minha-conta/chat" className="flex flex-col items-center space-y-2">
                                <MessageCircle className="h-6 w-6" />
                                <span>Mensagens</span>
                            </Link>
                        </Button>
                    </div>

                    <div className="grid gap-6 lg:grid-cols-3">
                        {/* Stats Cards */}
                        <div className="lg:col-span-2 space-y-6">
                            <div className="grid gap-4 md:grid-cols-3">
                                <Card>
                                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                        <CardTitle className="text-sm font-medium">Anúncios</CardTitle>
                                        <ShoppingBag className="h-4 w-4 text-muted-foreground" />
                                    </CardHeader>
                                    <CardContent>
                                        <div className="text-2xl font-bold">{stats.total_advertisements}</div>
                                        <p className="text-xs text-muted-foreground">
                                            {stats.active_advertisements} ativos
                                        </p>
                                    </CardContent>
                                </Card>
                                
                                <Card>
                                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                        <CardTitle className="text-sm font-medium">Favoritos</CardTitle>
                                        <Heart className="h-4 w-4 text-muted-foreground" />
                                    </CardHeader>
                                    <CardContent>
                                        <div className="text-2xl font-bold">{stats.total_favorites}</div>
                                        <p className="text-xs text-muted-foreground">
                                            Itens salvos
                                        </p>
                                    </CardContent>
                                </Card>
                                
                                <Card>
                                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                        <CardTitle className="text-sm font-medium">Mensagens</CardTitle>
                                        <MessageCircle className="h-4 w-4 text-muted-foreground" />
                                    </CardHeader>
                                    <CardContent>
                                        <div className="text-2xl font-bold">{stats.total_chats}</div>
                                        <p className="text-xs text-muted-foreground">
                                            {stats.unread_messages} não lidas
                                        </p>
                                    </CardContent>
                                </Card>
                            </div>

                            {/* Recent Advertisements */}
                            <Card>
                                <CardHeader>
                                    <div className="flex items-center justify-between">
                                        <div>
                                            <CardTitle>Meus Anúncios Recentes</CardTitle>
                                            <CardDescription>
                                                Seus últimos anúncios publicados
                                            </CardDescription>
                                        </div>
                                        <Button variant="outline" asChild>
                                            <Link href="/minha-conta/anuncios">Ver todos</Link>
                                        </Button>
                                    </div>
                                </CardHeader>
                                <CardContent>
                                    {recentAdvertisements.length > 0 ? (
                                        <div className="space-y-4">
                                            {recentAdvertisements.slice(0, 3).map((ad) => (
                                                <div key={ad.id} className="flex items-center space-x-4 p-4 border rounded-lg">
                                                    {ad.featured_image_url && (
                                                        <img 
                                                            src={ad.featured_image_url} 
                                                            alt={ad.title}
                                                            className="h-16 w-16 rounded-lg object-cover"
                                                        />
                                                    )}
                                                    <div className="flex-1 min-w-0">
                                                        <h4 className="font-medium truncate">{ad.title}</h4>
                                                        <p className="text-sm text-gray-600">{formatPrice(ad.price)}</p>
                                                        <div className="flex items-center space-x-2 mt-1">
                                                            {getStatusBadge(ad.status)}
                                                            <span className="text-xs text-gray-500 flex items-center">
                                                                <Eye className="h-3 w-3 mr-1" />
                                                                {ad.views} visualizações
                                                            </span>
                                                        </div>
                                                    </div>
                                                    <div className="text-right">
                                                        <p className="text-xs text-gray-500">{formatDate(ad.created_at)}</p>
                                                    </div>
                                                </div>
                                            ))}
                                        </div>
                                    ) : (
                                        <div className="text-center py-8">
                                            <ShoppingBag className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                                            <p className="text-gray-600 mb-4">Você ainda não tem anúncios</p>
                                            <Button asChild>
                                                <Link href="/anunciar">Criar primeiro anúncio</Link>
                                            </Button>
                                        </div>
                                    )}
                                </CardContent>
                            </Card>
                        </div>

                        {/* Sidebar */}
                        <div className="space-y-6">
                            <Card>
                                <CardHeader>
                                    <CardTitle>Configurações</CardTitle>
                                    <CardDescription>
                                        Gerencie sua conta
                                    </CardDescription>
                                </CardHeader>
                                <CardContent className="space-y-2">
                                    <Button variant="ghost" asChild className="w-full justify-start">
                                        <Link href="/minha-conta/perfil/editar">
                                            <User className="h-4 w-4 mr-2" />
                                            Editar perfil
                                        </Link>
                                    </Button>
                                    <Button variant="ghost" asChild className="w-full justify-start">
                                        <Link href="/minha-conta/configuracoes">
                                            <Settings className="h-4 w-4 mr-2" />
                                            Configurações
                                        </Link>
                                    </Button>
                                </CardContent>
                            </Card>

                            <Card>
                                <CardHeader>
                                    <CardTitle>Membro desde</CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <p className="text-2xl font-bold">{formatDate(auth.user.created_at)}</p>
                                    <p className="text-sm text-gray-600">Data de cadastro</p>
                                </CardContent>
                            </Card>
                        </div>
                    </div>
                </div>
            </div>
        </MainLayout>
    );
}
