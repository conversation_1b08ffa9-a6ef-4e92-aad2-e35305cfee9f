import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
    <PERSON><PERSON>,
    DialogContent,
    DialogHeader,
    <PERSON><PERSON>Title,
    DialogTrigger,
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import ProductCarousel from '@/components/ui/ProductCarousel';
import { Textarea } from '@/components/ui/textarea';
import MainLayout from '@/layouts/MainLayout';
import { PageProps } from '@/types';
import { Head, Link, router, useForm } from '@inertiajs/react';
import {
    Calendar,
    Eye,
    Flag,
    Fuel,
    Gauge,
    Heart,
    MapPin,
    MessageCircle,
    Phone,
    Settings,
    Share2,
    User,
} from 'lucide-react';
import { useState } from 'react';

interface Advertisement {
    id: number;
    title: string;
    description: string;
    price: number;
    location: string;
    contact_phone: string;
    created_at: string;
    views_count: number;
    is_featured: boolean;
    featured_image?: {
        url: string;
        alt: string;
    };
    images: Array<{
        id: number;
        url: string;
        alt: string;
    }>;
    vehicle: {
        id: number;
        model: string;
        year: number;
        mileage: number;
        fuel_type: string;
        transmission: string;
        condition: string;
        color: string;
        doors: number;
        engine_size: string;
        brand: {
            id: number;
            name: string;
        };
        category: {
            id: number;
            name: string;
        };
        features: Array<{
            id: number;
            name: string;
            value: string;
        }>;
    };
    user: {
        id: number;
        name: string;
        avatar?: string;
        created_at: string;
    };
}

interface AdvertisementShowProps extends PageProps {
    advertisement: Advertisement;
    relatedAds: Advertisement[];
    sellerAds: Advertisement[];
}

export default function AdvertisementShow({
    advertisement,
    relatedAds,
    sellerAds,
}: AdvertisementShowProps) {
    const { auth } = usePage<PageProps>().props;
    const [selectedImage, setSelectedImage] = useState(0);
    const [showContactForm, setShowContactForm] = useState(false);
    const [showReportForm, setShowReportForm] = useState(false);

    const {
        data: contactData,
        setData: setContactData,
        post: postContact,
        processing: contactProcessing,
    } = useForm({
        name: '',
        email: '',
        phone: '',
        message: `Olá! Tenho interesse no ${advertisement.vehicle.brand.name} ${advertisement.vehicle.model} ${advertisement.vehicle.year}. Poderia me fornecer mais informações?`,
    });

    const {
        data: reportData,
        setData: setReportData,
        post: postReport,
        processing: reportProcessing,
    } = useForm({
        reason: '',
        description: '',
    });

    const formatPrice = (price: number) => {
        return new Intl.NumberFormat('pt-BR', {
            style: 'currency',
            currency: 'BRL',
        }).format(price);
    };

    const formatMileage = (mileage: number) => {
        return new Intl.NumberFormat('pt-BR').format(mileage) + ' km';
    };

    const formatDate = (date: string) => {
        return new Date(date).toLocaleDateString('pt-BR');
    };

    const handleContact = (e: React.FormEvent) => {
        e.preventDefault();

        // Check if user is authenticated
        if (!auth?.user) {
            router.get('/login');
            return;
        }

        // Start a chat instead of just sending an email
        postContact(`/minha-conta/chat/start/${advertisement.id}`, {
            onSuccess: (response: any) => {
                setShowContactForm(false);
                // Redirect to the chat
                if (response.chat_id) {
                    router.get(`/minha-conta/chat/${response.chat_id}`);
                }
            },
        });
    };

    const handleReport = (e: React.FormEvent) => {
        e.preventDefault();
        postReport(`/anuncios/${advertisement.id}/denunciar`, {
            onSuccess: () => {
                setShowReportForm(false);
                // Show success message
            },
        });
    };

    const handleFavorite = () => {
        router.post(`/anuncios/${advertisement.id}/favoritar`);
    };

    const handleShare = () => {
        if (navigator.share) {
            navigator.share({
                title: advertisement.title,
                text: `Confira este ${advertisement.vehicle.brand.name} ${advertisement.vehicle.model}`,
                url: window.location.href,
            });
        } else {
            navigator.clipboard.writeText(window.location.href);
            // Show copied message
        }
    };

    const allImages = advertisement.featured_image
        ? [advertisement.featured_image, ...advertisement.images]
        : advertisement.images;

    return (
        <MainLayout>
            <Head title={advertisement.title} />

            <div className="min-h-screen bg-gray-50">
                <div className="container mx-auto px-4 py-6">
                    <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
                        {/* Coluna principal */}
                        <div className="space-y-6 lg:col-span-2">
                            {/* Galeria de imagens */}
                            <Card>
                                <CardContent className="p-0">
                                    {allImages.length > 0 ? (
                                        <div>
                                            {/* Imagem principal */}
                                            <div className="relative aspect-video bg-gray-200">
                                                <img
                                                    src={
                                                        allImages[selectedImage]
                                                            ?.url
                                                    }
                                                    alt={
                                                        allImages[selectedImage]
                                                            ?.alt
                                                    }
                                                    className="h-full w-full object-cover"
                                                />
                                                {advertisement.is_featured && (
                                                    <Badge className="absolute top-4 left-4 bg-yellow-500">
                                                        Destaque
                                                    </Badge>
                                                )}
                                            </div>

                                            {/* Miniaturas */}
                                            {allImages.length > 1 && (
                                                <div className="p-4">
                                                    <div className="flex gap-2 overflow-x-auto">
                                                        {allImages.map(
                                                            (image, index) => (
                                                                <button
                                                                    key={index}
                                                                    onClick={() =>
                                                                        setSelectedImage(
                                                                            index,
                                                                        )
                                                                    }
                                                                    className={`h-20 w-20 flex-shrink-0 overflow-hidden rounded-md border-2 ${
                                                                        selectedImage ===
                                                                        index
                                                                            ? 'border-blue-500'
                                                                            : 'border-gray-200'
                                                                    }`}
                                                                >
                                                                    <img
                                                                        src={
                                                                            image.url
                                                                        }
                                                                        alt={
                                                                            image.alt
                                                                        }
                                                                        className="h-full w-full object-cover"
                                                                    />
                                                                </button>
                                                            ),
                                                        )}
                                                    </div>
                                                </div>
                                            )}
                                        </div>
                                    ) : (
                                        <div className="flex aspect-video items-center justify-center bg-gray-200">
                                            <span className="text-gray-400">
                                                Sem imagens
                                            </span>
                                        </div>
                                    )}
                                </CardContent>
                            </Card>

                            {/* Informações do veículo */}
                            <Card>
                                <CardHeader>
                                    <div className="flex items-start justify-between">
                                        <div>
                                            <CardTitle className="text-2xl">
                                                {advertisement.title}
                                            </CardTitle>
                                            <p className="mt-1 text-gray-600">
                                                {
                                                    advertisement.vehicle.brand
                                                        .name
                                                }{' '}
                                                {advertisement.vehicle.model}{' '}
                                                {advertisement.vehicle.year}
                                            </p>
                                        </div>
                                        <div className="flex gap-2">
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                onClick={handleFavorite}
                                            >
                                                <Heart className="h-4 w-4" />
                                            </Button>
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                onClick={handleShare}
                                            >
                                                <Share2 className="h-4 w-4" />
                                            </Button>
                                            <Dialog
                                                open={showReportForm}
                                                onOpenChange={setShowReportForm}
                                            >
                                                <DialogTrigger asChild>
                                                    <Button
                                                        variant="outline"
                                                        size="sm"
                                                    >
                                                        <Flag className="h-4 w-4" />
                                                    </Button>
                                                </DialogTrigger>
                                                <DialogContent>
                                                    <DialogHeader>
                                                        <DialogTitle>
                                                            Denunciar anúncio
                                                        </DialogTitle>
                                                    </DialogHeader>
                                                    <form
                                                        onSubmit={handleReport}
                                                        className="space-y-4"
                                                    >
                                                        <div>
                                                            <Label>
                                                                Motivo
                                                            </Label>
                                                            <select
                                                                value={
                                                                    reportData.reason
                                                                }
                                                                onChange={(e) =>
                                                                    setReportData(
                                                                        'reason',
                                                                        e.target
                                                                            .value,
                                                                    )
                                                                }
                                                                className="w-full rounded-md border p-2"
                                                                required
                                                            >
                                                                <option value="">
                                                                    Selecione um
                                                                    motivo
                                                                </option>
                                                                <option value="spam">
                                                                    Spam
                                                                </option>
                                                                <option value="inappropriate">
                                                                    Conteúdo
                                                                    inapropriado
                                                                </option>
                                                                <option value="fake">
                                                                    Anúncio
                                                                    falso
                                                                </option>
                                                                <option value="other">
                                                                    Outro
                                                                </option>
                                                            </select>
                                                        </div>
                                                        <div>
                                                            <Label>
                                                                Descrição
                                                                (opcional)
                                                            </Label>
                                                            <Textarea
                                                                value={
                                                                    reportData.description
                                                                }
                                                                onChange={(e) =>
                                                                    setReportData(
                                                                        'description',
                                                                        e.target
                                                                            .value,
                                                                    )
                                                                }
                                                                placeholder="Descreva o problema..."
                                                            />
                                                        </div>
                                                        <Button
                                                            type="submit"
                                                            disabled={
                                                                reportProcessing
                                                            }
                                                        >
                                                            Enviar denúncia
                                                        </Button>
                                                    </form>
                                                </DialogContent>
                                            </Dialog>
                                        </div>
                                    </div>
                                </CardHeader>
                                <CardContent>
                                    <div className="mb-6 grid grid-cols-2 gap-4 md:grid-cols-4">
                                        <div className="flex items-center gap-2">
                                            <Calendar className="h-4 w-4 text-gray-500" />
                                            <span className="text-sm">
                                                {advertisement.vehicle.year}
                                            </span>
                                        </div>
                                        <div className="flex items-center gap-2">
                                            <Gauge className="h-4 w-4 text-gray-500" />
                                            <span className="text-sm">
                                                {formatMileage(
                                                    advertisement.vehicle
                                                        .mileage,
                                                )}
                                            </span>
                                        </div>
                                        <div className="flex items-center gap-2">
                                            <Fuel className="h-4 w-4 text-gray-500" />
                                            <span className="text-sm">
                                                {
                                                    advertisement.vehicle
                                                        .fuel_type
                                                }
                                            </span>
                                        </div>
                                        <div className="flex items-center gap-2">
                                            <Settings className="h-4 w-4 text-gray-500" />
                                            <span className="text-sm">
                                                {
                                                    advertisement.vehicle
                                                        .transmission
                                                }
                                            </span>
                                        </div>
                                    </div>

                                    <div className="space-y-4">
                                        <div>
                                            <h3 className="mb-2 font-semibold">
                                                Descrição
                                            </h3>
                                            <p className="whitespace-pre-wrap text-gray-700">
                                                {advertisement.description}
                                            </p>
                                        </div>

                                        {advertisement.vehicle.features.length >
                                            0 && (
                                            <div>
                                                <h3 className="mb-2 font-semibold">
                                                    Características
                                                </h3>
                                                <div className="grid grid-cols-2 gap-2 md:grid-cols-3">
                                                    {advertisement.vehicle.features.map(
                                                        (feature) => (
                                                            <div
                                                                key={feature.id}
                                                                className="flex justify-between"
                                                            >
                                                                <span className="text-sm text-gray-600">
                                                                    {
                                                                        feature.name
                                                                    }
                                                                    :
                                                                </span>
                                                                <span className="text-sm font-medium">
                                                                    {
                                                                        feature.value
                                                                    }
                                                                </span>
                                                            </div>
                                                        ),
                                                    )}
                                                </div>
                                            </div>
                                        )}
                                    </div>
                                </CardContent>
                            </Card>

                            {/* Anúncios relacionados */}
                            {relatedAds.length > 0 && (
                                <Card>
                                    <CardHeader>
                                        <CardTitle>
                                            Anúncios similares
                                        </CardTitle>
                                    </CardHeader>
                                    <CardContent>
                                        <ProductCarousel
                                            emptyMessage="Nenhum anúncio similar encontrado"
                                            emptyIcon="🔍"
                                        >
                                            {relatedAds.map((ad) => (
                                                <Link
                                                    key={ad.id}
                                                    href={`/anuncios/${ad.id}`}
                                                    className="block"
                                                >
                                                    <Card className="group cursor-pointer border-gray-200 transition-shadow duration-200 hover:shadow-lg">
                                                        <div className="relative">
                                                            <div className="aspect-video overflow-hidden rounded-t-lg bg-gray-100">
                                                                {ad.featured_image ? (
                                                                    <img
                                                                        src={
                                                                            ad
                                                                                .featured_image
                                                                                .url
                                                                        }
                                                                        alt={
                                                                            ad
                                                                                .featured_image
                                                                                .alt
                                                                        }
                                                                        className="h-full w-full object-cover transition-transform duration-200 group-hover:scale-105"
                                                                    />
                                                                ) : (
                                                                    <div className="flex h-full w-full items-center justify-center bg-gray-200">
                                                                        <span className="text-gray-400">
                                                                            Sem
                                                                            imagem
                                                                        </span>
                                                                    </div>
                                                                )}
                                                            </div>
                                                        </div>
                                                        <CardContent className="p-4">
                                                            <h3 className="mb-2 line-clamp-2 text-sm font-medium">
                                                                {ad.title}
                                                            </h3>
                                                            <p className="text-lg font-bold text-green-600">
                                                                {formatPrice(
                                                                    ad.price,
                                                                )}
                                                            </p>
                                                        </CardContent>
                                                    </Card>
                                                </Link>
                                            ))}
                                        </ProductCarousel>
                                    </CardContent>
                                </Card>
                            )}
                        </div>

                        {/* Sidebar */}
                        <div className="space-y-6">
                            {/* Preço e ações */}
                            <Card>
                                <CardContent className="p-6">
                                    <div className="mb-6 text-center">
                                        <div className="mb-2 text-3xl font-bold text-green-600">
                                            {formatPrice(advertisement.price)}
                                        </div>
                                        <div className="flex items-center justify-center gap-2 text-sm text-gray-600">
                                            <MapPin className="h-4 w-4" />
                                            {advertisement.location}
                                        </div>
                                    </div>

                                    <div className="space-y-3">
                                        <Dialog
                                            open={showContactForm}
                                            onOpenChange={setShowContactForm}
                                        >
                                            <DialogTrigger asChild>
                                                <Button
                                                    className="w-full"
                                                    size="lg"
                                                >
                                                    <MessageCircle className="mr-2 h-4 w-4" />
                                                    Entrar em contato
                                                </Button>
                                            </DialogTrigger>
                                            <DialogContent>
                                                <DialogHeader>
                                                    <DialogTitle>
                                                        Iniciar conversa
                                                    </DialogTitle>
                                                    <p className="text-sm text-gray-600">
                                                        Envie uma mensagem para
                                                        o vendedor sobre este
                                                        anúncio
                                                    </p>
                                                </DialogHeader>
                                                <form
                                                    onSubmit={handleContact}
                                                    className="space-y-4"
                                                >
                                                    <div>
                                                        <Label>Mensagem</Label>
                                                        <Textarea
                                                            value={
                                                                contactData.message
                                                            }
                                                            onChange={(e) =>
                                                                setContactData(
                                                                    'message',
                                                                    e.target
                                                                        .value,
                                                                )
                                                            }
                                                            placeholder="Olá! Tenho interesse neste anúncio. Poderia me fornecer mais informações?"
                                                            required
                                                            rows={4}
                                                        />
                                                    </div>
                                                    <Button
                                                        type="submit"
                                                        disabled={
                                                            contactProcessing
                                                        }
                                                        className="w-full"
                                                    >
                                                        Iniciar conversa
                                                    </Button>
                                                </form>
                                            </DialogContent>
                                        </Dialog>

                                        {advertisement.contact_phone && (
                                            <Button
                                                variant="outline"
                                                className="w-full"
                                                size="lg"
                                            >
                                                <Phone className="mr-2 h-4 w-4" />
                                                {advertisement.contact_phone}
                                            </Button>
                                        )}
                                    </div>
                                </CardContent>
                            </Card>

                            {/* Informações do vendedor */}
                            <Card>
                                <CardHeader>
                                    <CardTitle>Vendedor</CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <div className="mb-4 flex items-center gap-3">
                                        <div className="flex h-12 w-12 items-center justify-center rounded-full bg-gray-200">
                                            {advertisement.user.avatar ? (
                                                <img
                                                    src={
                                                        advertisement.user
                                                            .avatar
                                                    }
                                                    alt={
                                                        advertisement.user.name
                                                    }
                                                    className="h-full w-full rounded-full object-cover"
                                                />
                                            ) : (
                                                <User className="h-6 w-6 text-gray-400" />
                                            )}
                                        </div>
                                        <div>
                                            <h3 className="font-semibold">
                                                {advertisement.user.name}
                                            </h3>
                                            <p className="text-sm text-gray-600">
                                                Membro desde{' '}
                                                {formatDate(
                                                    advertisement.user
                                                        .created_at,
                                                )}
                                            </p>
                                        </div>
                                    </div>

                                    <Link
                                        href={`/vendedor/${advertisement.user.id}`}
                                        className="block w-full"
                                    >
                                        <Button
                                            variant="outline"
                                            className="w-full"
                                        >
                                            Ver perfil do vendedor
                                        </Button>
                                    </Link>

                                    {sellerAds.length > 0 && (
                                        <div className="mt-4">
                                            <h4 className="mb-2 font-medium">
                                                Outros anúncios do vendedor
                                            </h4>
                                            <div className="space-y-2">
                                                {sellerAds
                                                    .slice(0, 3)
                                                    .map((ad) => (
                                                        <Link
                                                            key={ad.id}
                                                            href={`/anuncios/${ad.id}`}
                                                            className="flex gap-2 rounded border p-2 transition-colors hover:bg-gray-50"
                                                        >
                                                            <div className="h-12 w-16 flex-shrink-0 overflow-hidden rounded bg-gray-200">
                                                                {ad.featured_image ? (
                                                                    <img
                                                                        src={
                                                                            ad
                                                                                .featured_image
                                                                                .url
                                                                        }
                                                                        alt={
                                                                            ad
                                                                                .featured_image
                                                                                .alt
                                                                        }
                                                                        className="h-full w-full object-cover"
                                                                    />
                                                                ) : (
                                                                    <div className="flex h-full w-full items-center justify-center bg-gray-200">
                                                                        <span className="text-xs text-gray-400">
                                                                            Sem
                                                                            imagem
                                                                        </span>
                                                                    </div>
                                                                )}
                                                            </div>
                                                            <div className="min-w-0 flex-1">
                                                                <p className="line-clamp-2 text-xs font-medium">
                                                                    {ad.title}
                                                                </p>
                                                                <p className="text-xs font-semibold text-green-600">
                                                                    {formatPrice(
                                                                        ad.price,
                                                                    )}
                                                                </p>
                                                            </div>
                                                        </Link>
                                                    ))}
                                            </div>
                                        </div>
                                    )}
                                </CardContent>
                            </Card>

                            {/* Estatísticas */}
                            <Card>
                                <CardContent className="p-6">
                                    <div className="space-y-3 text-sm">
                                        <div className="flex justify-between">
                                            <span className="text-gray-600">
                                                Publicado em:
                                            </span>
                                            <span>
                                                {formatDate(
                                                    advertisement.created_at,
                                                )}
                                            </span>
                                        </div>
                                        <div className="flex justify-between">
                                            <span className="text-gray-600">
                                                Visualizações:
                                            </span>
                                            <span className="flex items-center gap-1">
                                                <Eye className="h-3 w-3" />
                                                {advertisement.views_count || 0}
                                            </span>
                                        </div>
                                        <div className="flex justify-between">
                                            <span className="text-gray-600">
                                                ID do anúncio:
                                            </span>
                                            <span>#{advertisement.id}</span>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        </div>
                    </div>
                </div>
            </div>
        </MainLayout>
    );
}
