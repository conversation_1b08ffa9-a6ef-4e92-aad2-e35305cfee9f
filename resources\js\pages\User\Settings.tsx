import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import MainLayout from '@/layouts/MainLayout';
import { Head, Link, useForm } from '@inertiajs/react';
import { Settings, Bell, Shield, CreditCard, User, ArrowLeft } from 'lucide-react';

interface Props {
    auth: {
        user: {
            id: number;
            name: string;
            email: string;
            phone?: string;
            notification_preferences?: {
                email_notifications: boolean;
                sms_notifications: boolean;
                push_notifications: boolean;
                marketing_emails: boolean;
            };
        };
    };
}

export default function UserSettings({ auth }: Props) {
    const { data, setData, put, processing, errors } = useForm({
        email_notifications: auth.user.notification_preferences?.email_notifications ?? true,
        sms_notifications: auth.user.notification_preferences?.sms_notifications ?? false,
        push_notifications: auth.user.notification_preferences?.push_notifications ?? true,
        marketing_emails: auth.user.notification_preferences?.marketing_emails ?? false,
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        put('/minha-conta/configuracoes');
    };

    return (
        <MainLayout>
            <Head title="Configurações - Minha Conta" />
            
            <div className="min-h-screen bg-gray-50 py-8">
                <div className="container mx-auto px-4 max-w-4xl">
                    {/* Header */}
                    <div className="mb-8">
                        <div className="flex items-center space-x-4 mb-4">
                            <Link 
                                href="/minha-conta/perfil"
                                className="flex items-center text-gray-600 hover:text-primary"
                            >
                                <ArrowLeft className="h-4 w-4 mr-2" />
                                Voltar para Minha Conta
                            </Link>
                        </div>
                        <div className="flex items-center space-x-3">
                            <Settings className="h-8 w-8 text-primary" />
                            <div>
                                <h1 className="text-3xl font-bold text-gray-900">Configurações</h1>
                                <p className="text-gray-600">Gerencie suas preferências e configurações de conta</p>
                            </div>
                        </div>
                    </div>

                    <div className="grid gap-6">
                        {/* Notificações */}
                        <Card>
                            <CardHeader>
                                <div className="flex items-center space-x-2">
                                    <Bell className="h-5 w-5 text-primary" />
                                    <CardTitle>Notificações</CardTitle>
                                </div>
                                <CardDescription>
                                    Configure como você deseja receber notificações
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                <form onSubmit={handleSubmit} className="space-y-6">
                                    <div className="space-y-4">
                                        <div className="flex items-center justify-between">
                                            <div className="space-y-0.5">
                                                <Label htmlFor="email_notifications">Notificações por email</Label>
                                                <p className="text-sm text-gray-500">
                                                    Receba notificações sobre seus anúncios e mensagens
                                                </p>
                                            </div>
                                            <Switch
                                                id="email_notifications"
                                                checked={data.email_notifications}
                                                onCheckedChange={(checked) => setData('email_notifications', checked)}
                                            />
                                        </div>
                                        
                                        <Separator />
                                        
                                        <div className="flex items-center justify-between">
                                            <div className="space-y-0.5">
                                                <Label htmlFor="sms_notifications">Notificações por SMS</Label>
                                                <p className="text-sm text-gray-500">
                                                    Receba alertas importantes via SMS
                                                </p>
                                            </div>
                                            <Switch
                                                id="sms_notifications"
                                                checked={data.sms_notifications}
                                                onCheckedChange={(checked) => setData('sms_notifications', checked)}
                                            />
                                        </div>
                                        
                                        <Separator />
                                        
                                        <div className="flex items-center justify-between">
                                            <div className="space-y-0.5">
                                                <Label htmlFor="push_notifications">Notificações push</Label>
                                                <p className="text-sm text-gray-500">
                                                    Receba notificações no navegador
                                                </p>
                                            </div>
                                            <Switch
                                                id="push_notifications"
                                                checked={data.push_notifications}
                                                onCheckedChange={(checked) => setData('push_notifications', checked)}
                                            />
                                        </div>
                                        
                                        <Separator />
                                        
                                        <div className="flex items-center justify-between">
                                            <div className="space-y-0.5">
                                                <Label htmlFor="marketing_emails">Emails promocionais</Label>
                                                <p className="text-sm text-gray-500">
                                                    Receba ofertas especiais e novidades
                                                </p>
                                            </div>
                                            <Switch
                                                id="marketing_emails"
                                                checked={data.marketing_emails}
                                                onCheckedChange={(checked) => setData('marketing_emails', checked)}
                                            />
                                        </div>
                                    </div>
                                    
                                    <div className="flex justify-end">
                                        <Button type="submit" disabled={processing}>
                                            {processing ? 'Salvando...' : 'Salvar configurações'}
                                        </Button>
                                    </div>
                                </form>
                            </CardContent>
                        </Card>

                        {/* Segurança */}
                        <Card>
                            <CardHeader>
                                <div className="flex items-center space-x-2">
                                    <Shield className="h-5 w-5 text-primary" />
                                    <CardTitle>Segurança</CardTitle>
                                </div>
                                <CardDescription>
                                    Gerencie a segurança da sua conta
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="flex items-center justify-between">
                                    <div>
                                        <h4 className="font-medium">Alterar senha</h4>
                                        <p className="text-sm text-gray-500">
                                            Mantenha sua conta segura com uma senha forte
                                        </p>
                                    </div>
                                    <Button variant="outline" asChild>
                                        <Link href="/minha-conta/perfil/editar">
                                            Alterar senha
                                        </Link>
                                    </Button>
                                </div>
                                
                                <Separator />
                                
                                <div className="flex items-center justify-between">
                                    <div>
                                        <h4 className="font-medium">Verificação em duas etapas</h4>
                                        <p className="text-sm text-gray-500">
                                            Adicione uma camada extra de segurança
                                        </p>
                                    </div>
                                    <Button variant="outline" disabled>
                                        Em breve
                                    </Button>
                                </div>
                            </CardContent>
                        </Card>

                        {/* Conta */}
                        <Card>
                            <CardHeader>
                                <div className="flex items-center space-x-2">
                                    <User className="h-5 w-5 text-primary" />
                                    <CardTitle>Informações da conta</CardTitle>
                                </div>
                                <CardDescription>
                                    Gerencie suas informações pessoais
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="flex items-center justify-between">
                                    <div>
                                        <h4 className="font-medium">Editar perfil</h4>
                                        <p className="text-sm text-gray-500">
                                            Atualize suas informações pessoais
                                        </p>
                                    </div>
                                    <Button variant="outline" asChild>
                                        <Link href="/minha-conta/perfil/editar">
                                            Editar perfil
                                        </Link>
                                    </Button>
                                </div>
                                
                                <Separator />
                                
                                <div className="flex items-center justify-between">
                                    <div>
                                        <h4 className="font-medium text-red-600">Excluir conta</h4>
                                        <p className="text-sm text-gray-500">
                                            Remover permanentemente sua conta e dados
                                        </p>
                                    </div>
                                    <Button variant="destructive" disabled>
                                        Excluir conta
                                    </Button>
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                </div>
            </div>
        </MainLayout>
    );
}
