import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import {
    Card,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle,
} from '@/components/ui/card';
import { <PERSON>, <PERSON> } from '@inertiajs/react';
import {
    AlertCircle,
    CheckCircle,
    Clock,
    Eye,
    Heart,
    MessageCircle,
    Plus,
    ShoppingBag,
} from 'lucide-react';

interface DashboardStats {
    total_advertisements: number;
    active_advertisements: number;
    pending_advertisements: number;
    total_favorites: number;
    total_chats: number;
    unread_messages: number;
}

interface Advertisement {
    id: number;
    title: string;
    status: string;
    status_label: string;
    price: number;
    views: number;
    created_at: string;
    featured_image_url: string;
    url: string;
}

interface Chat {
    id: number;
    advertisement: {
        title: string;
        featured_image_url: string;
    };
    other_participant: {
        name: string;
    };
    last_message: {
        message: string;
        created_at: string;
    } | null;
    unread_count: number;
    url: string;
}

interface Favorite {
    id: number;
    advertisement: {
        id: number;
        title: string;
        price: number;
        featured_image_url: string;
        url: string;
    };
    created_at: string;
}

interface Props {
    stats: DashboardStats;
    recentAdvertisements: Advertisement[];
    recentChats: Chat[];
    recentFavorites: Favorite[];
}

export default function UserDashboard({
    stats,
    recentAdvertisements,
    recentChats,
    recentFavorites,
}: Props) {
    const formatPrice = (price: number) => {
        return new Intl.NumberFormat('pt-BR', {
            style: 'currency',
            currency: 'BRL',
        }).format(price);
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('pt-BR');
    };

    const getStatusIcon = (status: string) => {
        switch (status) {
            case 'published':
                return <CheckCircle className="h-4 w-4 text-green-600" />;
            case 'pending_review':
                return <Clock className="h-4 w-4 text-yellow-600" />;
            case 'rejected':
                return <AlertCircle className="h-4 w-4 text-red-600" />;
            default:
                return <Clock className="h-4 w-4 text-gray-600" />;
        }
    };

    const getStatusColor = (status: string) => {
        switch (status) {
            case 'published':
                return 'bg-green-100 text-green-800';
            case 'pending_review':
                return 'bg-yellow-100 text-yellow-800';
            case 'rejected':
                return 'bg-red-100 text-red-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };

    const statCards = [
        {
            title: 'Meus Anúncios',
            value: stats.total_advertisements,
            description: `${stats.active_advertisements} ativos`,
            icon: ShoppingBag,
            color: 'text-blue-600',
            bgColor: 'bg-blue-50',
            href: '/minha-conta/anuncios',
        },
        {
            title: 'Favoritos',
            value: stats.total_favorites,
            description: 'Itens salvos',
            icon: Heart,
            color: 'text-red-600',
            bgColor: 'bg-red-50',
            href: '/minha-conta/favoritos',
        },
        {
            title: 'Conversas',
            value: stats.total_chats,
            description: `${stats.unread_messages} não lidas`,
            icon: MessageCircle,
            color: 'text-green-600',
            bgColor: 'bg-green-50',
            href: '/minha-conta/chat',
        },
    ];

    return (
        <MainLayout>
            <Head title="Minha Conta - Dashboard" />

            <div className="container mx-auto px-4 py-8">
                <div className="mb-8">
                    <h1 className="text-3xl font-bold text-gray-900">
                        Minha Conta
                    </h1>
                    <p className="text-gray-600">
                        Gerencie seus anúncios, conversas e favoritos
                    </p>
                </div>

                {/* Stats Cards */}
                <div className="mb-8 grid gap-6 md:grid-cols-3">
                    {statCards.map((card, index) => (
                        <Link key={index} href={card.href}>
                            <Card className="cursor-pointer transition-shadow hover:shadow-lg">
                                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                    <CardTitle className="text-sm font-medium">
                                        {card.title}
                                    </CardTitle>
                                    <div
                                        className={`rounded-lg p-2 ${card.bgColor}`}
                                    >
                                        <card.icon
                                            className={`h-4 w-4 ${card.color}`}
                                        />
                                    </div>
                                </CardHeader>
                                <CardContent>
                                    <div className="text-2xl font-bold">
                                        {card.value}
                                    </div>
                                    <p className="text-xs text-muted-foreground">
                                        {card.description}
                                    </p>
                                </CardContent>
                            </Card>
                        </Link>
                    ))}
                </div>

                {/* Quick Actions */}
                <div className="mb-8">
                    <Card>
                        <CardHeader>
                            <CardTitle>Ações Rápidas</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="flex gap-4">
                                <Button asChild>
                                    <Link href="/minha-conta/anuncios/create">
                                        <Plus className="mr-2 h-4 w-4" />
                                        Criar Anúncio
                                    </Link>
                                </Button>
                                <Button variant="outline" asChild>
                                    <Link href="/pesquisar">
                                        Buscar Veículos
                                    </Link>
                                </Button>
                            </div>
                        </CardContent>
                    </Card>
                </div>

                <div className="grid gap-6 lg:grid-cols-2">
                    {/* Recent Advertisements */}
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between">
                            <div>
                                <CardTitle>Meus Anúncios Recentes</CardTitle>
                                <CardDescription>
                                    Últimos anúncios criados
                                </CardDescription>
                            </div>
                            <Button variant="outline" size="sm" asChild>
                                <Link href="/minha-conta/anuncios">
                                    Ver todos
                                </Link>
                            </Button>
                        </CardHeader>
                        <CardContent>
                            {recentAdvertisements.length > 0 ? (
                                <div className="space-y-4">
                                    {recentAdvertisements.map((ad) => (
                                        <div
                                            key={ad.id}
                                            className="flex items-center gap-4 rounded-lg border p-3"
                                        >
                                            <img
                                                src={ad.featured_image_url}
                                                alt={ad.title}
                                                className="h-12 w-12 rounded object-cover"
                                            />
                                            <div className="min-w-0 flex-1">
                                                <Link
                                                    href={ad.url}
                                                    className="text-sm font-medium hover:text-blue-600"
                                                >
                                                    {ad.title}
                                                </Link>
                                                <div className="mt-1 flex items-center gap-2">
                                                    <Badge
                                                        className={`text-xs ${getStatusColor(ad.status)}`}
                                                    >
                                                        {getStatusIcon(
                                                            ad.status,
                                                        )}
                                                        <span className="ml-1">
                                                            {ad.status_label}
                                                        </span>
                                                    </Badge>
                                                    <span className="text-xs text-gray-500">
                                                        {formatPrice(ad.price)}
                                                    </span>
                                                </div>
                                            </div>
                                            <div className="text-right">
                                                <div className="flex items-center text-xs text-gray-500">
                                                    <Eye className="mr-1 h-3 w-3" />
                                                    {ad.views}
                                                </div>
                                                <div className="mt-1 text-xs text-gray-500">
                                                    {formatDate(ad.created_at)}
                                                </div>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            ) : (
                                <p className="py-4 text-center text-gray-500">
                                    Nenhum anúncio encontrado
                                </p>
                            )}
                        </CardContent>
                    </Card>

                    {/* Recent Chats */}
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between">
                            <div>
                                <CardTitle>Conversas Recentes</CardTitle>
                                <CardDescription>
                                    Últimas mensagens
                                </CardDescription>
                            </div>
                            <Button variant="outline" size="sm" asChild>
                                <Link href="/minha-conta/chat">Ver todas</Link>
                            </Button>
                        </CardHeader>
                        <CardContent>
                            {recentChats.length > 0 ? (
                                <div className="space-y-4">
                                    {recentChats.map((chat) => (
                                        <Link key={chat.id} href={chat.url}>
                                            <div className="flex items-center gap-4 rounded-lg border p-3 hover:bg-gray-50">
                                                <img
                                                    src={
                                                        chat.advertisement
                                                            .featured_image_url
                                                    }
                                                    alt={
                                                        chat.advertisement.title
                                                    }
                                                    className="h-12 w-12 rounded object-cover"
                                                />
                                                <div className="min-w-0 flex-1">
                                                    <div className="text-sm font-medium">
                                                        {
                                                            chat
                                                                .other_participant
                                                                .name
                                                        }
                                                    </div>
                                                    <div className="truncate text-xs text-gray-500">
                                                        {
                                                            chat.advertisement
                                                                .title
                                                        }
                                                    </div>
                                                    {chat.last_message && (
                                                        <div className="mt-1 truncate text-xs text-gray-600">
                                                            {
                                                                chat
                                                                    .last_message
                                                                    .message
                                                            }
                                                        </div>
                                                    )}
                                                </div>
                                                {chat.unread_count > 0 && (
                                                    <Badge className="bg-blue-100 text-blue-800">
                                                        {chat.unread_count}
                                                    </Badge>
                                                )}
                                            </div>
                                        </Link>
                                    ))}
                                </div>
                            ) : (
                                <p className="py-4 text-center text-gray-500">
                                    Nenhuma conversa encontrada
                                </p>
                            )}
                        </CardContent>
                    </Card>
                </div>
            </div>
        </MainLayout>
    );
}
