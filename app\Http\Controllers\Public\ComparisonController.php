<?php

namespace App\Http\Controllers\Public;

use App\Http\Controllers\Controller;
use App\Models\Advertisement;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class ComparisonController extends Controller
{
    /**
     * Display the comparison page
     */
    public function index(Request $request): Response
    {
        $advertisementIds = $request->input('ads', []);
        
        // Ensure we have an array and limit to maximum 4 advertisements
        if (!is_array($advertisementIds)) {
            $advertisementIds = explode(',', $advertisementIds);
        }
        
        $advertisementIds = array_slice(array_filter($advertisementIds), 0, 4);
        
        $advertisements = [];
        
        if (!empty($advertisementIds)) {
            $advertisements = Advertisement::with([
                'vehicle.brand',
                'vehicle.category',
                'featuredImage',
                'images',
                'user'
            ])
            ->whereIn('id', $advertisementIds)
            ->where('status', 'published')
            ->get()
            ->keyBy('id');
            
            // Reorder based on the original order
            $orderedAdvertisements = [];
            foreach ($advertisementIds as $id) {
                if (isset($advertisements[$id])) {
                    $orderedAdvertisements[] = $advertisements[$id];
                }
            }
            $advertisements = $orderedAdvertisements;
        }

        return Inertia::render('Comparison/Index', [
            'advertisements' => $advertisements,
            'advertisementIds' => $advertisementIds,
        ]);
    }

    /**
     * Add advertisement to comparison
     */
    public function add(Request $request, Advertisement $advertisement)
    {
        // Check if advertisement is published
        if ($advertisement->status !== 'published') {
            return response()->json(['error' => 'Anúncio não está disponível para comparação.'], 400);
        }

        $currentIds = $request->input('current_ads', []);
        
        if (!is_array($currentIds)) {
            $currentIds = explode(',', $currentIds);
        }
        
        $currentIds = array_filter($currentIds);

        // Check if already in comparison
        if (in_array($advertisement->id, $currentIds)) {
            return response()->json(['error' => 'Anúncio já está na comparação.'], 400);
        }

        // Check maximum limit
        if (count($currentIds) >= 4) {
            return response()->json(['error' => 'Máximo de 4 anúncios podem ser comparados.'], 400);
        }

        $currentIds[] = $advertisement->id;

        return response()->json([
            'message' => 'Anúncio adicionado à comparação!',
            'advertisement_ids' => $currentIds,
            'comparison_url' => route('comparison.index', ['ads' => implode(',', $currentIds)]),
        ]);
    }

    /**
     * Remove advertisement from comparison
     */
    public function remove(Request $request, Advertisement $advertisement)
    {
        $currentIds = $request->input('current_ads', []);
        
        if (!is_array($currentIds)) {
            $currentIds = explode(',', $currentIds);
        }
        
        $currentIds = array_filter($currentIds);
        $currentIds = array_diff($currentIds, [$advertisement->id]);

        return response()->json([
            'message' => 'Anúncio removido da comparação!',
            'advertisement_ids' => array_values($currentIds),
            'comparison_url' => !empty($currentIds) 
                ? route('comparison.index', ['ads' => implode(',', $currentIds)])
                : route('comparison.index'),
        ]);
    }

    /**
     * Clear all advertisements from comparison
     */
    public function clear()
    {
        return response()->json([
            'message' => 'Comparação limpa!',
            'advertisement_ids' => [],
            'comparison_url' => route('comparison.index'),
        ]);
    }

    /**
     * Get comparison count
     */
    public function count(Request $request)
    {
        $currentIds = $request->input('current_ads', []);
        
        if (!is_array($currentIds)) {
            $currentIds = explode(',', $currentIds);
        }
        
        $count = count(array_filter($currentIds));

        return response()->json(['count' => $count]);
    }

    /**
     * Search advertisements for comparison
     */
    public function search(Request $request)
    {
        $query = Advertisement::with(['vehicle.brand', 'featuredImage'])
            ->where('status', 'published');

        // Search filter
        if ($request->filled('search')) {
            $search = $request->input('search');
            $query->where(function($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhereHas('vehicle', function($vq) use ($search) {
                      $vq->where('model', 'like', "%{$search}%")
                         ->orWhereHas('brand', function($bq) use ($search) {
                             $bq->where('name', 'like', "%{$search}%");
                         });
                  });
            });
        }

        // Brand filter
        if ($request->filled('brand_id')) {
            $query->whereHas('vehicle', function($q) use ($request) {
                $q->where('brand_id', $request->input('brand_id'));
            });
        }

        // Category filter
        if ($request->filled('category_id')) {
            $query->whereHas('vehicle', function($q) use ($request) {
                $q->where('category_id', $request->input('category_id'));
            });
        }

        // Price range
        if ($request->filled('min_price')) {
            $query->where('price', '>=', $request->input('min_price'));
        }

        if ($request->filled('max_price')) {
            $query->where('price', '<=', $request->input('max_price'));
        }

        // Year range
        if ($request->filled('min_year')) {
            $query->whereHas('vehicle', function($q) use ($request) {
                $q->where('year', '>=', $request->input('min_year'));
            });
        }

        if ($request->filled('max_year')) {
            $query->whereHas('vehicle', function($q) use ($request) {
                $q->where('year', '<=', $request->input('max_year'));
            });
        }

        $advertisements = $query->orderBy('created_at', 'desc')
            ->paginate(20)
            ->withQueryString();

        return response()->json($advertisements);
    }
}
