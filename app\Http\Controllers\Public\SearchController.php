<?php

namespace App\Http\Controllers\Public;

use App\Http\Controllers\Controller;
use App\Models\Brand;
use App\Models\Category;
use App\Models\Vehicle;
use App\Models\Advertisement;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class SearchController extends Controller
{
    /**
     * Display the search page with filters
     */
    public function index(Request $request): Response
    {
        $query = Advertisement::with(['vehicle.brand', 'vehicle.category', 'user'])
            ->where('status', 'published');

        // Apply filters
        if ($request->filled('search')) {
            $search = $request->input('search');
            $query->where(function($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhereHas('vehicle', function($vehicleQuery) use ($search) {
                      $vehicleQuery->where('model', 'like', "%{$search}%")
                                  ->orWhere('year_manufacture', 'like', "%{$search}%");
                  });
            });
        }

        if ($request->filled('category_id') && $request->input('category_id') !== 'all') {
            $query->whereHas('vehicle', function($vehicleQuery) use ($request) {
                $vehicleQuery->where('category_id', $request->input('category_id'));
            });
        }

        // Filter by category slug (for "Ver todos" links)
        if ($request->filled('categoria')) {
            $categorySlug = $request->input('categoria');
            $query->whereHas('vehicle.category', function($categoryQuery) use ($categorySlug) {
                $categoryQuery->where('slug', $categorySlug);
            });
        }

        if ($request->filled('brand_id') && $request->input('brand_id') !== 'all') {
            $query->whereHas('vehicle', function($vehicleQuery) use ($request) {
                $vehicleQuery->where('brand_id', $request->input('brand_id'));
            });
        }

        if ($request->filled('min_price')) {
            $query->where('price', '>=', $request->input('min_price'));
        }

        if ($request->filled('max_price')) {
            $query->where('price', '<=', $request->input('max_price'));
        }

        if ($request->filled('fuel_type')) {
            $query->whereHas('vehicle', function($vehicleQuery) use ($request) {
                $vehicleQuery->where('fuel_type', $request->input('fuel_type'));
            });
        }

        if ($request->filled('transmission')) {
            $query->whereHas('vehicle', function($vehicleQuery) use ($request) {
                $vehicleQuery->where('transmission', $request->input('transmission'));
            });
        }

        if ($request->filled('year_min')) {
            $query->whereHas('vehicle', function($vehicleQuery) use ($request) {
                $vehicleQuery->where('year_manufacture', '>=', $request->input('year_min'));
            });
        }

        if ($request->filled('year_max')) {
            $query->whereHas('vehicle', function($vehicleQuery) use ($request) {
                $vehicleQuery->where('year_manufacture', '<=', $request->input('year_max'));
            });
        }

        if ($request->filled('mileage_max')) {
            $query->whereHas('vehicle', function($vehicleQuery) use ($request) {
                $vehicleQuery->where('mileage', '<=', $request->input('mileage_max'));
            });
        }

        if ($request->filled('condition')) {
            $query->whereHas('vehicle', function($vehicleQuery) use ($request) {
                $vehicleQuery->where('condition', $request->input('condition'));
            });
        }

        if ($request->filled('location')) {
            $query->where('location', 'like', '%' . $request->input('location') . '%');
        }

        // Sorting
        $sortBy = $request->input('sort_by', 'created_at');
        $sortOrder = $request->input('sort_order', 'desc');
        
        $validSortFields = ['created_at', 'price', 'title'];
        if (in_array($sortBy, $validSortFields)) {
            $query->orderBy($sortBy, $sortOrder);
        }

        $advertisements = $query->paginate(12)->withQueryString();

        // Get filter options
        $brands = Brand::whereHas('vehicles', function($vehicleQuery) {
            $vehicleQuery->whereHas('advertisements', function($q) {
                $q->where('status', 'published');
            });
        })->orderBy('name')->get(['id', 'name']);

        $categories = Category::whereHas('vehicles', function($vehicleQuery) {
            $vehicleQuery->whereHas('advertisements', function($q) {
                $q->where('status', 'published');
            });
        })->orderBy('name')->get(['id', 'name']);

        // Get price range
        $priceRange = Advertisement::where('status', 'published')
            ->selectRaw('MIN(price) as min_price, MAX(price) as max_price')
            ->first();

        // Get year range
        $yearRange = Vehicle::whereHas('advertisements', function($q) {
            $q->where('status', 'published');
        })->selectRaw('MIN(year_manufacture) as min_year, MAX(year_manufacture) as max_year')->first();

        return Inertia::render('Search/Index', [
            'advertisements' => $advertisements,
            'brands' => $brands,
            'categories' => $categories,
            'filters' => $request->all(),
            'priceRange' => $priceRange,
            'yearRange' => $yearRange,
            'fuelTypes' => [
                'gasoline' => 'Gasolina',
                'ethanol' => 'Etanol',
                'flex' => 'Flex',
                'diesel' => 'Diesel',
                'electric' => 'Elétrico',
                'hybrid' => 'Híbrido'
            ],
            'transmissions' => [
                'manual' => 'Manual',
                'automatic' => 'Automático',
                'cvt' => 'CVT'
            ],
            'conditions' => [
                'new' => 'Novo',
                'used' => 'Usado',
                'semi_new' => 'Semi-novo'
            ]
        ]);
    }

    /**
     * Get suggestions for search autocomplete
     */
    public function suggestions(Request $request)
    {
        $query = $request->input('q', '');
        
        if (strlen($query) < 2) {
            return response()->json([]);
        }

        $suggestions = collect();

        // Vehicle models
        $vehicles = Vehicle::whereHas('advertisements', function($q) {
                $q->where('status', 'published');
            })
            ->where('model', 'like', "%{$query}%")
            ->distinct()
            ->limit(5)
            ->pluck('model')
            ->map(function($model) {
                return [
                    'type' => 'model',
                    'text' => $model,
                    'label' => "Modelo: {$model}"
                ];
            });

        // Brands
        $brands = Brand::whereHas('vehicles', function($vehicleQuery) {
                $vehicleQuery->whereHas('advertisements', function($q) {
                    $q->where('status', 'published');
                });
            })
            ->where('name', 'like', "%{$query}%")
            ->limit(3)
            ->pluck('name')
            ->map(function($name) {
                return [
                    'type' => 'brand',
                    'text' => $name,
                    'label' => "Marca: {$name}"
                ];
            });

        // Advertisement titles
        $ads = Advertisement::where('status', 'published')
            ->where('title', 'like', "%{$query}%")
            ->limit(3)
            ->pluck('title')
            ->map(function($title) {
                return [
                    'type' => 'ad',
                    'text' => $title,
                    'label' => $title
                ];
            });

        $suggestions = $suggestions->concat($vehicles)
                                 ->concat($brands)
                                 ->concat($ads)
                                 ->take(10);

        return response()->json($suggestions);
    }
}
