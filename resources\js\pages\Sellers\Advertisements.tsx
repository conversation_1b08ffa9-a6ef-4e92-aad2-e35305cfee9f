import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select';
import MainLayout from '@/layouts/MainLayout';
import { PageProps } from '@/types';
import { Head, Link, router } from '@inertiajs/react';
import { ArrowLeft, CheckCircle, Grid, Heart, List, MapPin, Search, User as UserIcon } from 'lucide-react';
import { useState } from 'react';

interface Advertisement {
    id: number;
    title: string;
    description: string;
    price: number;
    location: string;
    created_at: string;
    is_featured: boolean;
    featured_image?: {
        url: string;
        alt: string;
    };
    vehicle: {
        id: number;
        model: string;
        year: number;
        mileage: number;
        fuel_type: string;
        transmission: string;
        brand: {
            id: number;
            name: string;
        };
        category: {
            id: number;
            name: string;
        };
    };
}

interface Seller {
    id: number;
    name: string;
    avatar?: string;
    created_at: string;
}

interface SellerAdvertisementsProps extends PageProps {
    seller: Seller;
    advertisements: {
        data: Advertisement[];
        links: any[];
        meta: any;
    };
    filters: Record<string, any>;
}

export default function SellerAdvertisements({
    seller,
    advertisements,
    filters,
}: SellerAdvertisementsProps) {
    const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
    const [localFilters, setLocalFilters] = useState({
        search: filters.search || '',
        sort_by: filters.sort_by || 'created_at',
        sort_order: filters.sort_order || 'desc',
    });

    const handleFilterChange = (key: string, value: any) => {
        setLocalFilters(prev => ({ ...prev, [key]: value }));
    };

    const applyFilters = () => {
        const cleanFilters = Object.fromEntries(
            Object.entries(localFilters).filter(([_, value]) => 
                value !== '' && value !== null && value !== undefined
            )
        );
        
        router.get(`/vendedor/${seller.id}/anuncios`, cleanFilters, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    const formatPrice = (price: number) => {
        return new Intl.NumberFormat('pt-BR', {
            style: 'currency',
            currency: 'BRL',
        }).format(price);
    };

    const formatMileage = (mileage: number) => {
        return new Intl.NumberFormat('pt-BR').format(mileage) + ' km';
    };

    const formatDate = (date: string) => {
        return new Date(date).toLocaleDateString('pt-BR');
    };

    return (
        <MainLayout>
            <Head title={`Anúncios do vendedor ${seller.name}`} />
            
            <div className="min-h-screen bg-gray-50">
                {/* Header */}
                <div className="bg-white border-b">
                    <div className="container mx-auto px-4 py-6">
                        {/* Breadcrumb */}
                        <div className="flex items-center gap-2 mb-4">
                            <Link href={`/vendedor/${seller.id}`}>
                                <Button variant="ghost" size="sm">
                                    <ArrowLeft className="h-4 w-4 mr-1" />
                                    Voltar ao perfil
                                </Button>
                            </Link>
                        </div>

                        {/* Seller info */}
                        <div className="flex items-center gap-4 mb-6">
                            <div className="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center relative">
                                {seller.avatar ? (
                                    <img
                                        src={seller.avatar}
                                        alt={seller.name}
                                        className="w-full h-full rounded-full object-cover"
                                    />
                                ) : (
                                    <UserIcon className="h-8 w-8 text-gray-400" />
                                )}
                                <div className="absolute -bottom-1 -right-1 bg-blue-500 rounded-full p-1">
                                    <CheckCircle className="h-3 w-3 text-white" />
                                </div>
                            </div>
                            <div>
                                <h1 className="text-2xl font-bold flex items-center gap-2">
                                    Anúncios do vendedor {seller.name}
                                    <Badge className="bg-blue-500">
                                        <CheckCircle className="h-3 w-3 mr-1" />
                                        Verificado
                                    </Badge>
                                </h1>
                                <p className="text-gray-600">
                                    {advertisements.meta.total} anúncios encontrados
                                </p>
                            </div>
                        </div>

                        {/* Search and filters */}
                        <div className="flex flex-col lg:flex-row gap-4">
                            {/* Barra de pesquisa */}
                            <div className="flex-1">
                                <div className="relative">
                                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                                    <Input
                                        placeholder="Buscar nos anúncios deste vendedor..."
                                        value={localFilters.search}
                                        onChange={(e) => handleFilterChange('search', e.target.value)}
                                        onKeyPress={(e) => e.key === 'Enter' && applyFilters()}
                                        className="pl-10 h-12"
                                    />
                                </div>
                            </div>
                            
                            {/* Botão de busca */}
                            <Button onClick={applyFilters} className="h-12 px-6">
                                <Search className="mr-2 h-4 w-4" />
                                Buscar
                            </Button>
                        </div>
                    </div>
                </div>

                <div className="container mx-auto px-4 py-6">
                    {/* Barra de resultados e ordenação */}
                    <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
                        <div>
                            {filters.search && (
                                <p className="text-gray-600">
                                    Resultados para "{filters.search}"
                                </p>
                            )}
                        </div>
                        
                        <div className="flex items-center gap-2">
                            {/* Ordenação */}
                            <Select
                                value={`${localFilters.sort_by}-${localFilters.sort_order}`}
                                onValueChange={(value) => {
                                    const [sortBy, sortOrder] = value.split('-');
                                    handleFilterChange('sort_by', sortBy);
                                    handleFilterChange('sort_order', sortOrder);
                                    applyFilters();
                                }}
                            >
                                <SelectTrigger className="w-48">
                                    <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="created_at-desc">Mais recentes</SelectItem>
                                    <SelectItem value="created_at-asc">Mais antigos</SelectItem>
                                    <SelectItem value="price-asc">Menor preço</SelectItem>
                                    <SelectItem value="price-desc">Maior preço</SelectItem>
                                    <SelectItem value="title-asc">A-Z</SelectItem>
                                    <SelectItem value="title-desc">Z-A</SelectItem>
                                </SelectContent>
                            </Select>
                            
                            {/* Modo de visualização */}
                            <div className="flex border rounded-md">
                                <Button
                                    variant={viewMode === 'grid' ? 'default' : 'ghost'}
                                    size="sm"
                                    onClick={() => setViewMode('grid')}
                                    className="rounded-r-none"
                                >
                                    <Grid className="h-4 w-4" />
                                </Button>
                                <Button
                                    variant={viewMode === 'list' ? 'default' : 'ghost'}
                                    size="sm"
                                    onClick={() => setViewMode('list')}
                                    className="rounded-l-none"
                                >
                                    <List className="h-4 w-4" />
                                </Button>
                            </div>
                        </div>
                    </div>

                    {/* Lista de anúncios */}
                    {advertisements.data.length > 0 ? (
                        <div className={viewMode === 'grid' 
                            ? 'grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6' 
                            : 'space-y-4'
                        }>
                            {advertisements.data.map((ad) => (
                                <Card key={ad.id} className="overflow-hidden hover:shadow-lg transition-shadow">
                                    <div className={viewMode === 'grid' ? '' : 'flex'}>
                                        {/* Imagem */}
                                        <div className={viewMode === 'grid' ? 'aspect-video relative' : 'w-48 h-32 flex-shrink-0 relative'}>
                                            {ad.featured_image ? (
                                                <img
                                                    src={ad.featured_image.url}
                                                    alt={ad.featured_image.alt}
                                                    className="w-full h-full object-cover"
                                                />
                                            ) : (
                                                <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                                                    <span className="text-gray-400">Sem imagem</span>
                                                </div>
                                            )}
                                            
                                            {/* Badges */}
                                            <div className="absolute top-2 left-2 flex gap-1">
                                                {ad.is_featured && (
                                                    <Badge className="bg-yellow-500">Destaque</Badge>
                                                )}
                                            </div>
                                            
                                            {/* Botão de favorito */}
                                            <Button
                                                variant="ghost"
                                                size="sm"
                                                className="absolute top-2 right-2 bg-white/80 hover:bg-white"
                                            >
                                                <Heart className="h-4 w-4" />
                                            </Button>
                                        </div>
                                        
                                        {/* Conteúdo */}
                                        <CardContent className={`p-4 ${viewMode === 'list' ? 'flex-1' : ''}`}>
                                            <div className="space-y-2">
                                                <h3 className="font-semibold text-lg line-clamp-2">
                                                    <Link 
                                                        href={`/anuncios/${ad.id}`}
                                                        className="hover:text-blue-600"
                                                    >
                                                        {ad.title}
                                                    </Link>
                                                </h3>
                                                
                                                <div className="flex flex-wrap gap-2 text-sm text-gray-600">
                                                    <span>{ad.vehicle.brand.name}</span>
                                                    <span>•</span>
                                                    <span>{ad.vehicle.year}</span>
                                                    <span>•</span>
                                                    <span>{formatMileage(ad.vehicle.mileage)}</span>
                                                </div>
                                                
                                                <div className="flex flex-wrap gap-1">
                                                    <Badge variant="secondary">
                                                        {ad.vehicle.fuel_type}
                                                    </Badge>
                                                    <Badge variant="secondary">
                                                        {ad.vehicle.transmission}
                                                    </Badge>
                                                </div>
                                                
                                                <div className="flex justify-between items-center">
                                                    <span className="text-2xl font-bold text-green-600">
                                                        {formatPrice(ad.price)}
                                                    </span>
                                                    <div className="text-right">
                                                        <div className="flex items-center text-sm text-gray-500">
                                                            <MapPin className="h-3 w-3 mr-1" />
                                                            {ad.location}
                                                        </div>
                                                        <div className="text-xs text-gray-400">
                                                            {formatDate(ad.created_at)}
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </CardContent>
                                    </div>
                                </Card>
                            ))}
                        </div>
                    ) : (
                        <div className="text-center py-12">
                            <h3 className="text-xl font-semibold mb-2">Nenhum anúncio encontrado</h3>
                            <p className="text-gray-600 mb-4">
                                {filters.search 
                                    ? 'Tente fazer uma nova pesquisa ou ajustar os filtros'
                                    : 'Este vendedor ainda não publicou nenhum anúncio.'
                                }
                            </p>
                            {filters.search && (
                                <Button onClick={() => {
                                    setLocalFilters(prev => ({ ...prev, search: '' }));
                                    router.get(`/vendedor/${seller.id}/anuncios`);
                                }}>
                                    Limpar filtros
                                </Button>
                            )}
                        </div>
                    )}

                    {/* Paginação */}
                    {advertisements.links && advertisements.links.length > 3 && (
                        <div className="mt-8 flex justify-center">
                            <div className="flex gap-2">
                                {advertisements.links.map((link, index) => (
                                    <Button
                                        key={index}
                                        variant={link.active ? 'default' : 'outline'}
                                        size="sm"
                                        onClick={() => {
                                            if (link.url) {
                                                router.get(link.url);
                                            }
                                        }}
                                        disabled={!link.url}
                                        dangerouslySetInnerHTML={{ __html: link.label }}
                                    />
                                ))}
                            </div>
                        </div>
                    )}
                </div>
            </div>
        </MainLayout>
    );
}
