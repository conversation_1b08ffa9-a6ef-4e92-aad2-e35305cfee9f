import Link from "next/link"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Heart, ChevronRight } from "lucide-react"

const featuredListings = [
  {
    id: 1,
    title: "Moto Fazer 250",
    price: "R$ 5.500",
    location: "Belford Roxo - RJ",
    date: "18/09/2025, 22:32",
    image: "/yamaha-mt-07-2021-blue-motorcycle.jpg",
    type: "Moto",
    featured: false,
  },
  {
    id: 2,
    title: "Scooters Elétrica!!! Festival de OFERTAS !!!",
    price: "R$ 3.499",
    location: "Rio de Janeiro - RJ",
    date: "18/09/2025, 22:49",
    image: "/honda-civic-2022-silver-car.jpg",
    type: "Moto",
    featured: true,
  },
  {
    id: 3,
    title: "Moto Honda NC 700 X ABS",
    price: "R$ 29.000",
    location: "Rio de Janeiro - RJ",
    date: "18/09/2025, 22:56",
    image: "/toyota-corolla-2023-white-car.jpg",
    type: "Moto",
    featured: false,
  },
  {
    id: 4,
    title: "Yamaha Ys 150 fazer sed 2023",
    price: "R$ 17.000",
    location: "Rio de Janeiro - RJ",
    date: "22/07/2025, 17:39",
    image: "/car-suspension-kit-parts.jpg",
    type: "Moto",
    featured: false,
  },
  {
    id: 5,
    title: "Biz 2024 único dono",
    price: "R$ 15.900",
    location: "Rio de Janeiro - RJ",
    date: "Hoje, 0:53",
    image: "/honda-civic-2022-silver-car.jpg",
    type: "Moto",
    featured: false,
  },
  {
    id: 6,
    title: "YAMAHA FACTOR 150 FLEX - 19/20",
    price: "R$ 11.500",
    location: "Rio de Janeiro - RJ",
    date: "Hoje, 0:36",
    image: "/yamaha-mt-07-2021-blue-motorcycle.jpg",
    type: "Moto",
    featured: false,
  },
]

export function FeaturedListings() {
  return (
    <section className="py-8 bg-white">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-gray-800">Mais procurados em Motos</h2>
          <Button variant="ghost" className="text-primary hover:text-primary/80 p-0">
            Ver todos
            <ChevronRight className="h-4 w-4 ml-1" />
          </Button>
        </div>

        <div className="flex gap-4 overflow-x-auto pb-4 scrollbar-hide">
          {featuredListings.map((listing) => (
            <Link key={listing.id} href={`/anuncio/${listing.id}`} className="flex-shrink-0">
              <Card className="w-64 hover:shadow-md transition-shadow duration-200 border border-gray-200">
                <div className="relative">
                  <img
                    src={listing.image || "/placeholder.svg"}
                    alt={listing.title}
                    className="w-full h-40 object-cover rounded-t-lg"
                  />
                  <Button
                    variant="ghost"
                    size="sm"
                    className="absolute top-2 right-2 bg-white/80 hover:bg-white rounded-full p-1.5 h-8 w-8"
                  >
                    <Heart className="h-4 w-4 text-gray-600" />
                  </Button>
                  {listing.featured && (
                    <Badge className="absolute top-2 left-2 bg-primary text-primary-foreground text-xs px-2 py-1">
                      Destaque
                    </Badge>
                  )}
                </div>

                <CardContent className="p-3">
                  <h3 className="font-medium text-sm text-gray-800 mb-2 line-clamp-2 leading-tight">{listing.title}</h3>

                  <div className="text-lg font-bold text-gray-900 mb-2">{listing.price}</div>

                  <div className="space-y-1 text-xs text-gray-500">
                    <div className="flex items-center">
                      <span>{listing.date}</span>
                    </div>
                    <div className="flex items-center">
                      <span>{listing.location}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </Link>
          ))}
        </div>

        <div className="text-center mt-6 md:hidden">
          <Button variant="outline" className="text-primary border-primary bg-transparent">
            Ver todos os anúncios
          </Button>
        </div>
      </div>
    </section>
  )
}
