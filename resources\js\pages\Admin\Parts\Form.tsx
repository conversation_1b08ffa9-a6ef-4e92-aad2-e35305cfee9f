import { useEffect, useState } from 'react';
import { Head, Link, useForm, usePage } from '@inertiajs/react';
import type { PageProps as InertiaPageProps } from '@inertiajs/core';
import { ArrowLeft, Save, AlertCircle } from 'lucide-react';

// Layout and components
import AdminLayout from '@/components/Layout/AdminLayout';
import { FormImageUpload } from '@/components/FormImageUpload';

// UI Components
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';

// Types
interface User {
    id: number;
    name: string;
    email: string;
    [key: string]: unknown;
}

interface PageProps extends InertiaPageProps {
    auth: {
        user: User;
    };
    errors: Record<string, string>;
    part?: Part;
    brands: Array<{ id: number; name: string }>;
    categories: Array<{ id: number; name: string }>;
    vehicles: Array<{ id: number; full_name: string }>;
    [key: string]: unknown;
}

interface MediaFile {
    id: number;
    file_name: string;
    original_url: string;
    size: number;
    mime_type: string;
    collection_name: string;
    created_at: string;
    updated_at: string;
}

interface Part {
    id?: number;
    user_id: number;
    category_id: number | null;
    brand_id: number | null;
    name: string;
    slug: string;
    part_number: string;
    description: string;
    price: number;
    promotional_price: number | null;
    stock_quantity: number;
    weight: number | null;
    dimensions: string;
    is_original: boolean;
    is_featured: boolean;
    status: string;
    seo_title: string;
    seo_description: string;
    seo_keywords: string;
    compatible_vehicles?: Array<{ id: number }>;
    media: MediaFile[];
    [key: string]: any;
}

interface ImageFile {
    id: string;
    file: File;
    preview: string;
    name: string;
    size: number;
    type: string;
}

export default function PartForm() {
    const { part, brands, categories, vehicles, errors } = usePage<PageProps>().props;
    const isEdit = !!part?.id;

    const [featuredImage, setFeaturedImage] = useState<ImageFile | null>(null);
    const [galleryImages, setGalleryImages] = useState<ImageFile[]>([]);
    const [existingImages, setExistingImages] = useState<MediaFile[]>(part?.media || []);
    const [existingFeaturedImage, setExistingFeaturedImage] = useState<MediaFile | null>(
        part?.media?.find((m: MediaFile) => m.collection_name === 'featured_image') || null
    );

    const { data, setData, post, put, processing, recentlySuccessful } = useForm<{
        category_id: number | null;
        brand_id: number | null;
        name: string;
        part_number: string;
        description: string;
        price: string;
        promotional_price: string;
        stock_quantity: string;
        weight: string;
        dimensions: string;
        is_original: boolean;
        is_featured: boolean;
        status: string;
        seo_title: string;
        seo_description: string;
        seo_keywords: string;
        compatible_vehicles: number[];
        featured_image: File | null;
        images: File[];
        _method?: string;
    }>({
        category_id: part?.category_id || null,
        brand_id: part?.brand_id || null,
        name: part?.name || '',
        part_number: part?.part_number || '',
        description: part?.description || '',
        price: part?.price ? part.price.toString() : '0',
        promotional_price: part?.promotional_price ? part.promotional_price.toString() : '',
        stock_quantity: part?.stock_quantity ? part.stock_quantity.toString() : '0',
        weight: part?.weight ? part.weight.toString() : '',
        dimensions: part?.dimensions || '',
        is_original: part?.is_original || false,
        is_featured: part?.is_featured || false,
        status: part?.status || 'draft',
        seo_title: part?.seo_title || '',
        seo_description: part?.seo_description || '',
        seo_keywords: part?.seo_keywords || '',
        compatible_vehicles: part?.compatible_vehicles?.map((v: any) => v.id) || [],
        featured_image: null,
        images: [],
        ...(isEdit && { _method: 'put' }),
    });

    useEffect(() => {
        if (part?.media) {
            setExistingImages(part.media.filter((m: MediaFile) => m.collection_name === 'images'));
            const featured = part.media.find((m: MediaFile) => m.collection_name === 'featured_image');
            if (featured) {
                setExistingFeaturedImage(featured);
            }
        }
    }, [part]);

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        
        const formData = new FormData();
        
        // Add all form data to FormData
        Object.keys(data).forEach(key => {
            const value = data[key as keyof typeof data];
            if (value !== null && value !== undefined) {
                if (Array.isArray(value)) {
                    value.forEach(item => formData.append(`${key}[]`, item));
                } else if (value instanceof File) {
                    formData.append(key, value);
                } else {
                    formData.append(key, String(value));
                }
            }
        });
        
        // Add featured image if it's a new file
        if (featuredImage) {
            formData.append('featured_image', featuredImage.file);
        }
        
        // Add gallery images if there are new ones
        galleryImages.forEach((image, index) => {
            formData.append(`images[${index}]`, image.file);
        });

        if (isEdit && part?.id) {
            put(route('admin.parts.update', part.id), formData);
        } else {
            post(route('admin.parts.store'), formData);
        }
    };

    const handleFeaturedImageChange = (file: File | null) => {
        if (file) {
            const imageFile = {
                id: URL.createObjectURL(file),
                file,
                preview: URL.createObjectURL(file),
                name: file.name,
                size: file.size,
                type: file.type,
            };
            setFeaturedImage(imageFile);
            setData('featured_image', file);
        } else {
            setFeaturedImage(null);
            setData('featured_image', null);
        }
    };

    const handleGalleryImagesChange = (files: File[]) => {
        const newImages = files.map(file => ({
            id: URL.createObjectURL(file),
            file,
            preview: URL.createObjectURL(file),
            name: file.name,
            size: file.size,
            type: file.type,
        }));
        
        setGalleryImages([...galleryImages, ...newImages]);
        setData('images', [...data.images, ...files]);
    };

    const removeGalleryImage = (index: number) => {
        const newImages = [...galleryImages];
        newImages.splice(index, 1);
        setGalleryImages(newImages);
        
        const newFiles = [...data.images];
        newFiles.splice(index, 1);
        setData('images', newFiles);
    };

    const removeExistingImage = (id: number) => {
        setExistingImages(prev => prev.filter(img => img.id !== id));
        // You might want to send a request to delete the image from the server as well
    };

    const removeExistingFeaturedImage = () => {
        setExistingFeaturedImage(null);
        // You might want to send a request to delete the featured image from the server as well
    };

    return (
        <AdminLayout>
            <Head title={isEdit ? 'Editar Peça' : 'Adicionar Peça'} />
            
            <div className="space-y-6">
                <div className="flex items-center justify-between">
                    <div>
                        <h2 className="text-2xl font-bold tracking-tight">
                            {isEdit ? 'Editar Peça' : 'Adicionar Nova Peça'}
                        </h2>
                        <p className="text-muted-foreground">
                            {isEdit 
                                ? 'Atualize as informações da peça.'
                                : 'Preencha os campos abaixo para adicionar uma nova peça.'}
                        </p>
                    </div>
                    <Button variant="outline" asChild>
                        <Link href={route('admin.parts.index')}>
                            <ArrowLeft className="mr-2 h-4 w-4" />
                            Voltar para a lista
                        </Link>
                    </Button>
                </div>

                <form onSubmit={handleSubmit} className="space-y-6">
                    <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
                        {/* Left column - Main information */}
                        <div className="lg:col-span-2 space-y-6">
                            <Card>
                                <CardHeader>
                                    <CardTitle>Informações da Peça</CardTitle>
                                    <CardDescription>
                                        Informações básicas sobre a peça.
                                    </CardDescription>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                                        <div className="space-y-2">
                                            <Label htmlFor="name">Nome da Peça *</Label>
                                            <Input
                                                id="name"
                                                value={data.name}
                                                onChange={(e) => setData('name', e.target.value)}
                                                placeholder="Ex: Disco de Freio Dianteiro"
                                            />
                                            {errors.name && (
                                                <p className="text-sm text-red-600">{errors.name}</p>
                                            )}
                                        </div>
                                        
                                        <div className="space-y-2">
                                            <Label htmlFor="part_number">Número da Peça</Label>
                                            <Input
                                                id="part_number"
                                                value={data.part_number}
                                                onChange={(e) => setData('part_number', e.target.value)}
                                                placeholder="Ex: DF12345"
                                            />
                                            {errors.part_number && (
                                                <p className="text-sm text-red-600">{errors.part_number}</p>
                                            )}
                                        </div>
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="description">Descrição</Label>
                                        <Textarea
                                            id="description"
                                            value={data.description}
                                            onChange={(e) => setData('description', e.target.value)}
                                            rows={4}
                                            placeholder="Forneça uma descrição detalhada da peça..."
                                        />
                                        {errors.description && (
                                            <p className="text-sm text-red-600">{errors.description}</p>
                                        )}
                                    </div>

                                    <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
                                        <div className="space-y-2">
                                            <Label htmlFor="price">Preço *</Label>
                                            <div className="relative">
                                                <span className="absolute left-3 top-2.5 text-muted-foreground">R$</span>
                                                <Input
                                                    id="price"
                                                    type="number"
                                                    step="0.01"
                                                    min="0"
                                                    value={data.price}
                                                    onChange={(e) => setData('price', e.target.value)}
                                                    className="pl-8"
                                                />
                                            </div>
                                            {errors.price && (
                                                <p className="text-sm text-red-600">{errors.price}</p>
                                            )}
                                        </div>
                                        
                                        <div className="space-y-2">
                                            <Label htmlFor="promotional_price">Preço Promocional</Label>
                                            <div className="relative">
                                                <span className="absolute left-3 top-2.5 text-muted-foreground">R$</span>
                                                <Input
                                                    id="promotional_price"
                                                    type="number"
                                                    step="0.01"
                                                    min="0"
                                                    value={data.promotional_price}
                                                    onChange={(e) => setData('promotional_price', e.target.value)}
                                                    className="pl-8"
                                                />
                                            </div>
                                            {errors.promotional_price && (
                                                <p className="text-sm text-red-600">{errors.promotional_price}</p>
                                            )}
                                        </div>
                                        
                                        <div className="space-y-2">
                                            <Label htmlFor="stock_quantity">Quantidade em Estoque *</Label>
                                            <Input
                                                id="stock_quantity"
                                                type="number"
                                                min="0"
                                                value={data.stock_quantity}
                                                onChange={(e) => setData('stock_quantity', e.target.value)}
                                            />
                                            {errors.stock_quantity && (
                                                <p className="text-sm text-red-600">{errors.stock_quantity}</p>
                                            )}
                                        </div>
                                    </div>

                                    <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
                                        <div className="space-y-2">
                                            <Label htmlFor="weight">Peso (kg)</Label>
                                            <div className="relative">
                                                <Input
                                                    id="weight"
                                                    type="number"
                                                    step="0.001"
                                                    min="0"
                                                    value={data.weight}
                                                    onChange={(e) => setData('weight', e.target.value)}
                                                />
                                                <span className="absolute right-3 top-2.5 text-muted-foreground">kg</span>
                                            </div>
                                            {errors.weight && (
                                                <p className="text-sm text-red-600">{errors.weight}</p>
                                            )}
                                        </div>
                                        
                                        <div className="space-y-2">
                                            <Label htmlFor="dimensions">Dimensões (CxLxA)</Label>
                                            <Input
                                                id="dimensions"
                                                value={data.dimensions}
                                                onChange={(e) => setData('dimensions', e.target.value)}
                                                placeholder="Ex: 20x15x10"
                                            />
                                            {errors.dimensions && (
                                                <p className="text-sm text-red-600">{errors.dimensions}</p>
                                            )}
                                        </div>
                                        
                                        <div className="space-y-2">
                                            <Label htmlFor="status">Status *</Label>
                                            <Select
                                                value={data.status}
                                                onValueChange={(value) => setData('status', value)}
                                            >
                                                <SelectTrigger>
                                                    <SelectValue placeholder="Selecione um status" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem value="draft">Rascunho</SelectItem>
                                                    <SelectItem value="active">Ativo</SelectItem>
                                                    <SelectItem value="inactive">Inativo</SelectItem>
                                                    <SelectItem value="out_of_stock">Fora de Estoque</SelectItem>
                                                </SelectContent>
                                            </Select>
                                            {errors.status && (
                                                <p className="text-sm text-red-600">{errors.status}</p>
                                            )}
                                        </div>
                                    </div>

                                    <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
                                        <div className="space-y-2">
                                            <Label htmlFor="category_id">Categoria *</Label>
                                            <Select
                                                value={data.category_id?.toString() || ''}
                                                onValueChange={(value) => setData('category_id', parseInt(value) || null)}
                                            >
                                                <SelectTrigger>
                                                    <SelectValue placeholder="Selecione uma categoria" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    {categories.map((category) => (
                                                        <SelectItem key={category.id} value={category.id.toString()}>
                                                            {category.name}
                                                        </SelectItem>
                                                    ))}
                                                </SelectContent>
                                            </Select>
                                            {errors.category_id && (
                                                <p className="text-sm text-red-600">{errors.category_id}</p>
                                            )}
                                        </div>
                                        
                                        <div className="space-y-2">
                                            <Label htmlFor="brand_id">Marca *</Label>
                                            <Select
                                                value={data.brand_id?.toString() || ''}
                                                onValueChange={(value) => setData('brand_id', parseInt(value) || null)}
                                            >
                                                <SelectTrigger>
                                                    <SelectValue placeholder="Selecione uma marca" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    {brands.map((brand) => (
                                                        <SelectItem key={brand.id} value={brand.id.toString()}>
                                                            {brand.name}
                                                        </SelectItem>
                                                    ))}
                                                </SelectContent>
                                            </Select>
                                            {errors.brand_id && (
                                                <p className="text-sm text-red-600">{errors.brand_id}</p>
                                            )}
                                        </div>
                                    </div>

                                    <div className="space-y-2">
                                        <Label>Compatibilidade com Veículos</Label>
                                        <Select
                                            value=""
                                            onValueChange={(value) => {
                                                if (value && !data.compatible_vehicles.includes(parseInt(value))) {
                                                    setData('compatible_vehicles', [
                                                        ...data.compatible_vehicles,
                                                        parseInt(value)
                                                    ]);
                                                }
                                            }}
                                        >
                                            <SelectTrigger>
                                                <SelectValue placeholder="Selecione veículos compatíveis" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                {vehicles.map((vehicle) => (
                                                    <SelectItem 
                                                        key={vehicle.id} 
                                                        value={vehicle.id.toString()}
                                                        disabled={data.compatible_vehicles.includes(vehicle.id)}
                                                    >
                                                        {vehicle.full_name}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                        
                                        {data.compatible_vehicles.length > 0 && (
                                            <div className="mt-2 flex flex-wrap gap-2">
                                                {data.compatible_vehicles.map((vehicleId) => {
                                                    const vehicle = vehicles.find(v => v.id === vehicleId);
                                                    return vehicle ? (
                                                        <div 
                                                            key={vehicleId}
                                                            className="flex items-center bg-muted px-2 py-1 rounded-md text-sm"
                                                        >
                                                            {vehicle.full_name}
                                                            <button
                                                                type="button"
                                                                className="ml-2 text-muted-foreground hover:text-foreground"
                                                                onClick={() => {
                                                                    setData('compatible_vehicles', 
                                                                        data.compatible_vehicles.filter(id => id !== vehicleId)
                                                                    );
                                                                }}
                                                            >
                                                                ×
                                                            </button>
                                                        </div>
                                                    ) : null;
                                                })}
                                            </div>
                                        )}
                                        {errors.compatible_vehicles && (
                                            <p className="text-sm text-red-600">{errors.compatible_vehicles}</p>
                                        )}
                                    </div>
                                </CardContent>
                            </Card>

                            <Card>
                                <CardHeader>
                                    <CardTitle>SEO</CardTitle>
                                    <CardDescription>
                                        Otimize como esta peça aparece nos mecanismos de busca.
                                    </CardDescription>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="space-y-2">
                                        <Label htmlFor="seo_title">Título SEO</Label>
                                        <Input
                                            id="seo_title"
                                            value={data.seo_title}
                                            onChange={(e) => setData('seo_title', e.target.value)}
                                            placeholder="Deixe em branco para usar o nome da peça"
                                        />
                                        {errors.seo_title && (
                                            <p className="text-sm text-red-600">{errors.seo_title}</p>
                                        )}
                                    </div>
                                    
                                    <div className="space-y-2">
                                        <Label htmlFor="seo_description">Descrição SEO</Label>
                                        <Textarea
                                            id="seo_description"
                                            value={data.seo_description}
                                            onChange={(e) => setData('seo_description', e.target.value)}
                                            rows={2}
                                            placeholder="Deixe em branco para gerar automaticamente"
                                        />
                                        {errors.seo_description && (
                                            <p className="text-sm text-red-600">{errors.seo_description}</p>
                                        )}
                                    </div>
                                    
                                    <div className="space-y-2">
                                        <Label htmlFor="seo_keywords">Palavras-chave</Label>
                                        <Input
                                            id="seo_keywords"
                                            value={data.seo_keywords}
                                            onChange={(e) => setData('seo_keywords', e.target.value)}
                                            placeholder="palavra1, palavra2, palavra3"
                                        />
                                        <p className="text-xs text-muted-foreground">
                                            Separe as palavras-chave por vírgula.
                                        </p>
                                        {errors.seo_keywords && (
                                            <p className="text-sm text-red-600">{errors.seo_keywords}</p>
                                        )}
                                    </div>
                                </CardContent>
                            </Card>
                        </div>

                        {/* Right column - Images and options */}
                        <div className="space-y-6">
                            <Card>
                                <CardHeader>
                                    <CardTitle>Imagem de Destaque</CardTitle>
                                    <CardDescription>
                                        Imagem principal da peça. Tamanho recomendado: 800x800px.
                                    </CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <FormImageUpload
                                        id="featured_image"
                                        image={featuredImage}
                                        existingImage={existingFeaturedImage}
                                        onChange={handleFeaturedImageChange}
                                        onRemove={removeExistingFeaturedImage}
                                        error={errors.featured_image}
                                    />
                                </CardContent>
                            </Card>

                            <Card>
                                <CardHeader>
                                    <CardTitle>Galeria de Imagens</CardTitle>
                                    <CardDescription>
                                        Adicione até 10 imagens adicionais da peça.
                                    </CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <div className="space-y-4">
                                        <FormImageUpload
                                            id="gallery_images"
                                            multiple
                                            images={galleryImages}
                                            existingImages={existingImages}
                                            onChangeMultiple={handleGalleryImagesChange}
                                            onRemove={removeGalleryImage}
                                            onRemoveExisting={removeExistingImage}
                                            maxFiles={10 - (existingImages?.length || 0)}
                                            error={errors.images}
                                        />
                                        
                                        {existingImages && existingImages.length > 0 && (
                                            <div className="mt-4">
                                                <h4 className="text-sm font-medium mb-2">Imagens existentes</h4>
                                                <div className="grid grid-cols-3 gap-2">
                                                    {existingImages.map((image) => (
                                                        <div key={image.id} className="relative group">
                                                            <img
                                                                src={image.original_url}
                                                                alt={image.file_name}
                                                                className="h-20 w-full object-cover rounded-md"
                                                            />
                                                            <button
                                                                type="button"
                                                                onClick={() => removeExistingImage(image.id)}
                                                                className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                                                                title="Remover imagem"
                                                            >
                                                                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                                                                    <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                                                                </svg>
                                                            </button>
                                                        </div>
                                                    ))}
                                                </div>
                                            </div>
                                        )}
                                    </div>
                                </CardContent>
                            </Card>

                            <Card>
                                <CardHeader>
                                    <CardTitle>Opções</CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="flex items-center justify-between">
                                        <div className="space-y-0.5">
                                            <Label htmlFor="is_original">Peça Original</Label>
                                            <p className="text-xs text-muted-foreground">
                                                Esta peça é original de fábrica?
                                            </p>
                                        </div>
                                        <Switch
                                            id="is_original"
                                            checked={data.is_original}
                                            onCheckedChange={(checked) => setData('is_original', checked)}
                                        />
                                    </div>

                                    <div className="flex items-center justify-between">
                                        <div className="space-y-0.5">
                                            <Label htmlFor="is_featured">Destaque</Label>
                                            <p className="text-xs text-muted-foreground">
                                                Destacar esta peça na página inicial?
                                            </p>
                                        </div>
                                        <Switch
                                            id="is_featured"
                                            checked={data.is_featured}
                                            onCheckedChange={(checked) => setData('is_featured', checked)}
                                        />
                                    </div>
                                </CardContent>
                            </Card>

                            <div className="sticky bottom-0 bg-background pt-4 pb-6 border-t">
                                <div className="flex justify-between items-center">
                                    <Button type="button" variant="outline" asChild>
                                        <Link href={route('admin.parts.index')}>
                                            Cancelar
                                        </Link>
                                    </Button>
                                    
                                    <div className="flex items-center space-x-2">
                                        {isEdit && (
                                            <Button
                                                type="button"
                                                variant="outline"
                                                onClick={() => {
                                                    // Handle save as draft
                                                    setData('status', 'draft');
                                                }}
                                                disabled={processing}
                                            >
                                                Salvar como Rascunho
                                            </Button>
                                        )}
                                        
                                        <Button type="submit" disabled={processing}>
                                            {processing ? (
                                                <>
                                                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                                    </svg>
                                                    Salvando...
                                                </>
                                            ) : (
                                                <>
                                                    <Save className="mr-2 h-4 w-4" />
                                                    {isEdit ? 'Atualizar Peça' : 'Cadastrar Peça'}
                                                </>
                                            )}
                                        </Button>
                                    </div>
                                </div>
                                
                                {recentlySuccessful && (
                                    <div className="mt-4 p-3 bg-green-50 text-green-700 rounded-md text-sm flex items-center">
                                        <svg className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                        </svg>
                                        {isEdit ? 'Peça atualizada com sucesso!' : 'Peça cadastrada com sucesso!'}
                                    </div>
                                )}
                                
                                {Object.keys(errors).length > 0 && (
                                    <div className="mt-4 p-3 bg-red-50 text-red-700 rounded-md text-sm flex items-start">
                                        <AlertCircle className="h-4 w-4 mr-2 mt-0.5 flex-shrink-0" />
                                        <div>
                                            <p className="font-medium">
                                                Ocorreram {Object.keys(errors).length} erro(s) ao processar o formulário:
                                            </p>
                                            <ul className="list-disc list-inside mt-1">
                                                {Object.entries(errors).map(([field, error]) => (
                                                    <li key={field}>
                                                        {error}
                                                    </li>
                                                ))}
                                            </ul>
                                        </div>
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </AdminLayout>
    );
}
