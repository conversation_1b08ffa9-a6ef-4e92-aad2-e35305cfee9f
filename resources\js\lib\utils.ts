import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
    return twMerge(clsx(inputs));
}

/**
 * Formata um valor numérico para o formato de moeda brasileira (R$)
 * @param value - Valor numérico a ser formatado
 * @returns String formatada no padrão de moeda brasileira
 */
export function formatCurrency(value: number | string): string {
    // Converte para número se for string
    const numberValue = typeof value === 'string' ? parseFloat(value) : value;
    
    // Verifica se o valor é um número válido
    if (isNaN(numberValue)) return 'R$ 0,00';
    
    // Formata o valor para o padrão brasileiro
    return new Intl.NumberFormat('pt-BR', {
        style: 'currency',
        currency: 'BRL',
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }).format(numberValue);
}

/**
 * Formata uma data para o formato brasileiro
 * @param date - Data em formato string, número ou objeto Date
 * @returns String formatada no padrão brasileiro (dd/mm/aaaa)
 */
export function formatDate(date: string | number | Date | null | undefined): string {
    if (!date) return 'Data não informada';
    
    const dateObj = new Date(date);
    
    // Verifica se a data é válida
    if (isNaN(dateObj.getTime())) return 'Data inválida';
    
    return dateObj.toLocaleDateString('pt-BR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        hour12: false
    });
}
