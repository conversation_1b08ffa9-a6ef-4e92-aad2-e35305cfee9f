<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreVehicleRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return $this->user()->hasPermissionTo('vehicles.create');
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'category_id' => ['required', 'exists:categories,id'],
            'brand_id' => ['required', 'exists:brands,id'],
            'model' => ['required', 'string', 'max:255'],
            'year_manufacture' => ['required', 'integer', 'min:1900', 'max:' . (date('Y') + 1)],
            'model_year' => ['required', 'integer', 'min:1900', 'max:' . (date('Y') + 2)],
            'license_plate' => ['nullable', 'string', 'max:20', 'unique:vehicles,license_plate'],
            'chassis_number' => ['nullable', 'string', 'max:50', 'unique:vehicles,chassis_number'],
            'color' => ['nullable', 'string', 'max:50'],
            'fuel_type' => ['required', Rule::in(['gasoline', 'ethanol', 'flex', 'diesel', 'electric', 'hybrid'])],
            'transmission' => ['required', Rule::in(['manual', 'automatic', 'semi_automatic', 'cvt'])],
            'mileage' => ['required', 'integer', 'min:0'],
            'description' => ['required', 'string', 'min:50'],
            'price' => ['required', 'numeric', 'min:0'],
            'promotional_price' => ['nullable', 'numeric', 'min:0', 'lt:price'],
            'is_negotiable' => ['boolean'],
            'is_featured' => ['boolean'],
            'status' => ['nullable', Rule::in(['draft', 'published'])],
            'images' => ['required', 'array', 'min:1', 'max:20'],
            'images.*' => ['image', 'mimes:jpeg,png,webp', 'max:5120'], // 5MB max per image
            'featured_image' => ['nullable', 'image', 'mimes:jpeg,png,webp', 'max:5120'],
            'seo_title' => ['nullable', 'string', 'max:60'],
            'seo_description' => ['nullable', 'string', 'max:160'],
            'seo_keywords' => ['nullable', 'string', 'max:255'],
        ];
    }

    /**
     * Get the custom error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'category_id.required' => 'A categoria é obrigatória.',
            'category_id.exists' => 'A categoria selecionada não existe.',
            'brand_id.required' => 'A marca é obrigatória.',
            'brand_id.exists' => 'A marca selecionada não existe.',
            'model.required' => 'O modelo é obrigatório.',
            'year_manufacture.required' => 'O ano de fabricação é obrigatório.',
            'year_manufacture.min' => 'O ano de fabricação mínimo é 1900.',
            'year_manufacture.max' => 'O ano de fabricação não pode ser maior que ' . (date('Y') + 1) . '.',
            'model_year.required' => 'O ano do modelo é obrigatório.',
            'model_year.min' => 'O ano do modelo mínimo é 1900.',
            'model_year.max' => 'O ano do modelo não pode ser maior que ' . (date('Y') + 2) . '.',
            'license_plate.unique' => 'Esta placa já está em uso.',
            'chassis_number.unique' => 'Este número de chassi já está em uso.',
            'fuel_type.required' => 'O tipo de combustível é obrigatório.',
            'fuel_type.in' => 'O tipo de combustível selecionado é inválido.',
            'transmission.required' => 'O tipo de transmissão é obrigatório.',
            'transmission.in' => 'O tipo de transmissão selecionado é inválido.',
            'mileage.required' => 'A quilometragem é obrigatória.',
            'mileage.min' => 'A quilometragem não pode ser negativa.',
            'description.required' => 'A descrição é obrigatória.',
            'description.min' => 'A descrição deve ter pelo menos 50 caracteres.',
            'price.required' => 'O preço é obrigatório.',
            'price.min' => 'O preço não pode ser negativo.',
            'promotional_price.lt' => 'O preço promocional deve ser menor que o preço normal.',
            'images.required' => 'É necessário enviar pelo menos uma imagem.',
            'images.min' => 'É necessário enviar pelo menos uma imagem.',
            'images.max' => 'É permitido enviar no máximo 20 imagens.',
            'images.*.image' => 'O arquivo deve ser uma imagem.',
            'images.*.mimes' => 'A imagem deve ser do tipo: jpeg, png ou webp.',
            'images.*.max' => 'Cada imagem pode ter no máximo 5MB.',
            'featured_image.image' => 'O arquivo deve ser uma imagem.',
            'featured_image.mimes' => 'A imagem destacada deve ser do tipo: jpeg, png ou webp.',
            'featured_image.max' => 'A imagem destacada pode ter no máximo 5MB.',
        ];
    }
}
