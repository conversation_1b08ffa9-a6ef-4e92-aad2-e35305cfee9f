<?php

namespace App\Http\Controllers\Public;

use App\Http\Controllers\Controller;
use App\Models\Vehicle;
use App\Models\Brand;
use App\Models\Category;
use Illuminate\Http\Request;
use Inertia\Inertia;

class VehicleController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $vehicles = Vehicle::with(['brand', 'category', 'mainImage'])
            ->where('status', 'published')
            ->latest()
            ->filter($request->only('search', 'brand', 'category', 'min_price', 'max_price', 'fuel_type', 'transmission'))
            ->paginate(12)
            ->withQueryString();

        $brands = Brand::whereHas('vehicles', function ($query) {
            $query->where('status', 'published');
        })->get(['id', 'name']);

        $categories = Category::whereHas('vehicles', function ($query) {
            $query->where('status', 'published');
        })->get(['id', 'name']);

        return Inertia::render('Vehicles/Index', [
            'vehicles' => $vehicles,
            'filters' => $request->all('search', 'brand', 'category', 'min_price', 'max_price', 'fuel_type', 'transmission'),
            'brands' => $brands,
            'categories' => $categories,
            'fuelTypes' => [
                'gasoline' => 'Gasolina',
                'ethanol' => 'Álcool',
                'flex' => 'Flex',
                'diesel' => 'Diesel',
                'electric' => 'Elétrico',
                'hybrid' => 'Híbrido',
            ],
            'transmissions' => [
                'manual' => 'Manual',
                'automatic' => 'Automático',
                'semi_automatic' => 'Semi-automático',
                'cvt' => 'CVT',
            ],
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show($slug)
    {
        $vehicle = Vehicle::with(['brand', 'category', 'user', 'features', 'images'])
            ->where('slug', $slug)
            ->where('status', 'published')
            ->firstOrFail();
        
        // Incrementa a contagem de visualizações
        $vehicle->increment('views');
        
        // Veículos relacionados
        $relatedVehicles = Vehicle::where('category_id', $vehicle->category_id)
            ->where('id', '!=', $vehicle->id)
            ->where('status', 'published')
            ->with(['brand', 'category', 'mainImage'])
            ->take(4)
            ->get();
        
        return Inertia::render('Vehicles/Show', [
            'vehicle' => $vehicle,
            'relatedVehicles' => $relatedVehicles
        ]);
    }
}
