import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { Calendar, MapPin, Gauge, Fuel, Palette, Car, Cog, Eye, Heart, Share2 } from "lucide-react"

interface VehicleDetailsProps {
  vehicle: {
    title: string
    price: string
    location: string
    year: string
    fuel: string
    mileage: string
    transmission: string
    color: string
    doors: string
    engine: string
    condition: string
    type: string
    brand: string
    model: string
    version: string
    description: string
    features: string[]
    createdAt: string
    views: number
    favorites: number
  }
}

export function VehicleDetails({ vehicle }: VehicleDetailsProps) {
  const specifications = [
    { icon: Calendar, label: "Ano", value: vehicle.year },
    { icon: Gauge, label: "Quilometragem", value: vehicle.mileage },
    { icon: Fuel, label: "Combustível", value: vehicle.fuel },
    { icon: Cog, label: "Transmissão", value: vehicle.transmission },
    { icon: Palette, label: "Cor", value: vehicle.color },
    { icon: Car, label: "Portas", value: vehicle.doors },
    { icon: Cog, label: "Motor", value: vehicle.engine },
    { icon: MapPin, label: "Localização", value: vehicle.location },
  ]

  return (
    <div className="space-y-6">
      {/* Title and Price */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-balance">{vehicle.title}</h1>
          <div className="flex items-center gap-2 mt-2 text-muted-foreground">
            <Eye className="h-4 w-4" />
            <span>{vehicle.views.toLocaleString()} visualizações</span>
            <span>•</span>
            <Heart className="h-4 w-4" />
            <span>{vehicle.favorites} favoritos</span>
          </div>
        </div>
        <div className="flex items-center gap-3">
          <div className="text-right">
            <div className="text-3xl font-bold text-primary">{vehicle.price}</div>
            <Badge variant="secondary">{vehicle.condition}</Badge>
          </div>
          <div className="flex flex-col gap-2">
            <Button variant="outline" size="sm">
              <Heart className="h-4 w-4" />
            </Button>
            <Button variant="outline" size="sm">
              <Share2 className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Specifications */}
      <Card>
        <CardHeader>
          <CardTitle>Especificações</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {specifications.map((spec, index) => {
              const Icon = spec.icon
              return (
                <div key={index} className="flex items-center space-x-3">
                  <Icon className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <div className="text-sm text-muted-foreground">{spec.label}</div>
                    <div className="font-medium">{spec.value}</div>
                  </div>
                </div>
              )
            })}
          </div>
        </CardContent>
      </Card>

      {/* Description */}
      <Card>
        <CardHeader>
          <CardTitle>Descrição</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground leading-relaxed">{vehicle.description}</p>
        </CardContent>
      </Card>

      {/* Features */}
      <Card>
        <CardHeader>
          <CardTitle>Equipamentos e Opcionais</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
            {vehicle.features.map((feature, index) => (
              <div key={index} className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-primary rounded-full"></div>
                <span className="text-sm">{feature}</span>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Additional Info */}
      <Card>
        <CardHeader>
          <CardTitle>Informações Adicionais</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex justify-between items-center">
            <span className="text-muted-foreground">Anúncio publicado em:</span>
            <span>{new Date(vehicle.createdAt).toLocaleDateString("pt-BR")}</span>
          </div>
          <Separator />
          <div className="flex justify-between items-center">
            <span className="text-muted-foreground">Código do anúncio:</span>
            <span className="font-mono">
              #{vehicle.brand.toUpperCase()}
              {vehicle.year}001
            </span>
          </div>
          <Separator />
          <div className="flex justify-between items-center">
            <span className="text-muted-foreground">Categoria:</span>
            <Badge variant="outline">{vehicle.type}</Badge>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
