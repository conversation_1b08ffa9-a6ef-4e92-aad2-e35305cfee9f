import { Button } from '@/components/ui/button';
import {
    Card,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { router, useForm } from '@inertiajs/react';
import { Image as ImageIcon, Loader2, X } from 'lucide-react';
import { FormEvent, useEffect, useState } from 'react';
import { toast } from 'sonner';

interface Vehicle {
    id: number;
    brand: {
        id: number;
        name: string;
    };
    model: string;
    year_manufacture: number;
    price: number;
}

interface User {
    id: number;
    name: string;
    email: string;
}

interface AdvertisementFormProps {
    advertisement?: {
        id?: number;
        title: string;
        description: string;
        vehicle_id: number;
        user_id: number;
        status: string;
        price: number;
        is_negotiable: boolean;
        is_featured: boolean;
        contact_phone: string;
        contact_email: string;
        location: string;
        latitude: number | null;
        longitude: number | null;
        published_at: string | null;
        expires_at: string | null;
        rejection_reason: string | null;
        featured_image_url?: string;
        images?: Array<{ id: number; original_url: string }>;
    };
    vehicles: Vehicle[];
    users: User[];
    statuses?: Record<string, string>;
    isEdit?: boolean;
}

export default function AdvertisementForm({
    advertisement,
    vehicles,
    users,
    statuses = {},
    isEdit = false,
}: AdvertisementFormProps) {
    const [selectedVehicle, setSelectedVehicle] = useState<Vehicle | null>(
        vehicles.find((v) => v.id === advertisement?.vehicle_id) || null,
    );
    const [featuredImage, setFeaturedImage] = useState<File | null>(null);
    const [featuredImagePreview, setFeaturedImagePreview] = useState<
        string | null
    >(advertisement?.featured_image_url || null);
    const [images, setImages] = useState<File[]>([]);
    const [imagePreviews, setImagePreviews] = useState<string[]>(
        advertisement?.images?.map((img) => img.original_url) || [],
    );
    const [isSubmitting, setIsSubmitting] = useState(false);

    const { data, setData, post, put, errors, processing } = useForm({
        title: advertisement?.title || '',
        description: advertisement?.description || '',
        vehicle_id: advertisement?.vehicle_id || '',
        user_id: advertisement?.user_id || '',
        status: advertisement?.status || 'draft',
        price: advertisement?.price
            ? (advertisement.price / 100).toFixed(2)
            : '',
        is_negotiable: advertisement?.is_negotiable || false,
        is_featured: advertisement?.is_featured || false,
        contact_phone: advertisement?.contact_phone || '',
        contact_email: advertisement?.contact_email || '',
        location: advertisement?.location || '',
        latitude: String(advertisement?.latitude || ''),
        longitude: String(advertisement?.longitude || ''),
        published_at: advertisement?.published_at || '',
        expires_at: advertisement?.expires_at || '',
        rejection_reason: advertisement?.rejection_reason || '',
    });

    useEffect(() => {
        if (data.vehicle_id) {
            const vehicle = vehicles.find(
                (v) => v.id === Number(data.vehicle_id),
            );
            if (vehicle) {
                setSelectedVehicle(vehicle);
                setData('price', (vehicle.price / 100).toFixed(2));
            }
        }
    }, [data.vehicle_id, vehicles]);

    const handleFeaturedImageChange = (
        e: React.ChangeEvent<HTMLInputElement>,
    ) => {
        if (e.target.files && e.target.files[0]) {
            const file = e.target.files[0];
            setFeaturedImage(file);
            setFeaturedImagePreview(URL.createObjectURL(file));
        }
    };

    const handleImagesChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        if (e.target.files) {
            const newFiles = Array.from(e.target.files);
            setImages([...images, ...newFiles]);

            const newPreviews = newFiles.map((file) =>
                URL.createObjectURL(file),
            );
            setImagePreviews([...imagePreviews, ...newPreviews]);
        }
    };

    const removeImage = (index: number) => {
        const newPreviews = [...imagePreviews];
        newPreviews.splice(index, 1);
        setImagePreviews(newPreviews);

        // If it's a new image (not from server), remove from files array
        if (index >= (advertisement?.images?.length || 0)) {
            const newImages = [...images];
            newImages.splice(index - (advertisement?.images?.length || 0), 1);
            setImages(newImages);
        }
        // TODO: Handle removing images from server in the backend
    };

    const handleSubmit = (e: FormEvent) => {
        e.preventDefault();
        setIsSubmitting(true);

        const formData = new FormData();

        // Add all form data
        Object.entries(data).forEach(([key, value]) => {
            if (value !== null && value !== undefined) {
                formData.append(key, String(value));
            }
        });

        // Add featured image if it's a new one
        if (featuredImage) {
            formData.append('featured_image', featuredImage);
        }

        // Add new images
        images.forEach((image, index) => {
            formData.append(`images[${index}]`, image);
        });

        // Convert price to cents
        if (formData.get('price')) {
            formData.set(
                'price',
                String(Math.round(Number(formData.get('price')) * 100)),
            );
        }

        const url =
            isEdit && advertisement?.id
                ? route('admin.advertisements.update', {
                      advertisement: advertisement.id,
                  })
                : route('admin.advertisements.store');

        const method = isEdit ? 'put' : 'post';

        router[method](url, formData, {
            onSuccess: () => {
                toast(isEdit ? 'Anúncio atualizado!' : 'Anúncio criado!', {
                    description: isEdit
                        ? 'As alterações foram salvas com sucesso.'
                        : 'O anúncio foi criado com sucesso.',
                });
            },
            onError: () => {
                toast('Erro', {
                    description:
                        'Ocorreu um erro ao salvar o anúncio. Por favor, tente novamente.',
                });
            },
            onFinish: () => {
                setIsSubmitting(false);
            },
            preserveScroll: true,
            forceFormData: true,
        });
    };

    return (
        <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
                <div className="space-y-6 lg:col-span-2">
                    <Card>
                        <CardHeader>
                            <CardTitle>Informações do Anúncio</CardTitle>
                            <CardDescription>
                                Preencha os detalhes do anúncio
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="space-y-2">
                                <Label htmlFor="title">Título do Anúncio</Label>
                                <Input
                                    id="title"
                                    value={data.title}
                                    onChange={(e) =>
                                        setData('title', e.target.value)
                                    }
                                    placeholder="Ex: BMW 320i 2.0 16V Turbo 4P"
                                />
                                {errors.title && (
                                    <p className="text-sm text-red-500">
                                        {errors.title}
                                    </p>
                                )}
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="description">Descrição</Label>
                                <Textarea
                                    id="description"
                                    value={data.description}
                                    onChange={(e) =>
                                        setData('description', e.target.value)
                                    }
                                    rows={5}
                                    placeholder="Forneça detalhes sobre o veículo, condições de venda, etc."
                                />
                                {errors.description && (
                                    <p className="text-sm text-red-500">
                                        {errors.description}
                                    </p>
                                )}
                            </div>

                            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                <div className="space-y-2">
                                    <Label htmlFor="vehicle_id">Veículo</Label>
                                    <Select
                                        value={String(data.vehicle_id)}
                                        onValueChange={(value) =>
                                            setData('vehicle_id', Number(value))
                                        }
                                    >
                                        <SelectTrigger>
                                            <SelectValue placeholder="Selecione um veículo" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {vehicles.map((vehicle) => (
                                                <SelectItem
                                                    key={vehicle.id}
                                                    value={String(vehicle.id)}
                                                >
                                                    {vehicle.brand.name}{' '}
                                                    {vehicle.model} (
                                                    {vehicle.year_manufacture})
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                    {errors.vehicle_id && (
                                        <p className="text-sm text-red-500">
                                            {errors.vehicle_id}
                                        </p>
                                    )}
                                </div>

                                {users.length > 0 && (
                                    <div className="space-y-2">
                                        <Label htmlFor="user_id">
                                            Anunciante
                                        </Label>
                                        <Select
                                            value={String(data.user_id)}
                                            onValueChange={(value) =>
                                                setData(
                                                    'user_id',
                                                    Number(value),
                                                )
                                            }
                                        >
                                            <SelectTrigger>
                                                <SelectValue placeholder="Selecione um anunciante" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                {users.map((user) => (
                                                    <SelectItem
                                                        key={user.id}
                                                        value={String(user.id)}
                                                    >
                                                        {user.name} (
                                                        {user.email})
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                        {errors.user_id && (
                                            <p className="text-sm text-red-500">
                                                {errors.user_id}
                                            </p>
                                        )}
                                    </div>
                                )}
                            </div>

                            <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                                <div className="space-y-2">
                                    <Label htmlFor="price">Preço (R$)</Label>
                                    <Input
                                        id="price"
                                        type="number"
                                        step="0.01"
                                        min="0"
                                        value={data.price}
                                        onChange={(e) =>
                                            setData('price', e.target.value)
                                        }
                                        placeholder="0,00"
                                    />
                                    {errors.price && (
                                        <p className="text-sm text-red-500">
                                            {errors.price}
                                        </p>
                                    )}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="status">Status</Label>
                                    <Select
                                        value={data.status}
                                        onValueChange={(value) =>
                                            setData('status', value)
                                        }
                                    >
                                        <SelectTrigger>
                                            <SelectValue placeholder="Selecione um status" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {Object.entries(statuses).map(
                                                ([key, label]) => (
                                                    <SelectItem
                                                        key={key}
                                                        value={key}
                                                    >
                                                        {label}
                                                    </SelectItem>
                                                ),
                                            )}
                                        </SelectContent>
                                    </Select>
                                    {errors.status && (
                                        <p className="text-sm text-red-500">
                                            {errors.status}
                                        </p>
                                    )}
                                </div>

                                <div className="flex items-end space-x-4 pt-2">
                                    <div className="flex items-center space-x-2">
                                        <Switch
                                            id="is_negotiable"
                                            checked={data.is_negotiable}
                                            onCheckedChange={(checked) =>
                                                setData(
                                                    'is_negotiable',
                                                    checked,
                                                )
                                            }
                                        />
                                        <Label htmlFor="is_negotiable">
                                            Preço Negociável
                                        </Label>
                                    </div>

                                    <div className="flex items-center space-x-2">
                                        <Switch
                                            id="is_featured"
                                            checked={data.is_featured}
                                            onCheckedChange={(checked) =>
                                                setData('is_featured', checked)
                                            }
                                        />
                                        <Label htmlFor="is_featured">
                                            Destaque
                                        </Label>
                                    </div>
                                </div>
                            </div>

                            {data.status === 'rejected' && (
                                <div className="space-y-2">
                                    <Label htmlFor="rejection_reason">
                                        Motivo da Rejeição
                                    </Label>
                                    <Textarea
                                        id="rejection_reason"
                                        value={data.rejection_reason || ''}
                                        onChange={(e) =>
                                            setData(
                                                'rejection_reason',
                                                e.target.value,
                                            )
                                        }
                                        rows={2}
                                        placeholder="Informe o motivo da rejeição do anúncio"
                                    />
                                    {errors.rejection_reason && (
                                        <p className="text-sm text-red-500">
                                            {errors.rejection_reason}
                                        </p>
                                    )}
                                </div>
                            )}
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle>Imagens</CardTitle>
                            <CardDescription>
                                Adicione imagens do veículo
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="space-y-2">
                                <Label>Imagem de Destaque</Label>
                                <div className="flex items-center gap-4">
                                    <div className="relative h-32 w-32 overflow-hidden rounded-md border border-dashed border-gray-300">
                                        {featuredImagePreview ? (
                                            <>
                                                <img
                                                    src={featuredImagePreview}
                                                    alt="Preview"
                                                    className="h-full w-full object-cover"
                                                />
                                                <button
                                                    type="button"
                                                    className="absolute top-1 right-1 rounded-full bg-red-500 p-1 text-white"
                                                    onClick={() => {
                                                        setFeaturedImage(null);
                                                        setFeaturedImagePreview(
                                                            null,
                                                        );
                                                        // TODO: Add logic to remove from server if it's an existing image
                                                    }}
                                                >
                                                    <X className="h-3 w-3" />
                                                </button>
                                            </>
                                        ) : (
                                            <label
                                                htmlFor="featured_image"
                                                className="flex h-full w-full cursor-pointer items-center justify-center"
                                            >
                                                <div className="text-center">
                                                    <ImageIcon className="mx-auto h-8 w-8 text-gray-400" />
                                                    <span className="mt-1 text-xs text-gray-500">
                                                        Clique para adicionar
                                                    </span>
                                                </div>
                                                <input
                                                    id="featured_image"
                                                    name="featured_image"
                                                    type="file"
                                                    className="sr-only"
                                                    onChange={
                                                        handleFeaturedImageChange
                                                    }
                                                    accept="image/*"
                                                />
                                            </label>
                                        )}
                                    </div>
                                    <div className="text-sm text-gray-500">
                                        <p>
                                            Adicione uma imagem de destaque para
                                            o anúncio.
                                        </p>
                                        <p className="mt-1 text-xs">
                                            Tamanho recomendado: 800x600px
                                        </p>
                                    </div>
                                </div>
                                {(errors as any).featured_image && (
                                    <p className="text-sm text-red-500">
                                        {(errors as any).featured_image}
                                    </p>
                                )}
                            </div>

                            <div className="space-y-2">
                                <div className="flex items-center justify-between">
                                    <Label>Galeria de Imagens</Label>
                                    <span className="text-xs text-gray-500">
                                        {imagePreviews.length} de 10 imagens
                                    </span>
                                </div>

                                <div className="grid grid-cols-2 gap-4 sm:grid-cols-3 md:grid-cols-4">
                                    {imagePreviews.map((preview, index) => (
                                        <div
                                            key={index}
                                            className="group relative"
                                        >
                                            <div className="aspect-square overflow-hidden rounded-md border border-gray-200">
                                                <img
                                                    src={preview}
                                                    alt={`Preview ${index + 1}`}
                                                    className="h-full w-full object-cover"
                                                />
                                            </div>
                                            <button
                                                type="button"
                                                className="absolute -top-2 -right-2 rounded-full bg-red-500 p-1 text-white opacity-0 transition-opacity group-hover:opacity-100"
                                                onClick={() =>
                                                    removeImage(index)
                                                }
                                            >
                                                <X className="h-3 w-3" />
                                            </button>
                                        </div>
                                    ))}

                                    {imagePreviews.length < 10 && (
                                        <label
                                            htmlFor="images"
                                            className="flex aspect-square cursor-pointer items-center justify-center rounded-md border-2 border-dashed border-gray-300 transition-colors hover:border-blue-500"
                                        >
                                            <div className="p-2 text-center">
                                                <ImageIcon className="mx-auto h-6 w-6 text-gray-400" />
                                                <span className="mt-1 text-xs text-gray-500">
                                                    Adicionar Imagens
                                                </span>
                                                <input
                                                    id="images"
                                                    name="images"
                                                    type="file"
                                                    className="sr-only"
                                                    onChange={
                                                        handleImagesChange
                                                    }
                                                    accept="image/*"
                                                    multiple
                                                />
                                            </div>
                                        </label>
                                    )}
                                </div>
                                {(errors as any).images && (
                                    <p className="text-sm text-red-500">
                                        {(errors as any).images}
                                    </p>
                                )}
                            </div>
                        </CardContent>
                    </Card>
                </div>

                <div className="space-y-6">
                    <Card>
                        <CardHeader>
                            <CardTitle>Contato</CardTitle>
                            <CardDescription>
                                Informações de contato para o anúncio
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="space-y-2">
                                <Label htmlFor="contact_phone">
                                    Telefone para Contato
                                </Label>
                                <Input
                                    id="contact_phone"
                                    value={data.contact_phone}
                                    onChange={(e) =>
                                        setData('contact_phone', e.target.value)
                                    }
                                    placeholder="(00) 00000-0000"
                                />
                                {errors.contact_phone && (
                                    <p className="text-sm text-red-500">
                                        {errors.contact_phone}
                                    </p>
                                )}
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="contact_email">
                                    E-mail para Contato
                                </Label>
                                <Input
                                    id="contact_email"
                                    type="email"
                                    value={data.contact_email}
                                    onChange={(e) =>
                                        setData('contact_email', e.target.value)
                                    }
                                    placeholder="<EMAIL>"
                                />
                                {errors.contact_email && (
                                    <p className="text-sm text-red-500">
                                        {errors.contact_email}
                                    </p>
                                )}
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="location">Localização</Label>
                                <Input
                                    id="location"
                                    value={data.location}
                                    onChange={(e) =>
                                        setData('location', e.target.value)
                                    }
                                    placeholder="Cidade - Estado"
                                />
                                {errors.location && (
                                    <p className="text-sm text-red-500">
                                        {errors.location}
                                    </p>
                                )}
                            </div>

                            <div className="grid grid-cols-2 gap-4">
                                <div className="space-y-2">
                                    <Label htmlFor="latitude">Latitude</Label>
                                    <Input
                                        id="latitude"
                                        type="number"
                                        step="any"
                                        value={data.latitude || ''}
                                        onChange={(e) =>
                                            setData('latitude', e.target.value)
                                        }
                                        placeholder="-23.5505"
                                    />
                                    {errors.latitude && (
                                        <p className="text-sm text-red-500">
                                            {errors.latitude}
                                        </p>
                                    )}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="longitude">Longitude</Label>
                                    <Input
                                        id="longitude"
                                        type="number"
                                        step="any"
                                        value={data.longitude || ''}
                                        onChange={(e) =>
                                            setData('longitude', e.target.value)
                                        }
                                        placeholder="-46.6333"
                                    />
                                    {errors.longitude && (
                                        <p className="text-sm text-red-500">
                                            {errors.longitude}
                                        </p>
                                    )}
                                </div>
                            </div>

                            <div className="text-xs text-gray-500">
                                <p>
                                    As coordenadas são usadas para exibir a
                                    localização no mapa.
                                </p>
                                <p>
                                    Você pode usar o{' '}
                                    <a
                                        href="https://www.google.com/maps"
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        className="text-blue-500 hover:underline"
                                    >
                                        Google Maps
                                    </a>{' '}
                                    para obter as coordenadas.
                                </p>
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle>Publicação</CardTitle>
                            <CardDescription>
                                Configurações de publicação do anúncio
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="space-y-2">
                                <Label htmlFor="published_at">
                                    Data de Publicação
                                </Label>
                                <Input
                                    id="published_at"
                                    type="datetime-local"
                                    value={
                                        data.published_at
                                            ? new Date(data.published_at)
                                                  .toISOString()
                                                  .slice(0, 16)
                                            : ''
                                    }
                                    onChange={(e) =>
                                        setData('published_at', e.target.value)
                                    }
                                />
                                {errors.published_at && (
                                    <p className="text-sm text-red-500">
                                        {errors.published_at}
                                    </p>
                                )}
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="expires_at">
                                    Data de Expiração
                                </Label>
                                <Input
                                    id="expires_at"
                                    type="datetime-local"
                                    value={
                                        data.expires_at
                                            ? new Date(data.expires_at)
                                                  .toISOString()
                                                  .slice(0, 16)
                                            : ''
                                    }
                                    onChange={(e) =>
                                        setData('expires_at', e.target.value)
                                    }
                                />
                                {errors.expires_at && (
                                    <p className="text-sm text-red-500">
                                        {errors.expires_at}
                                    </p>
                                )}
                            </div>

                            <div className="pt-4">
                                <Button
                                    type="submit"
                                    className="w-full"
                                    disabled={processing || isSubmitting}
                                >
                                    {(processing || isSubmitting) && (
                                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                    )}
                                    {isEdit
                                        ? 'Atualizar Anúncio'
                                        : 'Criar Anúncio'}
                                </Button>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </form>
    );
}
