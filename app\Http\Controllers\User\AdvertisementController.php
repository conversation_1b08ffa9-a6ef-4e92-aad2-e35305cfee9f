<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Models\Advertisement;
use App\Models\Vehicle;
use App\Models\Brand;
use App\Models\Category;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;
use Illuminate\Support\Facades\Storage;

class AdvertisementController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Display a listing of user's advertisements
     */
    public function index(Request $request): Response
    {
        $query = Advertisement::with(['vehicle.brand', 'vehicle.category', 'featuredImage'])
            ->where('user_id', auth()->id());

        // Apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->input('status'));
        }

        if ($request->filled('search')) {
            $search = $request->input('search');
            $query->where(function($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        $advertisements = $query->latest()->paginate(12)->withQueryString();

        return Inertia::render('User/Advertisements/Index', [
            'advertisements' => $advertisements,
            'filters' => $request->all(),
        ]);
    }

    /**
     * Show the form for creating a new advertisement
     */
    public function create(): Response
    {
        $brands = Brand::orderBy('name')->get();
        $categories = Category::orderBy('name')->get();

        return Inertia::render('User/Advertisements/Create', [
            'brands' => $brands,
            'categories' => $categories,
        ]);
    }

    /**
     * Store a newly created advertisement
     */
    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string|max:5000',
            'price' => 'required|numeric|min:0',
            'location' => 'required|string|max:255',
            'contact_phone' => 'nullable|string|max:20',
            
            // Vehicle data
            'vehicle.brand_id' => 'required|exists:brands,id',
            'vehicle.category_id' => 'required|exists:categories,id',
            'vehicle.model' => 'required|string|max:255',
            'vehicle.year' => 'required|integer|min:1900|max:' . (date('Y') + 1),
            'vehicle.mileage' => 'required|integer|min:0',
            'vehicle.fuel_type' => 'required|string|in:gasoline,ethanol,flex,diesel,electric,hybrid',
            'vehicle.transmission' => 'required|string|in:manual,automatic,cvt',
            'vehicle.condition' => 'required|string|in:new,used,certified',
            'vehicle.color' => 'required|string|max:50',
            'vehicle.doors' => 'required|integer|min:2|max:5',
            'vehicle.engine_size' => 'nullable|string|max:20',
            
            // Images
            'images' => 'nullable|array|max:10',
            'images.*' => 'image|mimes:jpeg,png,jpg,webp|max:5120', // 5MB max
            'featured_image_index' => 'nullable|integer|min:0',
        ]);

        // Create vehicle
        $vehicle = Vehicle::create([
            'brand_id' => $request->input('vehicle.brand_id'),
            'category_id' => $request->input('vehicle.category_id'),
            'model' => $request->input('vehicle.model'),
            'year' => $request->input('vehicle.year'),
            'mileage' => $request->input('vehicle.mileage'),
            'fuel_type' => $request->input('vehicle.fuel_type'),
            'transmission' => $request->input('vehicle.transmission'),
            'condition' => $request->input('vehicle.condition'),
            'color' => $request->input('vehicle.color'),
            'doors' => $request->input('vehicle.doors'),
            'engine_size' => $request->input('vehicle.engine_size'),
        ]);

        // Create advertisement
        $advertisement = Advertisement::create([
            'user_id' => auth()->id(),
            'vehicle_id' => $vehicle->id,
            'title' => $request->input('title'),
            'description' => $request->input('description'),
            'price' => $request->input('price'),
            'location' => $request->input('location'),
            'contact_phone' => $request->input('contact_phone'),
            'status' => 'draft', // Start as draft
        ]);

        // Handle image uploads
        if ($request->hasFile('images')) {
            $images = [];
            foreach ($request->file('images') as $index => $image) {
                $path = $image->store('advertisements', 'public');
                
                $imageRecord = $advertisement->images()->create([
                    'url' => Storage::url($path),
                    'alt' => $advertisement->title,
                    'order' => $index,
                ]);

                $images[] = $imageRecord;
            }

            // Set featured image
            $featuredIndex = $request->input('featured_image_index', 0);
            if (isset($images[$featuredIndex])) {
                $advertisement->update(['featured_image_id' => $images[$featuredIndex]->id]);
            }
        }

        return redirect()->route('user.advertisements.show', $advertisement)
            ->with('success', 'Anúncio criado com sucesso!');
    }

    /**
     * Display the specified advertisement
     */
    public function show(Advertisement $advertisement): Response
    {
        // Check if user owns this advertisement
        if ($advertisement->user_id !== auth()->id()) {
            abort(403);
        }

        $advertisement->load(['vehicle.brand', 'vehicle.category', 'images', 'featuredImage']);

        return Inertia::render('User/Advertisements/Show', [
            'advertisement' => $advertisement,
        ]);
    }

    /**
     * Show the form for editing the specified advertisement
     */
    public function edit(Advertisement $advertisement): Response
    {
        // Check if user owns this advertisement
        if ($advertisement->user_id !== auth()->id()) {
            abort(403);
        }

        $advertisement->load(['vehicle.brand', 'vehicle.category', 'images', 'featuredImage']);
        
        $brands = Brand::orderBy('name')->get();
        $categories = Category::orderBy('name')->get();

        return Inertia::render('User/Advertisements/Edit', [
            'advertisement' => $advertisement,
            'brands' => $brands,
            'categories' => $categories,
        ]);
    }

    /**
     * Update the specified advertisement
     */
    public function update(Request $request, Advertisement $advertisement)
    {
        // Check if user owns this advertisement
        if ($advertisement->user_id !== auth()->id()) {
            abort(403);
        }

        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string|max:5000',
            'price' => 'required|numeric|min:0',
            'location' => 'required|string|max:255',
            'contact_phone' => 'nullable|string|max:20',
            
            // Vehicle data
            'vehicle.brand_id' => 'required|exists:brands,id',
            'vehicle.category_id' => 'required|exists:categories,id',
            'vehicle.model' => 'required|string|max:255',
            'vehicle.year' => 'required|integer|min:1900|max:' . (date('Y') + 1),
            'vehicle.mileage' => 'required|integer|min:0',
            'vehicle.fuel_type' => 'required|string|in:gasoline,ethanol,flex,diesel,electric,hybrid',
            'vehicle.transmission' => 'required|string|in:manual,automatic,cvt',
            'vehicle.condition' => 'required|string|in:new,used,certified',
            'vehicle.color' => 'required|string|max:50',
            'vehicle.doors' => 'required|integer|min:2|max:5',
            'vehicle.engine_size' => 'nullable|string|max:20',
        ]);

        // Update vehicle
        $advertisement->vehicle->update([
            'brand_id' => $request->input('vehicle.brand_id'),
            'category_id' => $request->input('vehicle.category_id'),
            'model' => $request->input('vehicle.model'),
            'year' => $request->input('vehicle.year'),
            'mileage' => $request->input('vehicle.mileage'),
            'fuel_type' => $request->input('vehicle.fuel_type'),
            'transmission' => $request->input('vehicle.transmission'),
            'condition' => $request->input('vehicle.condition'),
            'color' => $request->input('vehicle.color'),
            'doors' => $request->input('vehicle.doors'),
            'engine_size' => $request->input('vehicle.engine_size'),
        ]);

        // Update advertisement
        $advertisement->update([
            'title' => $request->input('title'),
            'description' => $request->input('description'),
            'price' => $request->input('price'),
            'location' => $request->input('location'),
            'contact_phone' => $request->input('contact_phone'),
        ]);

        return redirect()->route('user.advertisements.show', $advertisement)
            ->with('success', 'Anúncio atualizado com sucesso!');
    }

    /**
     * Remove the specified advertisement
     */
    public function destroy(Advertisement $advertisement)
    {
        // Check if user owns this advertisement
        if ($advertisement->user_id !== auth()->id()) {
            abort(403);
        }

        // Delete images from storage
        foreach ($advertisement->images as $image) {
            $path = str_replace('/storage/', '', $image->url);
            Storage::disk('public')->delete($path);
        }

        // Delete advertisement (cascade will handle images and vehicle)
        $advertisement->delete();

        return redirect()->route('user.advertisements.index')
            ->with('success', 'Anúncio excluído com sucesso!');
    }

    /**
     * Publish advertisement
     */
    public function publish(Advertisement $advertisement)
    {
        // Check if user owns this advertisement
        if ($advertisement->user_id !== auth()->id()) {
            abort(403);
        }

        $advertisement->update(['status' => 'published']);

        return redirect()->back()
            ->with('success', 'Anúncio publicado com sucesso!');
    }

    /**
     * Unpublish advertisement
     */
    public function unpublish(Advertisement $advertisement)
    {
        // Check if user owns this advertisement
        if ($advertisement->user_id !== auth()->id()) {
            abort(403);
        }

        $advertisement->update(['status' => 'draft']);

        return redirect()->back()
            ->with('success', 'Anúncio despublicado com sucesso!');
    }
}
