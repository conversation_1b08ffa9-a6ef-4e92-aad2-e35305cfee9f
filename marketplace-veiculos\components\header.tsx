"use client"

import { useState } from "react"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Search, MapPin, MessageCircle, Bell, User, Plus, Menu } from "lucide-react"

export function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)

  return (
    <header className="w-full bg-white border-b border-gray-200">
      <div className="container mx-auto px-4">
        <div className="flex h-16 items-center justify-between">
          {/* Logo */}
          <Link href="/" className="flex items-center">
            <div className="text-2xl font-bold">
              <span className="text-primary">O</span>
              <span className="text-secondary">L</span>
              <span className="text-primary">X</span>
            </div>
          </Link>

          {/* Search Bar - Desktop */}
          <div className="hidden md:flex flex-1 max-w-2xl mx-8">
            <div className="relative flex-1">
              <Input
                type="text"
                placeholder='Buscar "Celta"'
                className="pl-4 pr-12 h-10 border-gray-300 rounded-l-md rounded-r-none focus:ring-primary focus:border-primary"
              />
              <Button
                size="sm"
                className="absolute right-0 top-0 h-10 px-4 bg-primary hover:bg-primary/90 rounded-l-none"
              >
                <Search className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Location */}
          <div className="hidden md:flex items-center text-sm text-gray-600 mr-4">
            <MapPin className="h-4 w-4 mr-1" />
            <span>RJ</span>
          </div>

          {/* Desktop Actions */}
          <div className="hidden md:flex items-center space-x-4">
            <Button variant="ghost" size="sm" className="text-gray-600">
              Plano Profissional
            </Button>
            <Button variant="ghost" size="sm" className="text-gray-600">
              Meus Anúncios
            </Button>
            <Button variant="ghost" size="sm" className="text-gray-600 relative">
              <MessageCircle className="h-4 w-4 mr-1" />
              Chat
            </Button>
            <Button variant="ghost" size="sm" className="text-gray-600 relative">
              <Bell className="h-4 w-4 mr-1" />
              Notificações
              <Badge className="absolute -top-1 -right-1 h-5 w-5 rounded-full bg-secondary text-white text-xs p-0 flex items-center justify-center">
                1
              </Badge>
            </Button>
            <Button variant="ghost" size="sm" className="text-gray-600">
              <User className="h-4 w-4 mr-1" />
              Rafael
            </Button>
            <Button className="bg-secondary hover:bg-secondary/90 text-white px-6">
              <Plus className="h-4 w-4 mr-2" />
              Anunciar grátis
            </Button>
          </div>

          {/* Mobile Menu Button */}
          <Button variant="ghost" size="sm" className="md:hidden" onClick={() => setIsMenuOpen(!isMenuOpen)}>
            <Menu className="h-4 w-4" />
          </Button>
        </div>

        <div className="hidden md:flex items-center py-3 space-x-6 border-t border-gray-100">
          <Button variant="ghost" size="sm" className="text-gray-600 hover:text-primary">
            <span className="mr-2">🎫</span>
            Cupons
          </Button>
          <Button variant="ghost" size="sm" className="text-gray-600 hover:text-primary">
            <span className="mr-2">🚗</span>
            Autos
          </Button>
          <Button variant="ghost" size="sm" className="text-gray-600 hover:text-primary">
            <span className="mr-2">🏠</span>
            Imóveis
          </Button>
          <Button variant="ghost" size="sm" className="text-gray-600 hover:text-primary">
            <span className="mr-2">🔧</span>
            Autopeças
          </Button>
          <Button variant="ghost" size="sm" className="text-gray-600 hover:text-primary">
            <span className="mr-2">📱</span>
            Celulares
          </Button>
          <Button variant="ghost" size="sm" className="text-gray-600 hover:text-primary">
            <span className="mr-2">🏡</span>
            Decoração
          </Button>
          <Button variant="ghost" size="sm" className="text-gray-600 hover:text-primary">
            <span className="mr-2">⚡</span>
            Eletro
          </Button>
          <Button variant="ghost" size="sm" className="text-gray-600 hover:text-primary">
            <span className="mr-2">🪑</span>
            Móveis
          </Button>
          <Button variant="ghost" size="sm" className="text-gray-600 hover:text-primary">
            <span className="mr-2">⚽</span>
            Esportes
          </Button>
          <Button variant="ghost" size="sm" className="text-gray-600 hover:text-primary">
            <span className="mr-2">🎮</span>
            Hobbies
          </Button>
          <Button variant="ghost" size="sm" className="text-gray-600 hover:text-primary">
            <span className="mr-2">🚜</span>
            Agro
          </Button>
          <Button variant="ghost" size="sm" className="text-gray-600 hover:text-primary">
            <span className="mr-2">👕</span>
            Moda
          </Button>
          <Button variant="ghost" size="sm" className="text-gray-600 hover:text-primary">
            <span className="mr-2">🧸</span>
            Infantil
          </Button>
        </div>

        {/* Mobile Search */}
        <div className="md:hidden py-3">
          <div className="relative">
            <Input
              type="text"
              placeholder='Buscar "Celta"'
              className="pl-4 pr-12 h-10 border-gray-300 focus:ring-primary focus:border-primary"
            />
            <Button size="sm" className="absolute right-1 top-1 h-8 px-3 bg-primary hover:bg-primary/90">
              <Search className="h-4 w-4" />
            </Button>
          </div>
          <div className="flex items-center mt-2 text-sm text-gray-600">
            <MapPin className="h-4 w-4 mr-1" />
            <span>Rio de Janeiro</span>
          </div>
        </div>

        {/* Mobile Categories */}
        <div className="md:hidden py-3 border-t border-gray-100">
          <div className="flex space-x-4 overflow-x-auto">
            <Button variant="ghost" size="sm" className="text-gray-600 whitespace-nowrap">
              <span className="mr-2">🎫</span>
              Cupons
            </Button>
            <Button variant="ghost" size="sm" className="text-gray-600 whitespace-nowrap">
              <span className="mr-2">🚗</span>
              Autos
            </Button>
            <Button variant="ghost" size="sm" className="text-gray-600 whitespace-nowrap">
              <span className="mr-2">🔧</span>
              Autopeças
            </Button>
            <Button variant="ghost" size="sm" className="text-gray-600 whitespace-nowrap">
              <span className="mr-2">📱</span>
              Celulares
            </Button>
          </div>
        </div>

        {/* Mobile Navigation Menu */}
        {isMenuOpen && (
          <div className="md:hidden py-4 border-t border-gray-100">
            <nav className="flex flex-col space-y-2">
              <Button variant="ghost" className="justify-start text-gray-600">
                Meus Anúncios
              </Button>
              <Button variant="ghost" className="justify-start text-gray-600">
                Chat
              </Button>
              <Button variant="ghost" className="justify-start text-gray-600">
                Notificações
              </Button>
              <Button className="justify-start mt-4 bg-secondary hover:bg-secondary/90 text-white">
                <Plus className="h-4 w-4 mr-2" />
                Anunciar grátis
              </Button>
            </nav>
          </div>
        )}
      </div>
    </header>
  )
}
