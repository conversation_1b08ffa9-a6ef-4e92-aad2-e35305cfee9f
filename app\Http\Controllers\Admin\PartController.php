<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\StorePartRequest;
use App\Http\Requests\Admin\UpdatePartRequest;
use App\Models\Brand;
use App\Models\Category;
use App\Models\Part;
use App\Models\Vehicle;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Inertia\Inertia;
use Spatie\MediaLibrary\MediaCollections\Exceptions\FileDoesNotExist;
use Spatie\MediaLibrary\MediaCollections\Exceptions\FileIsTooBig;

class PartController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $query = Part::with(['category', 'brand', 'mainImage', 'compatibleVehicles']);
        
        // Filtros de busca
        if ($request->filled('search')) {
            $search = $request->input('search');
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('part_number', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }
        
        if ($request->filled('category_id')) {
            $query->where('category_id', $request->input('category_id'));
        }
        
        if ($request->filled('brand_id')) {
            $query->where('brand_id', $request->input('brand_id'));
        }
        
        if ($request->filled('status')) {
            $query->where('status', $request->input('status'));
        }
        
        if ($request->filled('is_original')) {
            $query->where('is_original', $request->boolean('is_original'));
        }
        
        if ($request->filled('in_stock')) {
            if ($request->boolean('in_stock')) {
                $query->where('stock_quantity', '>', 0);
            } else {
                $query->where('stock_quantity', '<=', 0);
            }
        }
        
        // Ordenação
        $sortBy = $request->input('sort_by', 'created_at');
        $sortDirection = $request->input('sort_direction', 'desc');
        $query->orderBy($sortBy, $sortDirection);
        
        $parts = $query->paginate(15)->withQueryString();
        
        $categories = Category::where('type', 'parts')->pluck('name', 'id');
        $brands = Brand::pluck('name', 'id');
        
        return Inertia::render('Admin/Parts/Index', [
            'parts' => $parts,
            'categories' => $categories,
            'brands' => $brands,
            'filters' => $request->all('search', 'category_id', 'brand_id', 'status', 'is_original', 'in_stock'),
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $categories = Category::where('type', 'parts')->pluck('name', 'id');
        $brands = Brand::pluck('name', 'id');
        $vehicles = Vehicle::published()->get(['id', 'brand_id', 'model', 'year_manufacture']);
        
        return Inertia::render('Admin/Parts/Create', [
            'categories' => $categories,
            'brands' => $brands,
            'vehicles' => $vehicles,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    /**
     * Store a newly created resource in storage.
     *
     * @param  \App\Http\Requests\Admin\StorePartRequest  $request
     * @return \Illuminate\Http\Response
     */
    public function store(StorePartRequest $request)
    {
        DB::beginTransaction();
        
        try {
            $data = $request->validated();
            $data['user_id'] = Auth::id();
            
            // Criar a peça
            $part = Part::create($data);
            
            // Processar imagens
            $this->processImages($part, $request);
            
            // Sincronizar veículos compatíveis
            if ($request->has('compatible_vehicles')) {
                $part->compatibleVehicles()->sync($request->input('compatible_vehicles'));
            }
            
            DB::commit();
            
            return redirect()
                ->route('admin.parts.index')
                ->with('success', 'Peça cadastrada com sucesso!');
                
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Erro ao cadastrar peça: ' . $e->getMessage());
            
            return back()
                ->withInput()
                ->with('error', 'Erro ao cadastrar peça. Por favor, tente novamente.');
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\Part  $part
     * @return \Illuminate\Http\Response
     */
    public function show(Part $part)
    {
        $part->load(['category', 'brand', 'images', 'compatibleVehicles.brand']);
        
        return Inertia::render('Admin/Parts/Show', [
            'part' => $part,
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\Part  $part
     * @return \Illuminate\Http\Response
     */
    public function edit(Part $part)
    {
        $part->load(['images', 'compatibleVehicles']);
        $categories = Category::where('type', 'parts')->pluck('name', 'id');
        $brands = Brand::pluck('name', 'id');
        $vehicles = Vehicle::published()->get(['id', 'brand_id', 'model', 'year_manufacture']);
        
        return Inertia::render('Admin/Parts/Edit', [
            'part' => $part,
            'categories' => $categories,
            'brands' => $brands,
            'vehicles' => $vehicles,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    /**
     * Update the specified resource in storage.
     *
     * @param  \App\Http\Requests\Admin\UpdatePartRequest  $request
     * @param  \App\Models\Part  $part
     * @return \Illuminate\Http\Response
     */
    public function update(UpdatePartRequest $request, Part $part)
    {
        DB::beginTransaction();
        
        try {
            $data = $request->validated();
            
            // Atualizar a peça
            $part->update($data);
            
            // Processar imagens
            $this->processImages($part, $request);
            
            // Remover imagens selecionadas
            if ($request->has('remove_images')) {
                $part->media()->whereIn('id', $request->input('remove_images'))->delete();
            }
            
            // Sincronizar veículos compatíveis
            $part->compatibleVehicles()->sync($request->input('compatible_vehicles', []));
            
            DB::commit();
            
            return redirect()
                ->route('admin.parts.index')
                ->with('success', 'Peça atualizada com sucesso!');
                
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Erro ao atualizar peça: ' . $e->getMessage());
            
            return back()
                ->withInput()
                ->with('error', 'Erro ao atualizar peça. Por favor, tente novamente.');
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\Part  $part
     * @return \Illuminate\Http\Response
     */
    public function destroy(Part $part)
    {
        DB::beginTransaction();
        
        try {
            // Remover relacionamentos
            $part->compatibleVehicles()->detach();
            
            // Remover mídia
            $part->clearMediaCollection('images');
            $part->clearMediaCollection('featured_image');
            
            // Excluir a peça
            $part->delete();
            
            DB::commit();
            
            return redirect()
                ->route('admin.parts.index')
                ->with('success', 'Peça excluída com sucesso!');
                
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Erro ao excluir peça: ' . $e->getMessage());
            
            return back()
                ->with('error', 'Erro ao excluir peça. Por favor, tente novamente.');
        }
    }
    
    /**
     * Processa o upload de imagens para a peça.
     *
     * @param  \App\Models\Part  $part
     * @param  \Illuminate\Http\Request  $request
     * @return void
     */
    protected function processImages(Part $part, $request)
    {
        // Processar imagem de destaque
        if ($request->hasFile('featured_image')) {
            try {
                $part->clearMediaCollection('featured_image');
                $part->addMediaFromRequest('featured_image')
                    ->toMediaCollection('featured_image');
            } catch (FileDoesNotExist | FileIsTooBig $e) {
                Log::error('Erro ao fazer upload da imagem de destaque: ' . $e->getMessage());
            }
        }
        
        // Processar galeria de imagens
        if ($request->hasFile('images')) {
            foreach ($request->file('images') as $image) {
                try {
                    $part->addMedia($image)
                        ->toMediaCollection('images');
                } catch (FileDoesNotExist | FileIsTooBig $e) {
                    Log::error('Erro ao fazer upload de imagem da galeria: ' . $e->getMessage());
                }
            }
        }
    }
}
