import { Link } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import { ChevronLeft, ChevronRight, Heart } from 'lucide-react';
import { useState } from 'react';

interface Product {
    id: number;
    title: string;
    price: number;
    image: string;
    category: string;
}

const featuredProducts: Product[] = [
    {
        id: 1,
        title: 'Blazer Elegante',
        price: 89.90,
        image: '/placeholder.jpg',
        category: 'Moda'
    },
    {
        id: 2,
        title: 'Tênis Esportivo',
        price: 159.90,
        image: '/placeholder.jpg',
        category: 'Calçados'
    },
    {
        id: 3,
        title: 'Cal<PERSON> Jeans',
        price: 79.90,
        image: '/placeholder.jpg',
        category: 'Moda'
    },
    {
        id: 4,
        title: 'Camisa Social',
        price: 69.90,
        image: '/placeholder.jpg',
        category: 'Moda'
    }
];

export default function HeroSectionOLX() {
    const [currentSlide, setCurrentSlide] = useState(0);

    const nextSlide = () => {
        setCurrentSlide((prev) => (prev + 1) % featuredProducts.length);
    };

    const prevSlide = () => {
        setCurrentSlide((prev) => (prev - 1 + featuredProducts.length) % featuredProducts.length);
    };

    return (
        <section className="relative bg-gradient-to-r from-orange-400 via-orange-500 to-orange-600 text-white overflow-hidden">
            {/* Background Pattern */}
            <div className="absolute inset-0 opacity-10">
                <div className="absolute inset-0" style={{
                    backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.4'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
                    backgroundSize: '60px 60px'
                }} />
            </div>

            <div className="relative container mx-auto px-4 py-12">
                <div className="grid lg:grid-cols-2 gap-8 items-center">
                    {/* Content */}
                    <div className="space-y-6">
                        <div className="space-y-4">
                            <h1 className="text-4xl lg:text-5xl font-bold leading-tight">
                                Achadinhos de estilo
                            </h1>
                            <p className="text-xl lg:text-2xl text-orange-100 max-w-lg">
                                Preços que cabem no bolso e com estilo que vale ouro
                            </p>
                        </div>

                        {/* CTA Button */}
                        <div className="flex flex-col sm:flex-row gap-4">
                            <Button 
                                asChild 
                                size="lg" 
                                className="bg-purple-600 hover:bg-purple-700 text-white font-semibold text-lg px-8 py-4 rounded-lg"
                            >
                                <Link href="/anuncios">
                                    Comprar agora
                                </Link>
                            </Button>
                        </div>
                    </div>

                    {/* Product Carousel */}
                    <div className="relative">
                        <div className="flex items-center justify-center space-x-4">
                            {/* Previous Button */}
                            <Button
                                variant="ghost"
                                size="icon"
                                onClick={prevSlide}
                                className="absolute left-0 z-10 bg-white/20 hover:bg-white/30 text-white rounded-full"
                            >
                                <ChevronLeft className="h-6 w-6" />
                            </Button>

                            {/* Product Cards */}
                            <div className="flex space-x-4 overflow-hidden">
                                {featuredProducts.slice(currentSlide, currentSlide + 3).map((product, index) => (
                                    <div
                                        key={product.id}
                                        className="bg-white rounded-lg shadow-lg overflow-hidden min-w-[200px] transform transition-transform hover:scale-105"
                                    >
                                        <div className="relative">
                                            <img
                                                src={product.image}
                                                alt={product.title}
                                                className="w-full h-40 object-cover"
                                            />
                                            <Button
                                                variant="ghost"
                                                size="icon"
                                                className="absolute top-2 right-2 bg-white/80 hover:bg-white text-gray-600 rounded-full"
                                            >
                                                <Heart className="h-4 w-4" />
                                            </Button>
                                        </div>
                                        <div className="p-4">
                                            <h3 className="text-gray-900 font-semibold text-sm mb-1">
                                                {product.title}
                                            </h3>
                                            <p className="text-gray-600 text-xs mb-2">
                                                {product.category}
                                            </p>
                                            <p className="text-purple-600 font-bold text-lg">
                                                R$ {product.price.toFixed(2).replace('.', ',')}
                                            </p>
                                        </div>
                                    </div>
                                ))}
                            </div>

                            {/* Next Button */}
                            <Button
                                variant="ghost"
                                size="icon"
                                onClick={nextSlide}
                                className="absolute right-0 z-10 bg-white/20 hover:bg-white/30 text-white rounded-full"
                            >
                                <ChevronRight className="h-6 w-6" />
                            </Button>
                        </div>

                        {/* Carousel Indicators */}
                        <div className="flex justify-center mt-6 space-x-2">
                            {featuredProducts.map((_, index) => (
                                <button
                                    key={index}
                                    onClick={() => setCurrentSlide(index)}
                                    className={`w-3 h-3 rounded-full transition-colors ${
                                        index === currentSlide
                                            ? 'bg-white'
                                            : 'bg-white/50'
                                    }`}
                                />
                            ))}
                        </div>
                    </div>
                </div>
            </div>

            {/* Bottom Wave */}
            <div className="absolute bottom-0 left-0 right-0">
                <svg viewBox="0 0 1440 120" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path 
                        d="M0 120L60 110C120 100 240 80 360 70C480 60 600 60 720 65C840 70 960 80 1080 85C1200 90 1320 90 1380 90L1440 90V120H1380C1320 120 1200 120 1080 120C960 120 840 120 720 120C600 120 480 120 360 120C240 120 120 120 60 120H0V120Z" 
                        fill="white"
                    />
                </svg>
            </div>
        </section>
    );
}
