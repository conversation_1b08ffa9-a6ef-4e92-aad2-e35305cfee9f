<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Vehicle;
use App\Models\Brand;
use App\Models\Category;
use App\Http\Requests\StoreVehicleRequest;
use App\Http\Requests\UpdateVehicleRequest;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Illuminate\Support\Str;

class VehicleController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $vehicles = Vehicle::with(['brand', 'category', 'user'])
            ->latest()
            ->filter($request->only('search', 'brand', 'category', 'status'))
            ->paginate(10)
            ->withQueryString();

        return Inertia::render('Admin/Vehicles/Index', [
            'vehicles' => $vehicles,
            'filters' => $request->all('search', 'brand', 'category', 'status'),
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function create()
    {
        return Inertia::render('Admin/Vehicles/Create', [
            'brands' => Brand::all(['id', 'name']),
            'categories' => Category::all(['id', 'name']),
            'fuelTypes' => [
                'gasoline' => 'Gasolina',
                'ethanol' => 'Álcool',
                'flex' => 'Flex',
                'diesel' => 'Diesel',
                'electric' => 'Elétrico',
                'hybrid' => 'Híbrido',
            ],
            'transmissions' => [
                'manual' => 'Manual',
                'automatic' => 'Automático',
                'semi_automatic' => 'Semi-automático',
                'cvt' => 'CVT',
            ],
        ]);
    }

    public function store(StoreVehicleRequest $request)
    {
        $data = $request->validated();
        $data['slug'] = Str::slug($data['brand_id'] . ' ' . $data['model'] . ' ' . $data['year_manufacture']);
        $data['user_id'] = $request->user()->id;
        $data['status'] = $request->has('publish') ? 'published' : 'draft';

        $vehicle = Vehicle::create($data);

        // Handle image uploads
        if ($request->hasFile('images')) {
            foreach ($request->file('images') as $image) {
                $vehicle->addMedia($image)
                    ->toMediaCollection('images');
            }
        }

        // Handle featured image upload
        if ($request->hasFile('featured_image')) {
            $vehicle->addMedia($request->file('featured_image'))
                ->toMediaCollection('featured_image');
        }

        return redirect()
            ->route('admin.vehicles.edit', $vehicle)
            ->with('success', 'Veículo cadastrado com sucesso!');
    }

    /**
     * Display the specified resource.
     */
    public function show(Vehicle $vehicle)
    {
        $vehicle->load(['brand', 'category', 'user', 'features', 'images']);
        
        // Incrementa a contagem de visualizações
        $vehicle->increment('views');
        
        return Inertia::render('Vehicles/Show', [
            'vehicle' => $vehicle,
            'relatedVehicles' => Vehicle::where('category_id', $vehicle->category_id)
                ->where('id', '!=', $vehicle->id)
                ->with(['brand', 'category'])
                ->take(4)
                ->get()
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function edit(Vehicle $vehicle)
    {
        $vehicle->load(['features', 'images']);
        
        return Inertia::render('Admin/Vehicles/Edit', [
            'vehicle' => $vehicle,
            'brands' => Brand::all(['id', 'name']),
            'categories' => Category::all(['id', 'name']),
            'fuelTypes' => [
                'gasoline' => 'Gasolina',
                'ethanol' => 'Álcool',
                'flex' => 'Flex',
                'diesel' => 'Diesel',
                'electric' => 'Elétrico',
                'hybrid' => 'Híbrido',
            ],
            'transmissions' => [
                'manual' => 'Manual',
                'automatic' => 'Automático',
                'semi_automatic' => 'Semi-automático',
                'cvt' => 'CVT',
            ],
        ]);
    }

    public function update(UpdateVehicleRequest $request, Vehicle $vehicle)
    {
        $data = $request->validated();
        
        if ($request->has('publish')) {
            $data['status'] = 'published';
        } elseif ($request->has('draft')) {
            $data['status'] = 'draft';
        }
        
        $vehicle->update($data);

        // Handle image uploads
        if ($request->hasFile('images')) {
            foreach ($request->file('images') as $image) {
                $vehicle->addMedia($image)
                    ->toMediaCollection('images');
            }
        }

        // Handle featured image upload
        if ($request->hasFile('featured_image')) {
            // Clear existing featured image
            $vehicle->clearMediaCollection('featured_image');
            
            $vehicle->addMedia($request->file('featured_image'))
                ->toMediaCollection('featured_image');
        }

        // Handle image removal
        if ($request->has('remove_images')) {
            foreach ($request->remove_images as $mediaId) {
                $media = $vehicle->media()->find($mediaId);
                if ($media) {
                    $media->delete();
                }
            }
        }
        
        return back()->with('success', 'Veículo atualizado com sucesso!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Vehicle $vehicle)
    {
        $vehicle->delete();
        
        return redirect()
            ->route('admin.vehicles.index')
            ->with('success', 'Veículo removido com sucesso!');
    }
}
