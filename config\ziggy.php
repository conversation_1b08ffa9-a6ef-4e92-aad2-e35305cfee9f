<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Route Filter
    |--------------------------------------------------------------------------
    |
    | Routes can be filtered by the route name. If you want to filter out
    | all routes except those in a certain group or with a certain prefix,
    | you can use the 'only' and 'except' options. You can also filter
    | by route name pattern using the 'patterns' option.
    |
    */

    'route_groups' => [
        'web',
    ],

    /*
    |--------------------------------------------------------------------------
    | Base URL
    |--------------------------------------------------------------------------
    |
    | The base URL which will be prepended to all of your generated URLs.
    | For example, if you're using the default 'url' helper, this will be
    | the same as your app.url config value.
    |
    */

    'url' => config('app.url'),

    /*
    |--------------------------------------------------------------------------
    | Default Route Name
    |--------------------------------------------------------------------------
    |
    | The default route name that will be used if no name is provided.
    | This is useful if you want to use a different naming convention
    | for your routes than the default Laravel route naming.
    |
    */

    'default_route_name' => 'home',

    /*
    |--------------------------------------------------------------------------
    | Default Route Parameters
    |--------------------------------------------------------------------------
    |
    | Default parameters that will be included in every route. This is useful
    | for things like locale or tenant parameters that are used across
    | multiple routes.
    |
    */

    'default_parameters' => [
        // 'locale' => app()->getLocale(),
    ],

    /*
    |--------------------------------------------------------------------------
    | Absolute URLs
    |--------------------------------------------------------------------------
    |
    | Whether to generate absolute URLs by default. If set to false, relative
    | URLs will be generated instead. You can still override this on a per-route
    | basis by passing 'absolute' => true when generating a URL.
    |
    */

    'absolute' => true,

    /*
    |--------------------------------------------------------------------------
    | URL Parameters
    |--------------------------------------------------------------------------
    |
    | Whether to include route parameters in the generated URLs. If set to
    | false, only the route name will be included in the generated JavaScript.
    |
    */

    'parameters' => true,
];
