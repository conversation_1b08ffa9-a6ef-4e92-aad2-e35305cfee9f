import { Head, usePage } from '@inertiajs/react';
import MainLayout from '../../layouts/MainLayout';

export default function Security() {
    const { categories = [] } = usePage<any>().props;

    return (
        <MainLayout categories={categories}>
            <Head title="Dicas de Segurança" />

            <div className="container mx-auto px-4 py-8">
                <div className="mx-auto max-w-4xl">
                    <h1 className="mb-8 text-3xl font-bold">
                        Dicas de Segurança
                    </h1>

                    <div className="space-y-8">
                        <div className="rounded-lg border border-yellow-200 bg-yellow-50 p-6">
                            <h2 className="mb-4 text-xl font-semibold text-yellow-800">
                                ⚠️ Importante
                            </h2>
                            <p className="text-yellow-700">
                                Sua segurança é nossa prioridade. Siga sempre
                                estas dicas para uma experiência segura na
                                plataforma.
                            </p>
                        </div>

                        <div className="grid gap-6 md:grid-cols-2">
                            <div className="rounded-lg bg-white p-6 shadow-md">
                                <h3 className="mb-4 text-lg font-semibold">
                                    🛡️ Para Compradores
                                </h3>
                                <ul className="space-y-2 text-gray-600">
                                    <li>
                                        • Sempre veja o veículo pessoalmente
                                    </li>
                                    <li>• Verifique a documentação</li>
                                    <li>• Faça um test drive</li>
                                    <li>• Negocie em locais públicos</li>
                                    <li>• Desconfie de preços muito baixos</li>
                                    <li>• Use formas de pagamento seguras</li>
                                </ul>
                            </div>

                            <div className="rounded-lg bg-white p-6 shadow-md">
                                <h3 className="mb-4 text-lg font-semibold">
                                    🔒 Para Vendedores
                                </h3>
                                <ul className="space-y-2 text-gray-600">
                                    <li>• Mantenha seus dados atualizados</li>
                                    <li>• Use fotos reais do veículo</li>
                                    <li>• Seja transparente na descrição</li>
                                    <li>
                                        • Encontre compradores em locais seguros
                                    </li>
                                    <li>
                                        • Confirme o pagamento antes da entrega
                                    </li>
                                    <li>• Guarde comprovantes da venda</li>
                                </ul>
                            </div>
                        </div>

                        <div className="rounded-lg bg-white p-6 shadow-md">
                            <h3 className="mb-4 text-lg font-semibold">
                                🚨 Sinais de Alerta
                            </h3>
                            <div className="grid gap-4 md:grid-cols-2">
                                <div>
                                    <h4 className="mb-2 font-medium">
                                        Evite negócios que:
                                    </h4>
                                    <ul className="space-y-1 text-sm text-gray-600">
                                        <li>• Pedem pagamento antecipado</li>
                                        <li>• Oferecem preços irreais</li>
                                        <li>
                                            • Pressionam para decisão rápida
                                        </li>
                                        <li>• Não permitem inspeção</li>
                                        <li>
                                            • Usam apenas comunicação online
                                        </li>
                                    </ul>
                                </div>
                                <div>
                                    <h4 className="mb-2 font-medium">
                                        Documentos obrigatórios:
                                    </h4>
                                    <ul className="space-y-1 text-sm text-gray-600">
                                        <li>
                                            • CRLV (Certificado de Registro)
                                        </li>
                                        <li>• Nota fiscal ou DUT</li>
                                        <li>• Comprovante de quitação</li>
                                        <li>
                                            • Laudo de vistoria (se aplicável)
                                        </li>
                                        <li>• Identidade do proprietário</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <div className="rounded-lg border border-blue-200 bg-blue-50 p-6">
                            <h3 className="mb-4 text-lg font-semibold text-blue-800">
                                💡 Dica Extra
                            </h3>
                            <p className="text-blue-700">
                                Em caso de dúvidas ou problemas, entre em
                                contato conosco imediatamente. Nossa equipe está
                                sempre pronta para ajudar e garantir sua
                                segurança.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </MainLayout>
    );
}
