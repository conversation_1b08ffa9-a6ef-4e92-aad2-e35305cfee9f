<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class AdminAccess
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = auth()->user();

        if (!$user) {
            abort(401, 'Unauthenticated');
        }

        // Verificar se o usuário tem role admin ou permissão admin.access
        $hasAdminRole = $user->roles->contains('name', 'admin');
        $hasAdminPermission = $user->getAllPermissions()->contains('name', 'admin.access');

        if (!$hasAdminRole && !$hasAdminPermission) {
            abort(403, 'Access denied. Admin privileges required. User roles: ' . $user->roles->pluck('name')->implode(', ') . '. User permissions: ' . $user->getAllPermissions()->pluck('name')->implode(', '));
        }

        return $next($request);
    }
}
