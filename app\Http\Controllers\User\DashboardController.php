<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Models\Advertisement;
use App\Models\Chat;
use App\Models\Favorite;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class DashboardController extends Controller
{
    /**
     * Display the user dashboard.
     */
    public function index(Request $request): Response
    {
        $user = $request->user();
        
        // Estatísticas do usuário
        $stats = [
            'total_advertisements' => $user->advertisements()->count(),
            'active_advertisements' => $user->advertisements()->where('status', 'published')->count(),
            'pending_advertisements' => $user->advertisements()->where('status', 'pending_review')->count(),
            'total_favorites' => $user->favorites()->count(),
            'total_chats' => Chat::where(function($query) use ($user) {
                $query->where('buyer_id', $user->id)
                      ->orWhere('seller_id', $user->id);
            })->count(),
            'unread_messages' => Chat::where(function($query) use ($user) {
                $query->where('buyer_id', $user->id)
                      ->orWhere('seller_id', $user->id);
            })->sum('unread_messages_count'),
        ];

        // Anúncios recentes do usuário
        $recentAdvertisements = $user->advertisements()
            ->with(['vehicle.brand', 'vehicle.category'])
            ->latest()
            ->take(5)
            ->get()
            ->map(function ($ad) {
                return [
                    'id' => $ad->id,
                    'title' => $ad->title,
                    'status' => $ad->status,
                    'status_label' => $ad->status_label,
                    'price' => $ad->price,
                    'views' => $ad->views,
                    'created_at' => $ad->created_at->toISOString(),
                    'featured_image_url' => $ad->featured_image_url,
                    'url' => '/anuncios/' . $ad->id,
                ];
            });

        // Chats recentes
        $recentChats = Chat::with(['advertisement', 'buyer', 'seller', 'lastMessage'])
            ->where(function($query) use ($user) {
                $query->where('buyer_id', $user->id)
                      ->orWhere('seller_id', $user->id);
            })
            ->latest('updated_at')
            ->take(5)
            ->get()
            ->map(function ($chat) use ($user) {
                $otherParticipant = $chat->buyer_id === $user->id ? $chat->seller : $chat->buyer;
                return [
                    'id' => $chat->id,
                    'advertisement' => [
                        'title' => $chat->advertisement->title,
                        'featured_image_url' => $chat->advertisement->featured_image_url,
                    ],
                    'other_participant' => [
                        'name' => $otherParticipant->name,
                    ],
                    'last_message' => $chat->lastMessage ? [
                        'message' => $chat->lastMessage->message,
                        'created_at' => $chat->lastMessage->created_at->toISOString(),
                    ] : null,
                    'unread_count' => $chat->unread_messages_count,
                    'url' => '/minha-conta/chat/' . $chat->id,
                ];
            });

        // Favoritos recentes
        $recentFavorites = $user->favorites()
            ->with(['advertisement.vehicle.brand'])
            ->latest()
            ->take(5)
            ->get()
            ->map(function ($favorite) {
                return [
                    'id' => $favorite->id,
                    'advertisement' => [
                        'id' => $favorite->advertisement->id,
                        'title' => $favorite->advertisement->title,
                        'price' => $favorite->advertisement->price,
                        'featured_image_url' => $favorite->advertisement->featured_image_url,
                        'url' => '/anuncios/' . $favorite->advertisement->id,
                    ],
                    'created_at' => $favorite->created_at->toISOString(),
                ];
            });

        return Inertia::render('User/Dashboard', [
            'stats' => $stats,
            'recentAdvertisements' => $recentAdvertisements,
            'recentChats' => $recentChats,
            'recentFavorites' => $recentFavorites,
        ]);
    }
}
