"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { MapPin, Calendar, Fuel, Palette, Car, Cog, Phone, Mail, MessageCircle } from "lucide-react"

interface PreviewStepProps {
  formData: any
  updateFormData: (data: any) => void
  onNext: () => void
  onPrev: () => void
}

export function PreviewStep({ formData }: PreviewStepProps) {
  const photos = formData.photos || []

  return (
    <div className="space-y-6">
      <div className="text-center mb-6">
        <h3 className="text-lg font-semibold mb-2">Revisão do anúncio</h3>
        <p className="text-muted-foreground">Confira todas as informações antes de publicar seu anún<PERSON></p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Photos Preview */}
          {photos.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Fotos ({photos.length})</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-3 md:grid-cols-4 gap-2">
                  {photos.slice(0, 8).map((photo: File, index: number) => (
                    <div key={index} className="relative">
                      <img
                        src={URL.createObjectURL(photo) || "/placeholder.svg"}
                        alt={`Foto ${index + 1}`}
                        className="w-full h-20 object-cover rounded"
                      />
                      {index === 0 && (
                        <div className="absolute bottom-1 left-1 bg-primary text-primary-foreground text-xs px-1 rounded">
                          Principal
                        </div>
                      )}
                    </div>
                  ))}
                  {photos.length > 8 && (
                    <div className="w-full h-20 bg-muted rounded flex items-center justify-center text-sm text-muted-foreground">
                      +{photos.length - 8}
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Vehicle Details */}
          <Card>
            <CardHeader>
              <div className="flex justify-between items-start">
                <div>
                  <CardTitle className="text-2xl">
                    {formData.brand} {formData.model} {formData.version} {formData.year}
                  </CardTitle>
                  <div className="flex items-center gap-2 mt-2">
                    <Badge variant="secondary">{formData.condition}</Badge>
                    <Badge variant="outline">{formData.category}</Badge>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-3xl font-bold text-primary">R$ {formData.price}</div>
                  <div className="text-sm text-muted-foreground">
                    {formData.type === "venda" ? "À vista" : "Por mês"}
                  </div>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Specifications */}
              <div>
                <h4 className="font-semibold mb-3">Especificações</h4>
                <div className="grid grid-cols-2 gap-4">
                  <div className="flex items-center space-x-2">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">Ano: {formData.year}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Car className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">KM: {formData.mileage}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Fuel className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">Combustível: {formData.fuel}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Cog className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">Câmbio: {formData.transmission}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Palette className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">Cor: {formData.color}</span>
                  </div>
                  {formData.doors && (
                    <div className="flex items-center space-x-2">
                      <Car className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">Portas: {formData.doors}</span>
                    </div>
                  )}
                </div>
              </div>

              <Separator />

              {/* Description */}
              <div>
                <h4 className="font-semibold mb-2">Descrição</h4>
                <p className="text-muted-foreground text-sm leading-relaxed">{formData.description}</p>
              </div>

              {/* Features */}
              {formData.features && formData.features.length > 0 && (
                <>
                  <Separator />
                  <div>
                    <h4 className="font-semibold mb-2">Equipamentos</h4>
                    <div className="grid grid-cols-2 gap-1">
                      {formData.features.map((feature: string, index: number) => (
                        <div key={index} className="flex items-center space-x-2">
                          <div className="w-1.5 h-1.5 bg-primary rounded-full"></div>
                          <span className="text-sm">{feature}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Contact Info */}
          <Card>
            <CardHeader>
              <CardTitle>Contato</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <div className="font-semibold">{formData.sellerName}</div>
                <div className="flex items-center text-sm text-muted-foreground mt-1">
                  <MapPin className="h-4 w-4 mr-1" />
                  {formData.city}, {formData.state?.toUpperCase()}
                </div>
              </div>

              <Separator />

              <div className="space-y-2">
                <Button className="w-full">
                  <MessageCircle className="h-4 w-4 mr-2" />
                  WhatsApp
                </Button>
                <Button variant="outline" className="w-full bg-transparent">
                  <Phone className="h-4 w-4 mr-2" />
                  {formData.phone}
                </Button>
                <Button variant="outline" className="w-full bg-transparent">
                  <Mail className="h-4 w-4 mr-2" />
                  E-mail
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Publication Info */}
          <Card>
            <CardHeader>
              <CardTitle>Informações da publicação</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-muted-foreground">Categoria:</span>
                <span>{formData.category}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Tipo:</span>
                <span>{formData.type}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Fotos:</span>
                <span>{photos.length}</span>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
