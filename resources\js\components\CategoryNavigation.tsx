import { Link } from '@inertiajs/react';
import {
    Baby,
    Bike,
    Car,
    Gamepad2,
    Grid3X3,
    Home,
    Shirt,
    Smartphone,
    Sofa,
    Truck,
    Wrench,
    Zap,
} from 'lucide-react';

interface Category {
    id: number;
    name: string;
    slug: string;
    description?: string;
    icon?: string;
    url: string;
    vehicles_count?: number;
    parts_count?: number;
}

interface CategoryNavigationProps {
    categories: Category[];
}

const getIconComponent = (iconName?: string) => {
    const iconMap: Record<string, any> = {
        car: Car,
        wrench: Wrench,
        smartphone: Smartphone,
        home: Home,
        sofa: Sofa,
        zap: Zap,
        gamepad2: Gamepad2,
        shirt: Shirt,
        baby: Baby,
        grid3x3: Grid3X3,
        truck: Truck,
        bike: Bike,
    };

    return iconMap[iconName || 'grid3x3'] || Grid3X3;
};

export default function CategoryNavigation({
    categories,
}: CategoryNavigationProps) {
    // Mapeamento de ícones para as categorias reais
    const iconMapping: Record<string, string> = {
        carros: 'car',
        motos: 'bike',
        pecas: 'wrench',
        caminhoes: 'truck',
        eletricos: 'zap',
        seguros: 'home',
    };

    // Usar categorias reais do banco de dados
    const displayCategories = categories.map((category) => ({
        name: category.name,
        icon: iconMapping[category.slug] || 'grid3x3',
        href: `/pesquisar?category_id=${category.id}`,
        slug: category.slug,
    }));

    return (
        <div className="border-b border-gray-200 bg-gray-50">
            <div className="container mx-auto px-4">
                <div className="flex items-center justify-center space-x-8 overflow-x-auto py-3">
                    {displayCategories.map((category) => {
                        const IconComponent = getIconComponent(category.icon);
                        return (
                            <Link
                                key={category.name}
                                href={category.href}
                                className="group flex min-w-0 flex-shrink-0 flex-col items-center space-y-1 text-gray-600 transition-colors hover:text-primary"
                            >
                                <div className="rounded-full bg-white p-2 shadow-sm transition-colors group-hover:bg-primary/10">
                                    <IconComponent className="h-6 w-6" />
                                </div>
                                <span className="text-xs font-medium whitespace-nowrap">
                                    {category.name}
                                </span>
                            </Link>
                        );
                    })}
                </div>
            </div>
        </div>
    );
}
