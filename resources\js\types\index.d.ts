import { type PageProps as InertiaPageProps } from '@inertiajs/core';
import { type InertiaLinkProps } from '@inertiajs/react';
import { type AxiosInstance } from 'axios';
import { type LucideIcon } from 'lucide-react';

declare module '@inertiajs/core' {
    interface PageProps extends InertiaPageProps {
        auth: {
            user: User;
        };
        [key: string]: unknown;
    }
}

declare global {
    const route: (
        name?: string,
        params?: Record<string, unknown>,
        absolute?: boolean,
    ) => string;
    const axios: AxiosInstance;
}

export interface Auth {
    user: User;
}

export interface BreadcrumbItem {
    title: string;
    href: string;
}

export interface NavGroup {
    title: string;
    items: NavItem[];
}

export interface NavItem {
    title: string;
    href: NonNullable<InertiaLinkProps['href']>;
    icon?: LucideIcon | null;
    isActive?: boolean;
}

export interface SharedData {
    name: string;
    quote: { message: string; author: string };
    auth: Auth;
    sidebarOpen: boolean;
    categories: Category[];
    [key: string]: unknown;
}

export type PageProps<T = Record<string, unknown>> = InertiaPageProps & {
    auth: {
        user: User;
    };
    errors: Record<string, string>;
} & T;

export interface PaginatedData<T> {
    data: T[];
    current_page: number;
    first_page_url: string;
    from: number;
    last_page: number;
    last_page_url: string;
    links: Array<{
        url: string | null;
        label: string;
        active: boolean;
    }>;
    next_page_url: string | null;
    path: string;
    per_page: number;
    prev_page_url: string | null;
    to: number;
    total: number;
}

export interface Brand {
    id: number;
    name: string;
    slug: string;
    created_at: string;
    updated_at: string;
}

// Tipo para categorias completas (usado em listagens e navegação)
export interface Category {
    id: number;
    name: string;
    slug: string;
    description?: string;
    icon?: string;
    url: string;
    vehicles_count?: number;
    parts_count?: number;
    children?: Category[];
    created_at?: string;
    updated_at?: string;
}

// Tipo simplificado para opções em formulários
export interface CategoryOption {
    id: number;
    name: string;
}

export interface VehicleFeature {
    id: number;
    name: string;
    icon?: string;
    created_at: string;
    updated_at: string;
}

export interface MediaFile {
    id: number;
    file_name: string;
    original_url: string;
    size: number;
    mime_type: string;
    collection_name: string;
    created_at: string;
    updated_at: string;
}

export interface VehicleImage {
    id: number;
    url: string;
    is_main: boolean;
    order: number;
    created_at: string;
    updated_at: string;
}

export interface Vehicle {
    id: number;
    brand_id: number;
    category_id: number;
    user_id: number;
    model: string;
    slug: string;
    description: string;
    price: number;
    promotional_price?: number;
    year_manufacture: number;
    year_model: number;
    mileage: number;
    color: string;
    fuel_type: string;
    transmission: string;
    engine_size: string;
    power: string;
    doors: number;
    is_negotiable: boolean;
    is_featured: boolean;
    is_active: boolean;
    views: number;
    created_at: string;
    updated_at: string;
    deleted_at: string | null;
    brand: Brand;
    category: Category;
    features: VehicleFeature[];
    images: VehicleImage[];
}

export interface PartFilters {
    search?: string;
    status?: string;
    brand_id?: string;
    category_id?: string;
}

export interface Part {
    id: number;
    user_id: number;
    category_id: number | null;
    brand_id: number | null;
    name: string;
    slug: string;
    part_number: string;
    description: string;
    price: number;
    promotional_price: number | null;
    stock_quantity: number;
    weight: number | null;
    dimensions: string;
    is_original: boolean;
    is_featured: boolean;
    status: 'draft' | 'active' | 'inactive' | 'out_of_stock';
    views: number;
    seo_title: string;
    seo_description: string;
    seo_keywords: string;
    created_at: string;
    updated_at: string;
    deleted_at: string | null;
    brand: Brand | null;
    category: Category | null;
    compatible_vehicles: Array<{ id: number; full_name: string }>;
    media: MediaFile[];
}

// Tipos específicos para componentes de produto
export interface BaseProduct {
    id: number;
    slug: string;
    price: number;
    promotional_price?: number;
    is_featured: boolean;
    main_image_url?: string;
    url: string;
    brand: {
        name: string;
    };
}

export interface ProductVehicle extends BaseProduct {
    model: string;
    year_manufacture: number;
    mileage: number;
    color: string;
    fuel_type: string;
    transmission: string;
    is_negotiable: boolean;
    category: {
        name: string;
        slug: string;
    };
}

export interface ProductPart extends BaseProduct {
    name: string;
    stock_quantity: number;
    is_original: boolean;
}

export type ProductItem = ProductVehicle | ProductPart;

export interface User {
    id: number;
    name: string;
    email: string;
    avatar?: string;
    email_verified_at: string | null;
    created_at: string;
    updated_at: string;
    [key: string]: unknown; // This allows for additional properties...
}

export interface Advertisement {
    id?: number;
    user_id: number;
    vehicle_id: number;
    title: string;
    description: string;
    price: number;
    status: string;
    is_featured: boolean;
    is_negotiable: boolean;
    contact_phone: string;
    contact_email: string;
    location: string;
    latitude: number | null;
    longitude: number | null;
    featured_until?: string;
    published_at: string | null;
    sold_at?: string;
    expires_at: string | null;
    rejection_reason: string | null;
    created_at: string;
    updated_at: string;
    vehicle?: Vehicle;
    user?: User;
    featured_image?: MediaFile;
    images?: Array<{ id: number; original_url: string }>;
    featured_image_url?: string;
}

export interface ImageFile {
    id?: number;
    file: File;
    preview: string;
    name: string;
    size: number;
}
