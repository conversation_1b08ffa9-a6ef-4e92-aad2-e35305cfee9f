import { Button } from '@/components/ui/button';
import { useForm } from '@inertiajs/react';
import { useState } from 'react';
import {
    FaCar,
    FaChevronDown,
    FaChevronUp,
    FaMotorcycle,
    FaSearch,
    FaTools,
    FaTruck,
} from 'react-icons/fa';

type Category = {
    id: number;
    name: string;
    slug: string;
    icon: React.ReactNode;
};

type FilterOptions = {
    categories: Array<{ id: number; name: string }>;
    brands: Array<{ id: number; name: string }>;
    models: Array<{ id: number; name: string }>;
    cities: Array<{ id: number; name: string; state: string }>;
};

type AdvancedSearchProps = {
    initialData?: {
        query?: string;
        category?: string;
        minPrice?: number;
        maxPrice?: number;
        condition?: 'new' | 'used' | 'all';
        city?: string;
        state?: string;
    };
    filterOptions?: Partial<FilterOptions>;
    className?: string;
    onSearch?: (filters: Record<string, any>) => void;
};

const defaultCategories: Category[] = [
    { id: 1, name: '<PERSON><PERSON>', slug: 'carros', icon: <FaCar /> },
    { id: 2, name: 'Motos', slug: 'motos', icon: <FaMotorcycle /> },
    { id: 3, name: 'Caminhões', slug: 'caminhoes', icon: <FaTruck /> },
    { id: 4, name: 'Peças', slug: 'pecas', icon: <FaTools /> },
];

export default function AdvancedSearch({
    initialData = {},
    filterOptions = {},
    className = '',
    onSearch,
}: AdvancedSearchProps) {
    const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);
    const [selectedCategory, setSelectedCategory] = useState<Category | null>(
        initialData.category
            ? defaultCategories.find((c) => c.slug === initialData.category) ||
                  null
            : null,
    );

    const { data, setData, get } = useForm({
        query: initialData.query || '',
        category: initialData.category || '',
        minPrice: initialData.minPrice || '',
        maxPrice: initialData.maxPrice || '',
        condition: initialData.condition || 'all',
        city: initialData.city || '',
        state: initialData.state || '',
    });

    const handleCategorySelect = (category: Category) => {
        setSelectedCategory(category);
        setData('category', category.slug);
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();

        // Remove campos vazios
        const filters = Object.fromEntries(
            Object.entries(data).filter(([_, value]) => value !== ''),
        );

        if (onSearch) {
            onSearch(filters);
        } else {
            // Se não houver callback, redireciona para a página de busca
            get(route('ads.index', filters), {
                preserveScroll: true,
                preserveState: true,
            });
        }
    };

    return (
        <div className={`w-full ${className}`}>
            <form onSubmit={handleSubmit} className="space-y-4">
                {/* Categorias */}
                <div className="flex flex-wrap gap-2">
                    {defaultCategories.map((category) => (
                        <button
                            key={category.id}
                            type="button"
                            onClick={() => handleCategorySelect(category)}
                            className={`flex h-24 w-24 flex-col items-center justify-center rounded-lg border-2 transition-all ${
                                selectedCategory?.id === category.id
                                    ? 'border-orange-500 bg-orange-50 text-orange-600'
                                    : 'border-gray-200 hover:border-orange-300 hover:bg-orange-50'
                            }`}
                        >
                            <span className="mb-1 text-2xl">
                                {category.icon}
                            </span>
                            <span className="text-sm font-medium">
                                {category.name}
                            </span>
                        </button>
                    ))}
                </div>

                {/* Barra de busca principal */}
                <div className="relative">
                    <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                        <FaSearch className="text-gray-400" />
                    </div>
                    <input
                        type="text"
                        value={data.query}
                        onChange={(e) => setData('query', e.target.value)}
                        className="block w-full rounded-lg border border-gray-300 bg-white p-4 pl-10 text-sm text-gray-900 focus:border-orange-500 focus:ring-orange-500"
                        placeholder="O que você está procurando?"
                    />
                    <Button
                        type="submit"
                        className="absolute right-2.5 bottom-2.5 rounded-lg bg-orange-600 px-4 py-2 text-sm font-medium hover:bg-orange-700 focus:ring-4 focus:ring-orange-300 focus:outline-none"
                    >
                        Buscar
                    </Button>
                </div>

                {/* Filtros avançados */}
                <div className="mt-2">
                    <button
                        type="button"
                        onClick={() =>
                            setShowAdvancedFilters(!showAdvancedFilters)
                        }
                        className="flex items-center text-sm text-orange-600 hover:text-orange-700"
                    >
                        {showAdvancedFilters ? (
                            <>
                                <span>Menos filtros</span>
                                <FaChevronUp className="ml-1 text-xs" />
                            </>
                        ) : (
                            <>
                                <span>Mais filtros</span>
                                <FaChevronDown className="ml-1 text-xs" />
                            </>
                        )}
                    </button>

                    {showAdvancedFilters && (
                        <div className="mt-4 space-y-4 rounded-lg bg-gray-50 p-4">
                            <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
                                {/* Preço Mínimo */}
                                <div>
                                    <label
                                        htmlFor="minPrice"
                                        className="mb-1 block text-sm font-medium text-gray-700"
                                    >
                                        Preço mínimo
                                    </label>
                                    <div className="relative">
                                        <span className="absolute top-1/2 left-3 -translate-y-1/2 text-gray-500">
                                            R$
                                        </span>
                                        <input
                                            type="number"
                                            id="minPrice"
                                            value={data.minPrice}
                                            onChange={(e) =>
                                                setData(
                                                    'minPrice',
                                                    e.target.value,
                                                )
                                            }
                                            className="w-full rounded-md border border-gray-300 py-2 pr-3 pl-10 focus:border-orange-500 focus:ring-orange-500"
                                            placeholder="0,00"
                                            min="0"
                                            step="1000"
                                        />
                                    </div>
                                </div>

                                {/* Preço Máximo */}
                                <div>
                                    <label
                                        htmlFor="maxPrice"
                                        className="mb-1 block text-sm font-medium text-gray-700"
                                    >
                                        Preço máximo
                                    </label>
                                    <div className="relative">
                                        <span className="absolute top-1/2 left-3 -translate-y-1/2 text-gray-500">
                                            R$
                                        </span>
                                        <input
                                            type="number"
                                            id="maxPrice"
                                            value={data.maxPrice}
                                            onChange={(e) =>
                                                setData(
                                                    'maxPrice',
                                                    e.target.value,
                                                )
                                            }
                                            className="w-full rounded-md border border-gray-300 py-2 pr-3 pl-10 focus:border-orange-500 focus:ring-orange-500"
                                            placeholder="100.000,00"
                                            min="0"
                                            step="1000"
                                        />
                                    </div>
                                </div>

                                {/* Condição */}
                                <div>
                                    <label
                                        htmlFor="condition"
                                        className="mb-1 block text-sm font-medium text-gray-700"
                                    >
                                        Condição
                                    </label>
                                    <select
                                        id="condition"
                                        value={data.condition}
                                        onChange={(e) =>
                                            setData(
                                                'condition',
                                                e.target.value as any,
                                            )
                                        }
                                        className="w-full rounded-md border border-gray-300 px-3 py-2 focus:border-orange-500 focus:ring-orange-500"
                                    >
                                        <option value="all">Todos</option>
                                        <option value="new">Novo</option>
                                        <option value="used">Usado</option>
                                    </select>
                                </div>

                                {/* Cidade */}
                                <div>
                                    <label
                                        htmlFor="city"
                                        className="mb-1 block text-sm font-medium text-gray-700"
                                    >
                                        Cidade
                                    </label>
                                    <select
                                        id="city"
                                        value={data.city}
                                        onChange={(e) =>
                                            setData('city', e.target.value)
                                        }
                                        className="w-full rounded-md border border-gray-300 px-3 py-2 focus:border-orange-500 focus:ring-orange-500"
                                        disabled={!filterOptions.cities?.length}
                                    >
                                        <option value="">
                                            Todas as cidades
                                        </option>
                                        {filterOptions.cities?.map((city) => (
                                            <option
                                                key={city.id}
                                                value={city.name}
                                            >
                                                {city.name} - {city.state}
                                            </option>
                                        ))}
                                    </select>
                                </div>
                            </div>

                            {/* Botões de ação */}
                            <div className="flex justify-end space-x-3 pt-2">
                                <button
                                    type="button"
                                    onClick={() => {
                                        setData({
                                            query: '',
                                            category: '',
                                            minPrice: '',
                                            maxPrice: '',
                                            condition: 'all',
                                            city: '',
                                            state: '',
                                        });
                                        setSelectedCategory(null);
                                    }}
                                    className="rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 focus:outline-none"
                                >
                                    Limpar filtros
                                </button>
                                <button
                                    type="submit"
                                    className="rounded-md border border-transparent bg-orange-600 px-4 py-2 text-sm font-medium text-white hover:bg-orange-700 focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 focus:outline-none"
                                >
                                    Aplicar filtros
                                </button>
                            </div>
                        </div>
                    )}
                </div>
            </form>
        </div>
    );
}
