import AdminLayout from '@/components/Layout/AdminLayout';
import {
    Card,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle,
} from '@/components/ui/card';
import { Head, Link } from '@inertiajs/react';
import { ArrowLeft } from 'lucide-react';
import AdvertisementForm from './Form';

export default function AdvertisementCreate({
    vehicles,
    users,
    statuses,
}: any) {
    return (
        <AdminLayout>
            <Head title="Criar An<PERSON>" />

            <div className="space-y-6">
                <div className="flex items-center justify-between">
                    <div>
                        <Link
                            href={route('admin.advertisements.index')}
                            className="flex items-center text-sm text-muted-foreground hover:text-foreground"
                        >
                            <ArrowLeft className="mr-1 h-4 w-4" /> Voltar para a
                            lista
                        </Link>
                        <h1 className="mt-2 text-2xl font-bold tracking-tight">
                            Criar <PERSON><PERSON>
                        </h1>
                    </div>
                </div>

                <Card>
                    <CardHeader>
                        <CardTitle>Informações do Anúncio</CardTitle>
                        <CardDescription>
                            Preencha os campos abaixo para criar um novo
                            anúncio.
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <AdvertisementForm
                            vehicles={vehicles}
                            users={users}
                            statuses={statuses}
                        />
                    </CardContent>
                </Card>
            </div>
        </AdminLayout>
    );
}
