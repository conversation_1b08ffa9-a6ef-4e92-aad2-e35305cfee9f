<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Advertisement;
use App\Models\User;
use App\Models\Part;
use App\Models\Offer;
use App\Models\Chat;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;
use Carbon\Carbon;

class ReportController extends Controller
{
    /**
     * Display the reports dashboard.
     */
    public function index(Request $request)
    {
        $period = $request->get('period', '30'); // days
        $startDate = now()->subDays($period);
        $endDate = now();

        $data = [
            'overview' => $this->getOverviewStats($startDate, $endDate),
            'users' => $this->getUserStats($startDate, $endDate),
            'advertisements' => $this->getAdvertisementStats($startDate, $endDate),
            'parts' => $this->getPartStats($startDate, $endDate),
            'offers' => $this->getOfferStats($startDate, $endDate),
            'revenue' => $this->getRevenueStats($startDate, $endDate),
            'charts' => $this->getChartData($startDate, $endDate),
        ];

        return Inertia::render('Admin/Reports/Index', [
            'data' => $data,
            'period' => $period,
            'startDate' => $startDate->format('Y-m-d'),
            'endDate' => $endDate->format('Y-m-d'),
        ]);
    }

    /**
     * Get overview statistics.
     */
    private function getOverviewStats($startDate, $endDate)
    {
        return [
            'total_users' => User::count(),
            'new_users' => User::whereBetween('created_at', [$startDate, $endDate])->count(),
            'active_users' => User::where('last_login_at', '>=', $startDate)->count(),
            'total_advertisements' => Advertisement::count(),
            'new_advertisements' => Advertisement::whereBetween('created_at', [$startDate, $endDate])->count(),
            'active_advertisements' => Advertisement::where('status', 'active')->count(),
            'total_parts' => Part::count(),
            'new_parts' => Part::whereBetween('created_at', [$startDate, $endDate])->count(),
            'total_offers' => Offer::count(),
            'new_offers' => Offer::whereBetween('created_at', [$startDate, $endDate])->count(),
        ];
    }

    /**
     * Get user statistics.
     */
    private function getUserStats($startDate, $endDate)
    {
        $userRegistrations = User::selectRaw('DATE(created_at) as date, COUNT(*) as count')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        $usersByType = User::selectRaw('account_type, COUNT(*) as count')
            ->groupBy('account_type')
            ->get();

        $topSellers = User::withCount(['advertisements' => function ($query) use ($startDate, $endDate) {
                $query->whereBetween('created_at', [$startDate, $endDate]);
            }])
            ->having('advertisements_count', '>', 0)
            ->orderBy('advertisements_count', 'desc')
            ->limit(10)
            ->get();

        return [
            'registrations_chart' => $userRegistrations,
            'by_type' => $usersByType,
            'top_sellers' => $topSellers,
            'verification_stats' => [
                'verified' => User::whereNotNull('email_verified_at')->count(),
                'unverified' => User::whereNull('email_verified_at')->count(),
            ],
        ];
    }

    /**
     * Get advertisement statistics.
     */
    private function getAdvertisementStats($startDate, $endDate)
    {
        $adsByStatus = Advertisement::selectRaw('status, COUNT(*) as count')
            ->groupBy('status')
            ->get();

        $adsByCategory = Advertisement::join('vehicles', 'advertisements.vehicle_id', '=', 'vehicles.id')
            ->join('categories', 'vehicles.category_id', '=', 'categories.id')
            ->selectRaw('categories.name as category, COUNT(*) as count')
            ->whereBetween('advertisements.created_at', [$startDate, $endDate])
            ->groupBy('categories.name')
            ->orderBy('count', 'desc')
            ->get();

        $adsByBrand = Advertisement::join('vehicles', 'advertisements.vehicle_id', '=', 'vehicles.id')
            ->join('brands', 'vehicles.brand_id', '=', 'brands.id')
            ->selectRaw('brands.name as brand, COUNT(*) as count')
            ->whereBetween('advertisements.created_at', [$startDate, $endDate])
            ->groupBy('brands.name')
            ->orderBy('count', 'desc')
            ->limit(10)
            ->get();

        $priceRanges = Advertisement::selectRaw('
                CASE 
                    WHEN price < 10000 THEN "Até R$ 10.000"
                    WHEN price < 25000 THEN "R$ 10.000 - R$ 25.000"
                    WHEN price < 50000 THEN "R$ 25.000 - R$ 50.000"
                    WHEN price < 100000 THEN "R$ 50.000 - R$ 100.000"
                    ELSE "Acima de R$ 100.000"
                END as price_range,
                COUNT(*) as count
            ')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->groupBy('price_range')
            ->get();

        return [
            'by_status' => $adsByStatus,
            'by_category' => $adsByCategory,
            'by_brand' => $adsByBrand,
            'price_ranges' => $priceRanges,
            'average_price' => Advertisement::whereBetween('created_at', [$startDate, $endDate])->avg('price'),
            'total_value' => Advertisement::whereBetween('created_at', [$startDate, $endDate])->sum('price'),
        ];
    }

    /**
     * Get part statistics.
     */
    private function getPartStats($startDate, $endDate)
    {
        $partsByCategory = Part::join('categories', 'parts.category_id', '=', 'categories.id')
            ->selectRaw('categories.name as category, COUNT(*) as count')
            ->whereBetween('parts.created_at', [$startDate, $endDate])
            ->groupBy('categories.name')
            ->orderBy('count', 'desc')
            ->get();

        $partsByCondition = Part::selectRaw('condition, COUNT(*) as count')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->groupBy('condition')
            ->get();

        return [
            'by_category' => $partsByCategory,
            'by_condition' => $partsByCondition,
            'average_price' => Part::whereBetween('created_at', [$startDate, $endDate])->avg('price'),
            'total_value' => Part::whereBetween('created_at', [$startDate, $endDate])->sum('price'),
        ];
    }

    /**
     * Get offer statistics.
     */
    private function getOfferStats($startDate, $endDate)
    {
        $offersByStatus = Offer::selectRaw('status, COUNT(*) as count')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->groupBy('status')
            ->get();

        $acceptanceRate = Offer::whereBetween('created_at', [$startDate, $endDate])
            ->selectRaw('
                COUNT(*) as total,
                SUM(CASE WHEN status = "accepted" THEN 1 ELSE 0 END) as accepted
            ')
            ->first();

        $averageOfferValue = Offer::whereBetween('created_at', [$startDate, $endDate])->avg('amount');

        return [
            'by_status' => $offersByStatus,
            'acceptance_rate' => $acceptanceRate->total > 0 ? ($acceptanceRate->accepted / $acceptanceRate->total) * 100 : 0,
            'average_value' => $averageOfferValue,
            'total_value' => Offer::whereBetween('created_at', [$startDate, $endDate])->sum('amount'),
        ];
    }

    /**
     * Get revenue statistics (if applicable).
     */
    private function getRevenueStats($startDate, $endDate)
    {
        // This would be based on your revenue model
        // For now, we'll calculate based on featured ads or subscription fees
        
        $featuredAdsRevenue = Advertisement::where('is_featured', true)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->count() * 50; // Assuming R$ 50 per featured ad

        return [
            'featured_ads' => $featuredAdsRevenue,
            'total_revenue' => $featuredAdsRevenue,
            'projected_monthly' => $featuredAdsRevenue * (30 / $startDate->diffInDays($endDate)),
        ];
    }

    /**
     * Get chart data for visualizations.
     */
    private function getChartData($startDate, $endDate)
    {
        // Daily registrations
        $dailyRegistrations = User::selectRaw('DATE(created_at) as date, COUNT(*) as count')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->groupBy('date')
            ->orderBy('date')
            ->get()
            ->map(function ($item) {
                return [
                    'date' => $item->date,
                    'users' => $item->count,
                ];
            });

        // Daily advertisements
        $dailyAds = Advertisement::selectRaw('DATE(created_at) as date, COUNT(*) as count')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->groupBy('date')
            ->orderBy('date')
            ->get()
            ->map(function ($item) {
                return [
                    'date' => $item->date,
                    'advertisements' => $item->count,
                ];
            });

        // Merge data for combined chart
        $combinedData = collect();
        $dates = collect($dailyRegistrations->pluck('date'))
            ->merge($dailyAds->pluck('date'))
            ->unique()
            ->sort()
            ->values();

        foreach ($dates as $date) {
            $userCount = $dailyRegistrations->firstWhere('date', $date)['users'] ?? 0;
            $adCount = $dailyAds->firstWhere('date', $date)['advertisements'] ?? 0;
            
            $combinedData->push([
                'date' => $date,
                'users' => $userCount,
                'advertisements' => $adCount,
            ]);
        }

        return [
            'daily_activity' => $combinedData,
            'user_growth' => $dailyRegistrations,
            'ad_growth' => $dailyAds,
        ];
    }

    /**
     * Export reports to CSV.
     */
    public function export(Request $request)
    {
        $type = $request->get('type', 'overview');
        $period = $request->get('period', '30');
        $startDate = now()->subDays($period);
        $endDate = now();

        switch ($type) {
            case 'users':
                return $this->exportUsers($startDate, $endDate);
            case 'advertisements':
                return $this->exportAdvertisements($startDate, $endDate);
            case 'offers':
                return $this->exportOffers($startDate, $endDate);
            default:
                return $this->exportOverview($startDate, $endDate);
        }
    }

    /**
     * Export users data.
     */
    private function exportUsers($startDate, $endDate)
    {
        $users = User::whereBetween('created_at', [$startDate, $endDate])
            ->select(['id', 'name', 'email', 'account_type', 'created_at', 'email_verified_at'])
            ->get();

        $filename = 'users_report_' . now()->format('Y-m-d') . '.csv';
        
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"$filename\"",
        ];

        $callback = function () use ($users) {
            $file = fopen('php://output', 'w');
            fputcsv($file, ['ID', 'Nome', 'Email', 'Tipo de Conta', 'Data de Cadastro', 'Email Verificado']);

            foreach ($users as $user) {
                fputcsv($file, [
                    $user->id,
                    $user->name,
                    $user->email,
                    $user->account_type,
                    $user->created_at->format('d/m/Y H:i'),
                    $user->email_verified_at ? 'Sim' : 'Não',
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Export advertisements data.
     */
    private function exportAdvertisements($startDate, $endDate)
    {
        $ads = Advertisement::with(['user', 'vehicle.brand', 'vehicle.model'])
            ->whereBetween('created_at', [$startDate, $endDate])
            ->get();

        $filename = 'advertisements_report_' . now()->format('Y-m-d') . '.csv';
        
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"$filename\"",
        ];

        $callback = function () use ($ads) {
            $file = fopen('php://output', 'w');
            fputcsv($file, ['ID', 'Título', 'Preço', 'Marca', 'Modelo', 'Vendedor', 'Status', 'Data de Criação']);

            foreach ($ads as $ad) {
                fputcsv($file, [
                    $ad->id,
                    $ad->title,
                    'R$ ' . number_format($ad->price, 2, ',', '.'),
                    $ad->vehicle->brand->name ?? '',
                    $ad->vehicle->model->name ?? '',
                    $ad->user->name,
                    $ad->status,
                    $ad->created_at->format('d/m/Y H:i'),
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Export offers data.
     */
    private function exportOffers($startDate, $endDate)
    {
        $offers = Offer::with(['user', 'advertisement'])
            ->whereBetween('created_at', [$startDate, $endDate])
            ->get();

        $filename = 'offers_report_' . now()->format('Y-m-d') . '.csv';
        
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"$filename\"",
        ];

        $callback = function () use ($offers) {
            $file = fopen('php://output', 'w');
            fputcsv($file, ['ID', 'Valor da Oferta', 'Anúncio', 'Comprador', 'Status', 'Data da Oferta']);

            foreach ($offers as $offer) {
                fputcsv($file, [
                    $offer->id,
                    'R$ ' . number_format($offer->amount, 2, ',', '.'),
                    $offer->advertisement->title,
                    $offer->user->name,
                    $offer->status,
                    $offer->created_at->format('d/m/Y H:i'),
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Export overview data.
     */
    private function exportOverview($startDate, $endDate)
    {
        $overview = $this->getOverviewStats($startDate, $endDate);
        
        $filename = 'overview_report_' . now()->format('Y-m-d') . '.csv';
        
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"$filename\"",
        ];

        $callback = function () use ($overview) {
            $file = fopen('php://output', 'w');
            fputcsv($file, ['Métrica', 'Valor']);

            foreach ($overview as $key => $value) {
                $label = str_replace('_', ' ', ucfirst($key));
                fputcsv($file, [$label, $value]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
