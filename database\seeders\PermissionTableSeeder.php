<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class PermissionTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Reset cached roles and permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Advertisement Permissions
        $permissions = [
            // Advertisement Permissions
            'view_any_advertisement',
            'view_advertisement',
            'create_advertisement',
            'update_advertisement',
            'delete_advertisement',
            'restore_advertisement',
            'force_delete_advertisement',
            'approve_advertisement',
            'reject_advertisement',
            'publish_advertisement',
            'mark_sold_advertisement',
            'manage_advertisement', // Super admin permission
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['name' => $permission, 'guard_name' => 'web']);
        }

        // Create roles and assign created permissions
        $adminRole = Role::firstOrCreate(['name' => 'admin', 'guard_name' => 'web']);
        $moderatorRole = Role::firstOrCreate(['name' => 'moderator', 'guard_name' => 'web']);
        $userRole = Role::firstOrCreate(['name' => 'user', 'guard_name' => 'web']);

        // Admin gets all permissions
        $adminRole->givePermissionTo(Permission::all());

        // Moderator permissions
        $moderatorPermissions = [
            'view_any_advertisement',
            'view_advertisement',
            'approve_advertisement',
            'reject_advertisement',
            'publish_advertisement',
        ];
        $moderatorRole->givePermissionTo($moderatorPermissions);

        // User permissions
        $userPermissions = [
            'view_advertisement',
            'create_advertisement',
            'update_advertisement',
            'delete_advertisement',
            'mark_sold_advertisement',
        ];
        $userRole->givePermissionTo($userPermissions);
    }
}
