import { login, logout, register } from '@/routes';
import { PageProps } from '@/types';
import { Link, usePage } from '@inertiajs/react';
import { useEffect, useState } from 'react';
import {
    FaBars,
    FaBell,
    FaPlus,
    FaSearch,
    FaTimes,
    FaUser,
} from 'react-icons/fa';

export default function Header() {
    const { auth } = usePage<PageProps>().props;
    const [isMenuOpen, setIsMenuOpen] = useState(false);
    const [searchQuery, setSearchQuery] = useState('');
    const [isScrolled, setIsScrolled] = useState(false);

    // Efeito para adicionar sombra ao scrollar a página
    useEffect(() => {
        const handleScroll = () => {
            setIsScrolled(window.scrollY > 10);
        };

        window.addEventListener('scroll', handleScroll);
        return () => window.removeEventListener('scroll', handleScroll);
    }, []);

    const toggleMenu = () => {
        setIsMenuOpen(!isMenuOpen);
    };

    const handleSearch = (e: React.FormEvent) => {
        e.preventDefault();
        if (searchQuery.trim()) {
            window.location.href = `/anuncios?search=${encodeURIComponent(searchQuery)}`;
        }
    };

    return (
        <header
            className={`fixed z-50 w-full transition-all duration-300 ${
                isScrolled
                    ? 'bg-white shadow-md'
                    : 'bg-white/90 backdrop-blur-sm'
            }`}
        >
            <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                <div className="flex h-16 items-center justify-between">
                    {/* Logo */}
                    <div className="flex-shrink-0">
                        <Link href="/" className="flex items-center">
                            <span className="text-2xl font-bold text-orange-600">
                                AutoMercado
                            </span>
                        </Link>
                    </div>

                    {/* Barra de pesquisa (desktop) */}
                    <div className="mx-4 hidden max-w-2xl flex-1 md:flex">
                        <form onSubmit={handleSearch} className="w-full">
                            <div className="relative">
                                <input
                                    type="text"
                                    placeholder="O que você está procurando?"
                                    className="w-full rounded-l-md border border-gray-300 px-4 py-2 focus:border-transparent focus:ring-2 focus:ring-orange-500 focus:outline-none"
                                    value={searchQuery}
                                    onChange={(e) =>
                                        setSearchQuery(e.target.value)
                                    }
                                />
                                <button
                                    type="submit"
                                    className="absolute top-0 right-0 h-full rounded-r-md bg-orange-600 px-4 text-white hover:bg-orange-700 focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 focus:outline-none"
                                >
                                    <FaSearch />
                                </button>
                            </div>
                        </form>
                    </div>

                    {/* Menu de navegação (desktop) */}
                    <nav className="hidden items-center space-x-4 md:flex">
                        <Link
                            href="/anuncios/criar"
                            className="flex items-center rounded-md bg-orange-600 px-4 py-2 text-sm font-medium text-white hover:bg-orange-700"
                        >
                            <FaPlus className="mr-2" />
                            Anunciar
                        </Link>

                        {auth.user ? (
                            <div className="flex items-center space-x-4">
                                <Link
                                    href="/notificacoes"
                                    className="relative p-2 text-gray-600 hover:text-orange-600"
                                >
                                    <FaBell className="text-xl" />
                                    <span className="absolute top-0 right-0 h-2 w-2 rounded-full bg-orange-600"></span>
                                </Link>
                                <Link
                                    href="/perfil"
                                    className="flex items-center space-x-2 text-gray-700 hover:text-orange-600"
                                >
                                    {auth.user.avatar ? (
                                        <img
                                            src={auth.user.avatar}
                                            alt={auth.user.name}
                                            className="h-8 w-8 rounded-full"
                                        />
                                    ) : (
                                        <div className="flex h-8 w-8 items-center justify-center rounded-full bg-orange-100">
                                            <FaUser className="text-orange-600" />
                                        </div>
                                    )}
                                    <span className="hidden lg:inline">
                                        {auth.user.name.split(' ')[0]}
                                    </span>
                                </Link>
                            </div>
                        ) : (
                            <div className="flex space-x-2">
                                <Link
                                    href={login.url()}
                                    className="px-4 py-2 text-sm font-medium text-gray-700 hover:text-orange-600"
                                >
                                    Entrar
                                </Link>
                                <Link
                                    href={register.url()}
                                    className="rounded-md border border-orange-600 px-4 py-2 text-sm font-medium text-orange-600 hover:bg-orange-50"
                                >
                                    Criar conta
                                </Link>
                            </div>
                        )}
                    </nav>

                    {/* Botão do menu móvel */}
                    <div className="md:hidden">
                        <button
                            onClick={toggleMenu}
                            className="inline-flex items-center justify-center rounded-md p-2 text-gray-700 hover:bg-gray-100 hover:text-orange-600 focus:ring-2 focus:ring-orange-500 focus:outline-none focus:ring-inset"
                        >
                            {isMenuOpen ? <FaTimes /> : <FaBars />}
                        </button>
                    </div>
                </div>

                {/* Barra de pesquisa (mobile) */}
                <div className="py-3 md:hidden">
                    <form onSubmit={handleSearch}>
                        <div className="relative">
                            <input
                                type="text"
                                placeholder="Buscar anúncios..."
                                className="w-full rounded-md border border-gray-300 px-4 py-2 focus:border-transparent focus:ring-2 focus:ring-orange-500 focus:outline-none"
                                value={searchQuery}
                                onChange={(e) => setSearchQuery(e.target.value)}
                            />
                            <button
                                type="submit"
                                className="absolute top-0 right-0 h-full px-4 text-gray-500 hover:text-orange-600 focus:outline-none"
                            >
                                <FaSearch />
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            {/* Menu móvel */}
            {isMenuOpen && (
                <div className="border-t border-gray-200 bg-white md:hidden">
                    <div className="space-y-1 px-2 pt-2 pb-3">
                        <Link
                            href="/anuncios"
                            className="block px-3 py-2 text-base font-medium text-gray-700 hover:bg-gray-50 hover:text-orange-600"
                            onClick={() => setIsMenuOpen(false)}
                        >
                            Explorar
                        </Link>
                        <Link
                            href="/anuncios/criar"
                            className="flex items-center px-3 py-2 text-base font-medium text-orange-600 hover:bg-orange-50"
                            onClick={() => setIsMenuOpen(false)}
                        >
                            <FaPlus className="mr-2" />
                            Anunciar
                        </Link>

                        {auth.user ? (
                            <>
                                <Link
                                    href="/perfil"
                                    className="block px-3 py-2 text-base font-medium text-gray-700 hover:bg-gray-50 hover:text-orange-600"
                                    onClick={() => setIsMenuOpen(false)}
                                >
                                    Meu perfil
                                </Link>
                                <Link
                                    href={logout.url()}
                                    method="post"
                                    as="button"
                                    className="w-full px-3 py-2 text-left text-base font-medium text-gray-700 hover:bg-gray-50 hover:text-orange-600"
                                    onClick={() => setIsMenuOpen(false)}
                                >
                                    Sair
                                </Link>
                            </>
                        ) : (
                            <>
                                <Link
                                    href={login.url()}
                                    className="block px-3 py-2 text-base font-medium text-gray-700 hover:bg-gray-50 hover:text-orange-600"
                                    onClick={() => setIsMenuOpen(false)}
                                >
                                    Entrar
                                </Link>
                                <Link
                                    href={register.url()}
                                    className="block px-3 py-2 text-base font-medium text-orange-600 hover:bg-orange-50"
                                    onClick={() => setIsMenuOpen(false)}
                                >
                                    Criar conta
                                </Link>
                            </>
                        )}
                    </div>
                </div>
            )}
        </header>
    );
}
