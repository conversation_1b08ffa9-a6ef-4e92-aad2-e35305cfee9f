<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->string('phone')->nullable()->after('email');
            $table->string('cpf_cnpj', 20)->unique()->nullable()->after('phone');
            $table->enum('type', ['individual', 'company'])->default('individual')->after('cpf_cnpj');
            $table->date('birth_date')->nullable()->after('type');
            $table->string('avatar')->nullable()->after('birth_date');
            $table->enum('status', ['active', 'inactive', 'suspended', 'pending'])->default('pending')->after('avatar');
            $table->timestamp('last_login_at')->nullable()->after('remember_token');
            $table->softDeletes();
            
            // Índices
            $table->index('status');
            $table->index('type');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn([
                'phone',
                'cpf_cnpj',
                'type',
                'birth_date',
                'avatar',
                'status',
                'last_login_at'
            ]);
            
            $table->dropSoftDeletes();
        });
    }
};
