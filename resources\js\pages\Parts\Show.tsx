import { Head, Link, router } from '@inertiajs/react';
import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
    Heart, 
    Share2, 
    MapPin, 
    Calendar, 
    Eye,
    Phone,
    Mail,
    MessageCircle,
    ArrowLeft,
    Star,
    Shield,
    Truck,
    RotateCcw
} from 'lucide-react';
import { ProductImage, AvatarImage } from '@/components/ui/responsive-image';

interface Part {
    id: number;
    name: string;
    description: string;
    price: number;
    condition: 'new' | 'used' | 'refurbished';
    status: 'active' | 'inactive' | 'sold';
    slug: string;
    views: number;
    created_at: string;
    images: any[];
    specifications?: Record<string, string>;
    warranty_months?: number;
    stock_quantity?: number;
    user: {
        id: number;
        name: string;
        location?: string;
        avatar?: string;
        phone?: string;
        email?: string;
        rating?: number;
        total_sales?: number;
        member_since?: string;
    };
    category: {
        id: number;
        name: string;
        slug: string;
    };
    compatible_vehicles?: string[];
    related_parts?: Part[];
}

interface Props {
    part: Part;
    isFavorited: boolean;
}

export default function PartShow({ part, isFavorited }: Props) {
    const [selectedImageIndex, setSelectedImageIndex] = useState(0);
    const [favorited, setFavorited] = useState(isFavorited);

    const handleFavorite = () => {
        router.post(`/pecas/${part.slug}/favoritar`, {}, {
            onSuccess: () => setFavorited(!favorited),
        });
    };

    const handleShare = () => {
        if (navigator.share) {
            navigator.share({
                title: part.name,
                text: part.description,
                url: window.location.href,
            });
        } else {
            navigator.clipboard.writeText(window.location.href);
        }
    };

    const getConditionBadge = (condition: string) => {
        const conditionConfig = {
            new: { label: 'Nova', variant: 'default' as const, icon: Star },
            used: { label: 'Usada', variant: 'secondary' as const, icon: RotateCcw },
            refurbished: { label: 'Recondicionada', variant: 'outline' as const, icon: Shield },
        };

        const config = conditionConfig[condition as keyof typeof conditionConfig] || { 
            label: condition, 
            variant: 'secondary' as const,
            icon: Star
        };
        
        return (
            <Badge variant={config.variant} className="flex items-center gap-1">
                <config.icon className="h-3 w-3" />
                {config.label}
            </Badge>
        );
    };

    const formatPrice = (price: number) => {
        return new Intl.NumberFormat('pt-BR', {
            style: 'currency',
            currency: 'BRL',
        }).format(price);
    };

    return (
        <>
            <Head title={part.name} />
            
            <div className="space-y-6">
                {/* Breadcrumb */}
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <Link href="/pecas" className="hover:text-primary">
                        Peças
                    </Link>
                    <span>/</span>
                    <Link href={`/pecas?category=${part.category.slug}`} className="hover:text-primary">
                        {part.category.name}
                    </Link>
                    <span>/</span>
                    <span className="text-foreground">{part.name}</span>
                </div>

                <div className="grid gap-6 lg:grid-cols-3">
                    {/* Images */}
                    <div className="lg:col-span-2">
                        <Card>
                            <CardContent className="p-0">
                                {/* Main Image */}
                                <div className="aspect-square relative">
                                    <ProductImage
                                        src={part.images[selectedImageIndex]?.url}
                                        alt={part.name}
                                        className="w-full h-full rounded-t-lg"
                                        featured={true}
                                    />
                                    <div className="absolute top-4 right-4 flex gap-2">
                                        <Button
                                            size="sm"
                                            variant={favorited ? 'default' : 'secondary'}
                                            onClick={handleFavorite}
                                        >
                                            <Heart className={`h-4 w-4 ${favorited ? 'fill-current' : ''}`} />
                                        </Button>
                                        <Button size="sm" variant="secondary" onClick={handleShare}>
                                            <Share2 className="h-4 w-4" />
                                        </Button>
                                    </div>
                                </div>

                                {/* Thumbnail Gallery */}
                                {part.images.length > 1 && (
                                    <div className="p-4">
                                        <div className="flex gap-2 overflow-x-auto">
                                            {part.images.map((image, index) => (
                                                <button
                                                    key={index}
                                                    onClick={() => setSelectedImageIndex(index)}
                                                    className={`flex-shrink-0 w-20 h-20 rounded-lg overflow-hidden border-2 ${
                                                        selectedImageIndex === index
                                                            ? 'border-primary'
                                                            : 'border-transparent'
                                                    }`}
                                                >
                                                    <ProductImage
                                                        src={image.url}
                                                        alt={`${part.name} ${index + 1}`}
                                                        className="w-full h-full object-cover"
                                                        featured={false}
                                                    />
                                                </button>
                                            ))}
                                        </div>
                                    </div>
                                )}
                            </CardContent>
                        </Card>
                    </div>

                    {/* Product Info */}
                    <div className="space-y-6">
                        {/* Basic Info */}
                        <Card>
                            <CardHeader>
                                <div className="flex items-start justify-between">
                                    <div className="space-y-2">
                                        <CardTitle className="text-2xl">{part.name}</CardTitle>
                                        <div className="flex items-center gap-2">
                                            {getConditionBadge(part.condition)}
                                            <Badge variant="outline">{part.category.name}</Badge>
                                        </div>
                                    </div>
                                </div>
                                <div className="text-3xl font-bold text-primary">
                                    {formatPrice(part.price)}
                                </div>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <p className="text-muted-foreground">{part.description}</p>

                                {/* Stock and Warranty */}
                                <div className="grid grid-cols-2 gap-4 text-sm">
                                    {part.stock_quantity && (
                                        <div className="flex items-center gap-2">
                                            <Truck className="h-4 w-4 text-muted-foreground" />
                                            <span>{part.stock_quantity} em estoque</span>
                                        </div>
                                    )}
                                    {part.warranty_months && (
                                        <div className="flex items-center gap-2">
                                            <Shield className="h-4 w-4 text-muted-foreground" />
                                            <span>{part.warranty_months} meses garantia</span>
                                        </div>
                                    )}
                                </div>

                                {/* Views and Date */}
                                <div className="flex items-center gap-4 text-xs text-muted-foreground">
                                    <div className="flex items-center gap-1">
                                        <Eye className="h-3 w-3" />
                                        {part.views} visualizações
                                    </div>
                                    <div className="flex items-center gap-1">
                                        <Calendar className="h-3 w-3" />
                                        {new Date(part.created_at).toLocaleDateString()}
                                    </div>
                                </div>

                                <Separator />

                                {/* Contact Buttons */}
                                <div className="space-y-2">
                                    <Button className="w-full" size="lg">
                                        <MessageCircle className="h-4 w-4 mr-2" />
                                        Conversar com Vendedor
                                    </Button>
                                    {part.user.phone && (
                                        <Button variant="outline" className="w-full">
                                            <Phone className="h-4 w-4 mr-2" />
                                            Ligar
                                        </Button>
                                    )}
                                </div>
                            </CardContent>
                        </Card>

                        {/* Seller Info */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Vendedor</CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="flex items-center gap-3">
                                    <AvatarImage
                                        src={part.user.avatar}
                                        alt={part.user.name}
                                        size="medium"
                                    />
                                    <div className="flex-1">
                                        <h3 className="font-semibold">{part.user.name}</h3>
                                        {part.user.location && (
                                            <div className="flex items-center gap-1 text-sm text-muted-foreground">
                                                <MapPin className="h-3 w-3" />
                                                {part.user.location}
                                            </div>
                                        )}
                                    </div>
                                </div>

                                {/* Seller Stats */}
                                <div className="grid grid-cols-2 gap-4 text-sm">
                                    {part.user.rating && (
                                        <div>
                                            <p className="text-muted-foreground">Avaliação</p>
                                            <div className="flex items-center gap-1">
                                                <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                                                <span className="font-medium">{part.user.rating.toFixed(1)}</span>
                                            </div>
                                        </div>
                                    )}
                                    {part.user.total_sales && (
                                        <div>
                                            <p className="text-muted-foreground">Vendas</p>
                                            <p className="font-medium">{part.user.total_sales}</p>
                                        </div>
                                    )}
                                </div>

                                {part.user.member_since && (
                                    <div className="text-xs text-muted-foreground">
                                        Membro desde {new Date(part.user.member_since).toLocaleDateString()}
                                    </div>
                                )}

                                <Button variant="outline" className="w-full" asChild>
                                    <Link href={`/usuarios/${part.user.id}`}>
                                        Ver Perfil
                                    </Link>
                                </Button>
                            </CardContent>
                        </Card>
                    </div>
                </div>

                {/* Specifications */}
                {part.specifications && Object.keys(part.specifications).length > 0 && (
                    <Card>
                        <CardHeader>
                            <CardTitle>Especificações</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="grid gap-4 md:grid-cols-2">
                                {Object.entries(part.specifications).map(([key, value]) => (
                                    <div key={key} className="flex justify-between">
                                        <span className="text-muted-foreground">{key}:</span>
                                        <span className="font-medium">{value}</span>
                                    </div>
                                ))}
                            </div>
                        </CardContent>
                    </Card>
                )}

                {/* Compatible Vehicles */}
                {part.compatible_vehicles && part.compatible_vehicles.length > 0 && (
                    <Card>
                        <CardHeader>
                            <CardTitle>Veículos Compatíveis</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="flex flex-wrap gap-2">
                                {part.compatible_vehicles.map((vehicle, index) => (
                                    <Badge key={index} variant="outline">
                                        {vehicle}
                                    </Badge>
                                ))}
                            </div>
                        </CardContent>
                    </Card>
                )}

                {/* Related Parts */}
                {part.related_parts && part.related_parts.length > 0 && (
                    <Card>
                        <CardHeader>
                            <CardTitle>Peças Relacionadas</CardTitle>
                            <CardDescription>
                                Outras peças que podem interessar
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                                {part.related_parts.slice(0, 4).map((relatedPart) => (
                                    <Link
                                        key={relatedPart.id}
                                        href={`/pecas/${relatedPart.slug}`}
                                        className="group"
                                    >
                                        <div className="space-y-2">
                                            <div className="aspect-square relative overflow-hidden rounded-lg">
                                                <ProductImage
                                                    src={relatedPart.images[0]?.url}
                                                    alt={relatedPart.name}
                                                    className="w-full h-full group-hover:scale-105 transition-transform"
                                                    featured={false}
                                                />
                                            </div>
                                            <div>
                                                <h4 className="font-medium text-sm line-clamp-2 group-hover:text-primary">
                                                    {relatedPart.name}
                                                </h4>
                                                <p className="text-lg font-bold text-primary">
                                                    {formatPrice(relatedPart.price)}
                                                </p>
                                            </div>
                                        </div>
                                    </Link>
                                ))}
                            </div>
                        </CardContent>
                    </Card>
                )}
            </div>
        </>
    );
}
