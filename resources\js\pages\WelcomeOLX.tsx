import HeroSectionOLX from '@/components/HeroSectionOLX';
import ProductGrid from '@/components/ProductGrid';
import MainLayout from '@/layouts/MainLayout';
import { PageProps } from '@/types';

interface Category {
    id: number;
    name: string;
    slug: string;
    description: string;
    icon?: string;
    image?: string;
    url: string;
    vehicles_count?: number;
    parts_count?: number;
}

interface Vehicle {
    id: number;
    title: string;
    slug: string;
    price: number;
    promotional_price?: number;
    year_manufacture: number;
    mileage: number;
    color: string;
    fuel_type: string;
    transmission: string;
    is_featured: boolean;
    is_negotiable: boolean;
    location: string;
    created_at: string;
    main_image?: {
        url: string;
    };
    url: string;
    brand: {
        name: string;
    };
    category: {
        name: string;
        slug: string;
    };
}

interface WelcomeProps extends PageProps {
    categories: Category[];
    featuredListings?: Vehicle[];
}

export default function WelcomeOLX({
    categories = [],
    featuredListings = [],
}: WelcomeProps) {
    return (
        <MainLayout categories={categories}>
            <div className="space-y-0">
                <HeroSectionOLX />

                <ProductGrid
                    title="Mais procurados em Calçados"
                    products={featuredListings}
                    showMoreLink="/anuncios"
                />
            </div>
        </MainLayout>
    );
}
