import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Link } from '@inertiajs/react';
import { Clock, Heart, MapPin } from 'lucide-react';
import { useState } from 'react';

interface Product {
    id: number;
    title: string;
    price: number;
    location: string;
    created_at: string;
    main_image?: {
        url: string;
    };
    category: {
        name: string;
    };
    brand: {
        name: string;
    };
    is_featured?: boolean;
    model?: string;
    year_manufacture?: number;
    mileage?: number;
    fuel_type?: string;
    transmission?: string;
}

interface ProductGridProps {
    title: string;
    products: Product[];
    showMoreLink?: string;
}

export default function ProductGrid({
    title,
    products,
    showMoreLink,
}: ProductGridProps) {
    const [favorites, setFavorites] = useState<Set<number>>(new Set());

    const toggleFavorite = (productId: number) => {
        setFavorites((prev) => {
            const newFavorites = new Set(prev);
            if (newFavorites.has(productId)) {
                newFavorites.delete(productId);
            } else {
                newFavorites.add(productId);
            }
            return newFavorites;
        });
    };

    const formatPrice = (price: number) => {
        return `R$ ${price.toFixed(2).replace('.', ',')}`;
    };

    const formatDate = (dateString: string) => {
        const date = new Date(dateString);
        const now = new Date();
        const diffTime = Math.abs(now.getTime() - date.getTime());
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

        if (diffDays === 1) return 'Hoje';
        if (diffDays === 2) return 'Ontem';
        if (diffDays <= 7) return `${diffDays} dias`;
        return date.toLocaleDateString('pt-BR');
    };

    return (
        <section className="bg-white py-12">
            <div className="container mx-auto px-4">
                <div className="mb-8 flex items-center justify-between">
                    <h2 className="text-2xl font-bold text-gray-900 lg:text-3xl">
                        {title}
                    </h2>
                    {showMoreLink && (
                        <Link
                            href={showMoreLink}
                            className="font-medium text-purple-600 hover:text-purple-700"
                        >
                            Ver todos
                        </Link>
                    )}
                </div>

                <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
                    {products.map((product) => (
                        <Card
                            key={product.id}
                            className="group cursor-pointer overflow-hidden border border-gray-200 transition-all duration-300 hover:shadow-lg"
                        >
                            <div className="relative">
                                <div className="relative aspect-square overflow-hidden bg-gray-100">
                                    <img
                                        src={
                                            product.main_image?.url ||
                                            '/placeholder.jpg'
                                        }
                                        alt={product.title}
                                        className="h-full w-full object-cover transition-transform duration-300 group-hover:scale-105"
                                    />
                                    <div className="bg-opacity-0 group-hover:bg-opacity-10 absolute inset-0 bg-black transition-all duration-300" />
                                </div>

                                {/* Favorite Button */}
                                <Button
                                    variant="ghost"
                                    size="icon"
                                    onClick={(e) => {
                                        e.preventDefault();
                                        toggleFavorite(product.id);
                                    }}
                                    className="absolute top-3 right-3 rounded-full bg-white/80 text-gray-600 shadow-sm hover:bg-white"
                                >
                                    <Heart
                                        className={`h-4 w-4 ${
                                            favorites.has(product.id)
                                                ? 'fill-red-500 text-red-500'
                                                : 'text-gray-600'
                                        }`}
                                    />
                                </Button>

                                {/* Featured Badge */}
                                {product.is_featured && (
                                    <div className="absolute top-3 left-3 rounded bg-orange-500 px-2 py-1 text-xs font-bold text-white">
                                        DESTAQUE
                                    </div>
                                )}
                            </div>

                            <CardContent className="p-4">
                                <Link href={`/veiculos/${product.id}`}>
                                    <div className="space-y-2">
                                        <h3 className="line-clamp-2 text-lg font-semibold text-gray-900 transition-colors group-hover:text-purple-600">
                                            {product.title ||
                                                `${product.brand?.name} ${product.model}`}
                                        </h3>

                                        <div className="flex items-center text-sm text-gray-600">
                                            <span className="rounded bg-gray-100 px-2 py-1 text-xs">
                                                {product.category.name}
                                            </span>
                                            {product.year_manufacture && (
                                                <>
                                                    <span className="mx-2">
                                                        •
                                                    </span>
                                                    <span>
                                                        {
                                                            product.year_manufacture
                                                        }
                                                    </span>
                                                </>
                                            )}
                                        </div>

                                        {/* Vehicle Details */}
                                        {(product.mileage ||
                                            product.fuel_type ||
                                            product.transmission) && (
                                            <div className="flex items-center space-x-2 text-xs text-gray-500">
                                                {product.mileage && (
                                                    <span>
                                                        {product.mileage.toLocaleString()}{' '}
                                                        km
                                                    </span>
                                                )}
                                                {product.fuel_type && (
                                                    <>
                                                        {product.mileage && (
                                                            <span>•</span>
                                                        )}
                                                        <span>
                                                            {product.fuel_type}
                                                        </span>
                                                    </>
                                                )}
                                                {product.transmission && (
                                                    <>
                                                        {(product.mileage ||
                                                            product.fuel_type) && (
                                                            <span>•</span>
                                                        )}
                                                        <span>
                                                            {
                                                                product.transmission
                                                            }
                                                        </span>
                                                    </>
                                                )}
                                            </div>
                                        )}

                                        <div className="flex items-center justify-between">
                                            <p className="text-xl font-bold text-purple-600">
                                                {formatPrice(product.price)}
                                            </p>
                                        </div>

                                        <div className="flex items-center justify-between text-sm text-gray-500">
                                            <div className="flex items-center">
                                                <MapPin className="mr-1 h-3 w-3" />
                                                <span className="truncate">
                                                    {product.location}
                                                </span>
                                            </div>
                                            <div className="flex items-center">
                                                <Clock className="mr-1 h-3 w-3" />
                                                <span>
                                                    {formatDate(
                                                        product.created_at,
                                                    )}
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </Link>
                            </CardContent>
                        </Card>
                    ))}
                </div>

                {/* Show More Button */}
                {showMoreLink && products.length > 8 && (
                    <div className="mt-8 text-center">
                        <Button asChild variant="outline" size="lg">
                            <Link href={showMoreLink}>Ver mais produtos</Link>
                        </Button>
                    </div>
                )}
            </div>
        </section>
    );
}
