<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Spatie\Permission\Models\Role;

class CreateAdminUser extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:create-admin-user';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create an admin user for testing';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        // Criar usuário admin
        $admin = User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Administrator',
                'password' => Hash::make('password'),
                'email_verified_at' => now(),
                'status' => 'active',
                'type' => 'individual'
            ]
        );

        // Verificar se o role admin existe
        $adminRole = Role::firstOrCreate(['name' => 'admin', 'guard_name' => 'web']);

        // Remover roles existentes e atribuir role admin
        $admin->syncRoles([$adminRole]);

        // Verificar se tem a permissão admin.access
        $adminPermission = \Spatie\Permission\Models\Permission::firstOrCreate([
            'name' => 'admin.access',
            'guard_name' => 'web'
        ]);

        // Dar a permissão ao role admin
        $adminRole->givePermissionTo($adminPermission);

        $this->info('Admin user created successfully!');
        $this->info('Email: <EMAIL>');
        $this->info('Password: password');

        // Verificar permissões
        $this->info('User roles: ' . $admin->roles->pluck('name')->implode(', '));
        $this->info('User permissions: ' . $admin->getAllPermissions()->pluck('name')->implode(', '));
        $this->info('Can access admin: ' . ($admin->can('admin.access') ? 'YES' : 'NO'));

        // Mostrar todos os usuários
        $users = User::all(['email', 'name']);
        $this->info('All users:');
        foreach ($users as $user) {
            $this->line("- {$user->name} ({$user->email})");
        }
    }
}
