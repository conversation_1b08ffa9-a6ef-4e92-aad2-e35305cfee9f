# Plano de Desenvolvimento - Loja Virtual de Veículos e Peças

## Visão Geral do Projeto

Plataforma de comércio eletrônico especializada em venda e aluguel de carros, motos e peças automotivas, construída com Laravel e React através do Inertia.js, focando em performance, SEO e experiência do usuário sem necessidade de APIs REST complexas.

## Arquitetura Técnica

### Stack Principal

- **Backend**: Laravel 11 com Inertia.js
- **Frontend**: React com TypeScript (via Inertia)
- **Build Tool**: Vite
- **Database**: MySQL 8.0
- **Cache/Queue**: Redis
- **Search**: Laravel Scout com Meilisearch
- **Real-time**: Laravel Echo + Pusher/Soketi

### Por que Inertia.js?

- Elimina a necessidade de API REST para a maioria das operações
- Mantém a simplicidade do roteamento Laravel
- SSR (Server-Side Rendering) nativo para melhor SEO
- Navegação SPA sem complexidade de gerenciamento de estado
- Validação de formulários integrada com Laravel
- Autenticação e autorização simplificadas

## Tecnologias e Ferramentas

### Backend (Laravel)

```php
// Pacotes principais
- laravel/framework: ^11.0
- inertiajs/inertia-laravel: ^1.0
- tightenco/ziggy: ^2.0 (rotas JavaScript)
- laravel/scout: ^10.0 (busca full-text)
- spatie/laravel-medialibrary: ^11.0 (gerenciamento de mídia)
- spatie/laravel-permission: ^6.0 (roles e permissões)
- laravel/sanctum: ^4.0 (autenticação SPA)
- laravel/socialite: ^5.0 (login social - opcional)
- barryvdh/laravel-debugbar: ^3.0 (desenvolvimento)
- laravel/horizon: ^5.0 (gerenciamento de filas)
- laravel/telescope: ^5.0 (debugging - desenvolvimento)
```

### Frontend (React com Inertia)

```json
// Dependências principais
{
  "@inertiajs/react": "^1.0",
  "react": "^18.2",
  "react-dom": "^18.2",
  "typescript": "^5.0",
  "@vitejs/plugin-react": "^4.0",
  "tailwindcss": "^3.4",
  "@headlessui/react": "^1.7",
  "@heroicons/react": "^2.0",
  "framer-motion": "^11.0",
  "react-hot-toast": "^2.4",
  "react-hook-form": "^7.48",
  "@hookform/resolvers": "^3.3",
  "zod": "^3.22",
  "date-fns": "^3.0",
  "recharts": "^2.10",
  "react-select": "^5.8",
  "react-dropzone": "^14.2",
  "swiper": "^11.0",
  "@tanstack/react-table": "^8.11"
}
```

## Estrutura do Projeto

### Organização de Diretórios

```
projeto-loja/
├── app/
│   ├── Http/
│   │   ├── Controllers/
│   │   │   ├── Admin/
│   │   │   │   ├── DashboardController.php
│   │   │   │   ├── VehicleController.php
│   │   │   │   ├── PartController.php
│   │   │   │   └── UserController.php
│   │   │   ├── Public/
│   │   │   │   ├── HomeController.php
│   │   │   │   ├── CatalogController.php
│   │   │   │   └── VehicleDetailController.php
│   │   │   ├── User/
│   │   │   │   ├── ProfileController.php
│   │   │   │   ├── AnnouncementController.php
│   │   │   │   └── MessageController.php
│   │   │   └── Auth/
│   │   ├── Middleware/
│   │   │   ├── HandleInertiaRequests.php
│   │   │   └── ShareInertiaData.php
│   │   └── Requests/
│   │       ├── VehicleRequest.php
│   │       └── PartRequest.php
│   ├── Models/
│   ├── Services/
│   └── Actions/
├── resources/
│   ├── js/
│   │   ├── app.tsx
│   │   ├── Components/
│   │   │   ├── Layout/
│   │   │   │   ├── AppLayout.tsx
│   │   │   │   ├── AdminLayout.tsx
│   │   │   │   └── PublicLayout.tsx
│   │   │   ├── UI/
│   │   │   │   ├── Button.tsx
│   │   │   │   ├── Input.tsx
│   │   │   │   ├── Modal.tsx
│   │   │   │   └── Card.tsx
│   │   │   └── Features/
│   │   │       ├── VehicleCard.tsx
│   │   │       ├── SearchFilters.tsx
│   │   │       └── ChatWidget.tsx
│   │   ├── Pages/
│   │   │   ├── Admin/
│   │   │   ├── Public/
│   │   │   └── User/
│   │   ├── Hooks/
│   │   ├── Utils/
│   │   └── Types/
│   └── css/
│       └── app.css
├── routes/
│   ├── web.php
│   ├── admin.php
│   └── channels.php
└── database/
    ├── migrations/
    ├── seeders/
    └── factories/
```

## Modelo de Dados Aprimorado

### Estrutura do Banco de Dados

```sql
-- Tabelas principais com campos otimizados

-- Usuários
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    email_verified_at TIMESTAMP NULL,
    password VARCHAR(255) NOT NULL,
    phone VARCHAR(20),
    cpf_cnpj VARCHAR(20) UNIQUE,
    type ENUM('individual', 'company') DEFAULT 'individual',
    birth_date DATE NULL,
    avatar VARCHAR(255),
    status ENUM('active', 'inactive', 'suspended', 'pending') DEFAULT 'pending',
    last_login_at TIMESTAMP NULL,
    remember_token VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_email (email),
    INDEX idx_status (status)
);

-- Endereços (polimórfico)
CREATE TABLE addresses (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    addressable_type VARCHAR(255) NOT NULL,
    addressable_id BIGINT NOT NULL,
    type ENUM('billing', 'shipping', 'pickup', 'main') DEFAULT 'main',
    street VARCHAR(255) NOT NULL,
    number VARCHAR(20),
    complement VARCHAR(100),
    neighborhood VARCHAR(100),
    city VARCHAR(100) NOT NULL,
    state VARCHAR(2) NOT NULL,
    zip_code VARCHAR(10) NOT NULL,
    country VARCHAR(2) DEFAULT 'BR',
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    is_default BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_addressable (addressable_type, addressable_id),
    INDEX idx_location (city, state)
);

-- Veículos
CREATE TABLE vehicles (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    title VARCHAR(255) NOT NULL,
    category_id INT NOT NULL,
    brand_id INT NOT NULL,
    model_id INT NOT NULL,
    version VARCHAR(100),
    year_manufacture INT NOT NULL,
    year_model INT NOT NULL,
    color VARCHAR(50),
    plate VARCHAR(20) UNIQUE,
    renavam VARCHAR(50),
    chassis VARCHAR(50),
    mileage INT DEFAULT 0,
    fuel_type ENUM('gasoline', 'ethanol', 'flex', 'diesel', 'electric', 'hybrid'),
    transmission ENUM('manual', 'automatic', 'semi-automatic', 'cvt'),
    doors INT,
    seats INT,
    engine VARCHAR(50),
    potency VARCHAR(50),

    -- Campos de negócio
    business_type ENUM('sale', 'rent', 'both') NOT NULL,
    sale_price DECIMAL(12, 2),
    daily_rate DECIMAL(10, 2),
    weekly_rate DECIMAL(10, 2),
    monthly_rate DECIMAL(10, 2),
    minimum_rental_days INT DEFAULT 1,
    security_deposit DECIMAL(10, 2),

    -- Status e controle
    status ENUM('draft', 'pending', 'active', 'sold', 'rented', 'inactive') DEFAULT 'draft',
    moderation_status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
    moderation_notes TEXT,

    -- Detalhes
    description TEXT,
    features JSON,
    equipment JSON,
    maintenance_history JSON,

    -- Flags e contadores
    is_featured BOOLEAN DEFAULT FALSE,
    is_negotiable BOOLEAN DEFAULT FALSE,
    accepts_exchange BOOLEAN DEFAULT FALSE,
    accepts_financing BOOLEAN DEFAULT FALSE,
    views_count INT DEFAULT 0,
    favorites_count INT DEFAULT 0,

    -- Datas
    featured_until TIMESTAMP NULL,
    published_at TIMESTAMP NULL,
    sold_at TIMESTAMP NULL,
    expires_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (category_id) REFERENCES vehicle_categories(id),
    FOREIGN KEY (brand_id) REFERENCES brands(id),
    FOREIGN KEY (model_id) REFERENCES models(id),
    INDEX idx_slug (slug),
    INDEX idx_status (status, moderation_status),
    INDEX idx_business (business_type, status),
    INDEX idx_search (brand_id, model_id, year_model, fuel_type),
    FULLTEXT idx_fulltext (title, description)
);

-- Peças
CREATE TABLE parts (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    title VARCHAR(255) NOT NULL,
    category_id INT NOT NULL,
    brand_id INT,
    sku VARCHAR(100) UNIQUE,
    oem_number VARCHAR(100),

    -- Detalhes do produto
    description TEXT,
    condition ENUM('new', 'used', 'reconditioned') NOT NULL,
    warranty_months INT DEFAULT 0,
    compatibility JSON,
    specifications JSON,

    -- Preço e estoque
    price DECIMAL(10, 2) NOT NULL,
    promotional_price DECIMAL(10, 2),
    cost DECIMAL(10, 2),
    stock_quantity INT DEFAULT 0,
    min_stock INT DEFAULT 0,
    max_stock INT DEFAULT 999,
    reserved_quantity INT DEFAULT 0,

    -- Status
    status ENUM('draft', 'active', 'out_of_stock', 'discontinued', 'inactive') DEFAULT 'draft',
    moderation_status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',

    -- Flags e contadores
    is_featured BOOLEAN DEFAULT FALSE,
    is_negotiable BOOLEAN DEFAULT FALSE,
    free_shipping BOOLEAN DEFAULT FALSE,
    views_count INT DEFAULT 0,
    sales_count INT DEFAULT 0,

    -- Datas
    featured_until TIMESTAMP NULL,
    published_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (category_id) REFERENCES part_categories(id),
    FOREIGN KEY (brand_id) REFERENCES brands(id),
    INDEX idx_slug (slug),
    INDEX idx_sku (sku),
    INDEX idx_status (status),
    INDEX idx_stock (stock_quantity),
    FULLTEXT idx_fulltext (title, description, oem_number)
);

-- Sistema de mídia (usando Spatie Media Library)
CREATE TABLE media (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    model_type VARCHAR(255) NOT NULL,
    model_id BIGINT NOT NULL,
    uuid VARCHAR(36) UNIQUE,
    collection_name VARCHAR(255) NOT NULL,
    name VARCHAR(255) NOT NULL,
    file_name VARCHAR(255) NOT NULL,
    mime_type VARCHAR(255),
    disk VARCHAR(255) DEFAULT 'public',
    conversions_disk VARCHAR(255) DEFAULT 'public',
    size BIGINT NOT NULL,
    manipulations JSON,
    custom_properties JSON,
    generated_conversions JSON,
    responsive_images JSON,
    order_column INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_model (model_type, model_id),
    INDEX idx_order (order_column)
);
```

## Funcionalidades Detalhadas com Inertia

### 1. Sistema de Autenticação (Laravel Breeze + Inertia)

```php
// app/Http/Controllers/Auth/AuthenticatedSessionController.php
class AuthenticatedSessionController extends Controller
{
    public function create(): Response
    {
        return Inertia::render('Auth/Login', [
            'canResetPassword' => Route::has('password.request'),
            'status' => session('status'),
        ]);
    }

    public function store(LoginRequest $request): RedirectResponse
    {
        $request->authenticate();
        $request->session()->regenerate();

        return redirect()->intended(RouteServiceProvider::HOME);
    }
}
```

### 2. Páginas Públicas com Inertia

```php
// app/Http/Controllers/Public/CatalogController.php
class CatalogController extends Controller
{
    public function index(Request $request): Response
    {
        $vehicles = Vehicle::query()
            ->with(['brand', 'model', 'media'])
            ->when($request->search, function ($query, $search) {
                $query->where('title', 'like', "%{$search}%");
            })
            ->when($request->brand_id, function ($query, $brandId) {
                $query->where('brand_id', $brandId);
            })
            ->when($request->price_range, function ($query, $range) {
                [$min, $max] = explode('-', $range);
                $query->whereBetween('sale_price', [$min, $max]);
            })
            ->active()
            ->paginate(12)
            ->withQueryString();

        return Inertia::render('Public/Catalog/Index', [
            'vehicles' => $vehicles,
            'filters' => [
                'search' => $request->search,
                'brand_id' => $request->brand_id,
                'price_range' => $request->price_range,
            ],
            'brands' => Brand::all(),
            'categories' => VehicleCategory::all(),
        ]);
    }
}
```

### 3. Sistema de Chat em Tempo Real (Laravel Echo + Pusher)

```tsx
// resources/js/Components/Features/ChatWidget.tsx
import { useEffect, useState } from 'react';
import Echo from 'laravel-echo';
import { useForm } from '@inertiajs/react';

export default function ChatWidget({ conversation, currentUser }) {
    const [messages, setMessages] = useState(conversation.messages || []);
    const { data, setData, post, reset } = useForm({
        content: '',
    });

    useEffect(() => {
        // Conectar ao canal privado
        window.Echo.private(`conversation.${conversation.id}`)
            .listen('MessageSent', (e) => {
                setMessages(prev => [...prev, e.message]);
            })
            .listenForWhisper('typing', (e) => {
                // Mostrar indicador de digitação
            });

        return () => {
            window.Echo.leave(`conversation.${conversation.id}`);
        };
    }, [conversation.id]);

    const sendMessage = (e) => {
        e.preventDefault();
        post(route('messages.store', conversation.id), {
            onSuccess: () => reset(),
        });
    };

    return (
        // Interface do chat
    );
}
```

### 4. Dashboard Administrativo

```php
// app/Http/Controllers/Admin/DashboardController.php
class DashboardController extends Controller
{
    public function __invoke(): Response
    {
        return Inertia::render('Admin/Dashboard', [
            'stats' => [
                'total_vehicles' => Vehicle::count(),
                'active_rentals' => Rental::active()->count(),
                'monthly_revenue' => Sale::thisMonth()->sum('total_amount'),
                'pending_approvals' => Vehicle::pending()->count(),
            ],
            'recent_activities' => Activity::latest()->take(10)->get(),
            'chart_data' => $this->getChartData(),
        ]);
    }
}
```

### 5. Formulários com Validação Server-Side

```php
// app/Http/Requests/VehicleRequest.php
class VehicleRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'title' => ['required', 'string', 'max:255'],
            'category_id' => ['required', 'exists:vehicle_categories,id'],
            'brand_id' => ['required', 'exists:brands,id'],
            'model_id' => ['required', 'exists:models,id'],
            'year_manufacture' => ['required', 'integer', 'min:1900', 'max:' . (date('Y') + 1)],
            'year_model' => ['required', 'integer', 'min:1900', 'max:' . (date('Y') + 1)],
            'mileage' => ['required', 'integer', 'min:0'],
            'fuel_type' => ['required', Rule::in(['gasoline', 'ethanol', 'flex', 'diesel', 'electric', 'hybrid'])],
            'transmission' => ['required', Rule::in(['manual', 'automatic', 'semi-automatic', 'cvt'])],
            'business_type' => ['required', Rule::in(['sale', 'rent', 'both'])],
            'sale_price' => ['required_if:business_type,sale,both', 'numeric', 'min:0'],
            'daily_rate' => ['required_if:business_type,rent,both', 'numeric', 'min:0'],
            'description' => ['required', 'string', 'min:50'],
            'images' => ['required', 'array', 'min:3', 'max:20'],
            'images.*' => ['image', 'max:5120'], // 5MB
        ];
    }
}
```

## Compartilhamento de Dados Global (Inertia)

```php
// app/Http/Middleware/HandleInertiaRequests.php
class HandleInertiaRequests extends Middleware
{
    public function share(Request $request): array
    {
        return array_merge(parent::share($request), [
            'auth' => [
                'user' => $request->user() ? [
                    'id' => $request->user()->id,
                    'name' => $request->user()->name,
                    'email' => $request->user()->email,
                    'avatar' => $request->user()->avatar_url,
                    'roles' => $request->user()->roles->pluck('name'),
                    'permissions' => $request->user()->getAllPermissions()->pluck('name'),
                    'unread_messages' => $request->user()->unreadMessages()->count(),
                ] : null,
            ],
            'flash' => [
                'success' => fn () => $request->session()->get('success'),
                'error' => fn () => $request->session()->get('error'),
                'warning' => fn () => $request->session()->get('warning'),
                'info' => fn () => $request->session()->get('info'),
            ],
            'app' => [
                'name' => config('app.name'),
                'locale' => app()->getLocale(),
                'supported_locales' => config('app.supported_locales'),
            ],
            'ziggy' => function () use ($request) {
                return array_merge((new Ziggy)->toArray(), [
                    'location' => $request->url(),
                ]);
            },
        ]);
    }
}
```

## Otimizações e Performance

### 1. Server-Side Rendering (SSR)

```javascript
// ssr.js
import { createInertiaApp } from "@inertiajs/react";
import createServer from "@inertiajs/react/server";
import { renderToString } from "react-dom/server";

createServer((page) =>
  createInertiaApp({
    page,
    render: renderToString,
    resolve: (name) => {
      const pages = import.meta.glob("./Pages/**/*.tsx", { eager: true });
      return pages[`./Pages/${name}.tsx`];
    },
    setup: ({ App, props }) => <App {...props} />,
  })
);
```

### 2. Lazy Loading de Componentes

```tsx
// resources/js/Pages/Public/Home.tsx
import { lazy, Suspense } from "react";

const VehicleCarousel = lazy(
  () => import("@/Components/Features/VehicleCarousel")
);
const TestimonialsSection = lazy(
  () => import("@/Components/Sections/Testimonials")
);

export default function Home({ featuredVehicles, testimonials }) {
  return (
    <>
      <Suspense fallback={<div>Carregando...</div>}>
        <VehicleCarousel vehicles={featuredVehicles} />
      </Suspense>

      <Suspense fallback={<div>Carregando...</div>}>
        <TestimonialsSection testimonials={testimonials} />
      </Suspense>
    </>
  );
}
```

### 3. Cache de Queries

```php
// app/Services/VehicleService.php
class VehicleService
{
    public function getFeaturedVehicles()
    {
        return Cache::remember('featured_vehicles', 3600, function () {
            return Vehicle::with(['brand', 'model', 'media'])
                ->featured()
                ->active()
                ->take(8)
                ->get();
        });
    }
}
```

### 4. Otimização de Imagens

```php
// app/Models/Vehicle.php
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

class Vehicle extends Model implements HasMedia
{
    use InteractsWithMedia;

    public function registerMediaConversions(Media $media = null): void
    {
        $this->addMediaConversion('thumb')
            ->width(300)
            ->height(200)
            ->sharpen(10)
            ->optimize()
            ->nonQueued();

        $this->addMediaConversion('preview')
            ->width(800)
            ->height(600)
            ->quality(85)
            ->optimize();

        $this->addMediaConversion('large')
            ->width(1920)
            ->height(1080)
            ->quality(90)
            ->optimize();
    }
}
```

## Segurança Aprimorada

### 1. Políticas de Autorização

```php
// app/Policies/VehiclePolicy.php
class VehiclePolicy
{
    public function viewAny(User $user): bool
    {
        return true;
    }

    public function view(User $user, Vehicle $vehicle): bool
    {
        return $vehicle->isPublished() || $user->id === $vehicle->user_id;
    }

    public function create(User $user): bool
    {
        return $user->hasVerifiedEmail() && !$user->isSuspended();
    }

    public function update(User $user, Vehicle $vehicle): bool
    {
        return $user->id === $vehicle->user_id && !$vehicle->isSold();
    }

    public function delete(User $user, Vehicle $vehicle): bool
    {
        return $user->id === $vehicle->user_id && !$vehicle->hasActiveRentals();
    }
}
```

### 2. Rate Limiting

```php
// app/Providers/RouteServiceProvider.php
protected function configureRateLimiting(): void
{
    RateLimiter::for('api', function (Request $request) {
        return Limit::perMinute(60)->by($request->user()?->id ?: $request->ip());
    });

    RateLimiter::for('messages', function (Request $request) {
        return Limit::perMinute(10)->by($request->user()->id);
    });

    RateLimiter::for('uploads', function (Request $request) {
        return Limit::perHour(100)->by($request->user()->id);
    });
}
```

## Testes Automatizados

### 1. Testes de Feature

```php
// tests/Feature/VehicleManagementTest.php
class VehicleManagementTest extends TestCase
{
    use RefreshDatabase;

    public function test_user_can_create_vehicle_announcement()
    {
        $user = User::factory()->verified()->create();

        $response = $this->actingAs($user)
            ->post('/vehicles', [
                'title' => 'Honda Civic 2022',
                'category_id' => 1,
                'brand_id' => 1,
                'model_id' => 1,
                // ... outros campos
            ]);

        $response->assertRedirect('/my-vehicles');
        $this->assertDatabaseHas('vehicles', [
            'title' => 'Honda Civic 2022',
            'user_id' => $user->id,
        ]);
    }
}
```

### 2. Testes de Componente React

```tsx
// tests/React/VehicleCard.test.tsx
import { render, screen } from "@testing-library/react";
import VehicleCard from "@/Components/Features/VehicleCard";

describe("VehicleCard", () => {
  it("renders vehicle information correctly", () => {
    const vehicle = {
      id: 1,
      title: "Honda Civic 2022",
      sale_price: 120000,
      mileage: 15000,
      fuel_type: "flex",
    };

    render(<VehicleCard vehicle={vehicle} />);

    expect(screen.getByText("Honda Civic 2022")).toBeInTheDocument();
    expect(screen.getByText("R$ 120.000,00")).toBeInTheDocument();
    expect(screen.getByText("15.000 km")).toBeInTheDocument();
  });
});
```

## Deploy e DevOps

### 1. Docker Configuration

```dockerfile
# Dockerfile
FROM php:8.2-fpm

# Instalar dependências
RUN apt-get update && apt-get install -y \
    git \
    curl \
    libpng-dev \
    libonig-dev \
    libxml2-dev \
    zip \
    unzip \
    nodejs \
    npm

# Instalar extensões PHP
RUN docker-php-ext-install pdo_mysql mbstring exif pcntl bcmath gd

# Instalar Composer
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

WORKDIR /var/www

COPY . .

RUN composer install --optimize-autoloader --no-dev
RUN npm install && npm run build

CMD ["php-fpm"]
```

### 2. CI/CD Pipeline (GitHub Actions)

```yaml
# .github/workflows/deploy.yml
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: "8.2"
      - name: Run Tests
        run: |
          composer install
          php artisan test

  deploy:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - name: Deploy to server
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.HOST }}
          username: ${{ secrets.USERNAME }}
          key: ${{ secrets.KEY }}
          script: |
            cd /var/www/projeto
            git pull origin main
            composer install --optimize-autoloader --no-dev
            npm install && npm run build
            php artisan migrate --force
            php artisan config:cache
            php artisan route:cache
            php artisan view:cache
            php artisan queue:restart
```

## Monitoramento e Observabilidade

### 1. Laravel Telescope (Desenvolvimento)

```php
// config/telescope.php
'enabled' => env('TELESCOPE_ENABLED', false),

'watchers' => [
    Watchers\CacheWatcher::class => true,
    Watchers\CommandWatcher::class => true,
    Watchers\DumpWatcher::class => true,
    Watchers\EventWatcher::class => true,
    Watchers\ExceptionWatcher::class => true,
    Watchers\JobWatcher::class => true,
    Watchers\LogWatcher::class => true,
    Watchers\MailWatcher::class => true,
    Watchers\ModelWatcher::class => true,
    Watchers\NotificationWatcher::class => true,
    Watchers\QueryWatcher::class => [
        'enabled' => true,
        'slow' => 100,
    ],
    Watchers\RedisWatcher::class => true,
    Watchers\RequestWatcher::class => [
        'enabled' => true,
        'size_limit' => 64,
    ],
    Watchers\GateWatcher::class => true,
    Watchers\ScheduleWatcher::class => true,
    Watchers\ViewWatcher::class => true,
],
```

### 2. Logging e Métricas

```php
// app/Services/MetricsService.php
class MetricsService
{
    public function trackVehicleView(Vehicle $vehicle, ?User $user = null)
    {
        // Incrementar contador de visualizações
        $vehicle->increment('views_count');

        // Registrar no log de analytics
        activity()
            ->performedOn($vehicle)
            ->causedBy($user)
            ->withProperties([
                'ip' => request()->ip(),
                'user_agent' => request()->userAgent(),
                'referer' => request()->header('referer'),
            ])
            ->log('viewed');
    }

    public function getConversionRate(string $period = 'month'): array
    {
        return Cache::remember("conversion_rate_{$period}", 3600, function () use ($period) {
            $views = VehicleView::whereBetween('created_at', $this->getPeriodDates($period))->count();
            $conversions = Sale::whereBetween('created_at', $this->getPeriodDates($period))->count();

            return [
                'views' => $views,
                'conversions' => $conversions,
                'rate' => $views > 0 ? round(($conversions / $views) * 100, 2) : 0,
            ];
        });
    }
}
```

## Roadmap de Implementação

### Fase 1: Foundation (Semanas 1-2)

- [x] Configurar ambiente Docker
- [x] Instalar Laravel 11 com Inertia.js
- [x] Configurar React com TypeScript
- [x] Implementar autenticação (Laravel Breeze)
- [x] Criar migrations e models básicos
- [x] Implementar relacionamentos entre modelos
- [x] Adicionar traits e escopos reutilizáveis
- [x] Criar modelos auxiliares (Message, Review)
- [x] Implementar lógica de negócios nos modelos
- [x] Configurar Tailwind CSS
- [x] Implementar layouts base (público/admin/user)

### Fase 2: Core Features (Semanas 3-6)

- [x] **Semana 3: Gestão de Veículos, Motos e Peças**
  - [x] CRUD completo de veículos
  - [x] Upload de múltiplas imagens com Spatie Media Library
  - [x] Validações e políticas de autorização
- [x] **Semana 4: Catálogo Público**
  - [x] Listagem com filtros avançados
  - [x] Página de detalhes do veículo
  - [x] Sistema de busca com Laravel Scout
- [x] **Semana 5: Sistema de Anúncios**
  - [x] Fluxo de publicação de anúncio
  - [x] Moderação de conteúdo
  - [x] Gestão de status
- [x] **Semana 6: Gestão de Peças**
  - [x] CRUD de peças
    - [x] Listagem com busca e filtros
    - [x] Criação/edição com validação
    - [x] Upload de imagens
    - [x] Visualização detalhada
  - [x] Controle de estoque
    - [x] Quantidade em estoque
    - [x] Estoque mínimo
    - [x] Status baseado no estoque
  - [ ] Compatibilidade de veículos (próxima etapa)
  - [x] Gerenciamento de preços
  - [x] Categorização e marca

### Fase 3: Advanced Features (Semanas 7-10)

- [ ] **Semana 7: Sistema de Chat**
  - Implementar Laravel Echo + Pusher
  - Interface de chat em tempo real
  - Notificações push
- [ ] **Semana 8: Dashboard e Analytics**
  - Dashboard administrativo
  - Relatórios e gráficos
  - Métricas de performance
- [ ] **Semana 9: Sistema de Pagamentos**
  - Integração com gateway (Stripe/MercadoPago)
  - Controle de transações
  - Relatórios financeiros
- [ ] **Semana 10: Reservas e Aluguéis**
  - Sistema de calendário
  - Gestão de disponibilidade
  - Contratos digitais

### Fase 4: Polish & Optimization (Semanas 11-12)

- [ ] **Semana 11: Otimizações**
  - Implementar SSR completo
  - Cache strategies
  - Lazy loading
  - Image optimization
- [ ] **Semana 12: Testes e Deploy**
  - Testes automatizados (PHPUnit + Jest)
  - CI/CD pipeline
  - Deploy em produção
  - Monitoramento e logs

## Checklist de Segurança

### Autenticação e Autorização

- [x] Usar Laravel Sanctum para autenticação SPA
- [x] Implementar políticas (Policies) para todos os models
- [x] Verificação de e-mail obrigatória
- [x] Rate limiting em rotas sensíveis
- [x] Sessões seguras com HTTPS only
- [x] CSRF protection via Inertia

### Proteção de Dados

- [x] Validação server-side em todos os formulários
- [x] Sanitização de inputs
- [x] Prepared statements (Eloquent ORM)
- [x] Criptografia de dados sensíveis
- [x] Backup automático diário
- [x] GDPR compliance

### Infraestrutura

- [x] HTTPS obrigatório
- [x] Headers de segurança (CSP, HSTS, etc.)
- [x] Firewall de aplicação web (WAF)
- [x] Monitoramento de segurança
- [x] Logs de auditoria
- [x] Proteção contra DDoS

## Métricas de Sucesso

### KPIs Técnicos

- **Performance**: Tempo de carregamento < 3s
- **Disponibilidade**: Uptime > 99.9%
- **SEO**: Core Web Vitals score > 90
- **Mobile**: Responsive design 100%
- **Acessibilidade**: WCAG 2.1 Level AA

### KPIs de Negócio

- **Conversão**: Taxa de conversão > 2%
- **Engajamento**: Tempo médio na página > 3 min
- **Retenção**: Taxa de retorno > 30%
- **Satisfação**: NPS > 50
- **Growth**: 20% crescimento mensal

## Manutenção e Suporte

### Rotinas de Manutenção

```php
// app/Console/Kernel.php
protected function schedule(Schedule $schedule): void
{
    // Backup diário
    $schedule->command('backup:run')->daily()->at('02:00');

    // Limpeza de logs antigos
    $schedule->command('log:clear')->weekly();

    // Atualização de estatísticas
    $schedule->command('stats:calculate')->hourly();

    // Verificação de anúncios expirados
    $schedule->command('vehicles:check-expired')->daily();

    // Envio de relatórios
    $schedule->command('reports:send')->monthlyOn(1, '09:00');

    // Limpeza de arquivos temporários
    $schedule->command('media:clean')->daily();

    // Indexação para busca
    $schedule->command('scout:import')->daily();
}
```

### Documentação

#### Para Desenvolvedores

````markdown
## Setup do Ambiente de Desenvolvimento

1. Clone o repositório

```bash
git clone https://github.com/seu-usuario/projeto-loja.git
cd projeto-loja
```
````

2. Configure o ambiente

```bash
cp .env.example .env
composer install
npm install
php artisan key:generate
```

3. Configure o banco de dados

```bash
php artisan migrate --seed
```

4. Inicie os serviços

```bash
php artisan serve
npm run dev
php artisan queue:work
php artisan websockets:serve # se usar Laravel WebSockets
```

```

#### Para Usuários
- Manual do usuário vendedor
- Manual do administrador
- FAQs e troubleshooting
- Vídeos tutoriais
- Central de ajuda integrada

## Custos Estimados

### Desenvolvimento (3 meses)
- **Equipe**:
  - 1 Dev Full Stack Senior: R$ 15.000/mês
  - 1 Dev Frontend: R$ 8.000/mês
  - 1 Designer UI/UX: R$ 7.000/mês
  - **Total**: R$ 90.000

### Infraestrutura (mensal)
- **Hospedagem**: Digital Ocean/AWS - R$ 500-1.500
- **CDN**: Cloudflare Pro - R$ 100
- **E-mail**: SendGrid/Amazon SES - R$ 200
- **Storage**: S3/Spaces - R$ 300
- **Pusher/Soketi**: WebSockets - R$ 200
- **Monitoring**: Sentry/New Relic - R$ 300
- **Total**: R$ 1.600-2.600/mês

### Serviços e Licenças
- **SSL Certificate**: Let's Encrypt (Gratuito)
- **Payment Gateway**: 2-4% por transação
- **Laravel Forge**: R$ 60/mês (opcional)
- **GitHub**: R$ 20/mês
- **Backup Service**: R$ 100/mês

## Stack Tecnológica Final

### Core
- **Backend**: Laravel 11 + Inertia.js
- **Frontend**: React 18 + TypeScript
- **Styling**: Tailwind CSS 3.4
- **Database**: MySQL 8.0
- **Cache/Queue**: Redis
- **Search**: Meilisearch
- **Real-time**: Pusher/Soketi

### Ferramentas de Desenvolvimento
- **Version Control**: Git + GitHub
- **CI/CD**: GitHub Actions
- **Container**: Docker
- **Code Quality**: PHPStan + ESLint
- **Testing**: PHPUnit + Jest
- **API Testing**: Postman/Insomnia

### Monitoramento
- **APM**: New Relic/Datadog
- **Errors**: Sentry
- **Logs**: Laravel Telescope
- **Analytics**: Google Analytics 4
- **Uptime**: UptimeRobot

## Considerações Finais

Este plano foi otimizado para usar Inertia.js, eliminando a necessidade de uma API REST complexa e mantendo todos os benefícios de uma SPA moderna com React. As principais vantagens desta abordagem:

1. **Simplicidade**: Roteamento e controllers Laravel tradicionais
2. **Performance**: Menos requisições HTTP, dados otimizados
3. **SEO**: SSR nativo com melhor indexação
4. **Segurança**: Autenticação simplificada, CSRF automático
5. **Desenvolvimento**: Menor complexidade, desenvolvimento mais rápido
6. **Manutenção**: Código mais limpo e organizado

O projeto está estruturado para ser escalável, seguro e de fácil manutenção, com foco na experiência do usuário e nas melhores práticas de desenvolvimento moderno.

---
*Documento atualizado em: 18/09/2025*
*Versão: 2.0 - Otimizado para Inertia.js*
```
