import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import MainLayout from '@/layouts/MainLayout';
import { Head } from '@inertiajs/react';

export default function Contact() {
    return (
        <MainLayout>
            <Head title="Contato" />
            
            <div className="container mx-auto px-4 py-8">
                <div className="max-w-4xl mx-auto">
                    <h1 className="text-3xl font-bold mb-8">Entre em Contato</h1>
                    
                    <div className="grid gap-8 md:grid-cols-2">
                        <div>
                            <h2 className="text-xl font-semibold mb-6">Envie sua Mensagem</h2>
                            <form className="space-y-4">
                                <div>
                                    <Label htmlFor="name">Nome</Label>
                                    <Input id="name" placeholder="Seu nome completo" />
                                </div>
                                <div>
                                    <Label htmlFor="email">E-mail</Label>
                                    <Input id="email" type="email" placeholder="<EMAIL>" />
                                </div>
                                <div>
                                    <Label htmlFor="phone">Telefone</Label>
                                    <Input id="phone" placeholder="(11) 99999-9999" />
                                </div>
                                <div>
                                    <Label htmlFor="subject">Assunto</Label>
                                    <Input id="subject" placeholder="Como podemos ajudar?" />
                                </div>
                                <div>
                                    <Label htmlFor="message">Mensagem</Label>
                                    <Textarea 
                                        id="message" 
                                        placeholder="Descreva sua dúvida ou sugestão..."
                                        rows={5}
                                    />
                                </div>
                                <Button type="submit" className="w-full">
                                    Enviar Mensagem
                                </Button>
                            </form>
                        </div>
                        
                        <div>
                            <h2 className="text-xl font-semibold mb-6">Informações de Contato</h2>
                            <div className="space-y-6">
                                <div className="bg-white rounded-lg shadow-md p-6">
                                    <h3 className="font-semibold mb-3">📞 Telefone</h3>
                                    <p className="text-gray-600">(11) 1234-5678</p>
                                    <p className="text-sm text-gray-500">Seg-Sex: 8h às 18h</p>
                                </div>
                                
                                <div className="bg-white rounded-lg shadow-md p-6">
                                    <h3 className="font-semibold mb-3">📧 E-mail</h3>
                                    <p className="text-gray-600"><EMAIL></p>
                                    <p className="text-sm text-gray-500">Resposta em até 24h</p>
                                </div>
                                
                                <div className="bg-white rounded-lg shadow-md p-6">
                                    <h3 className="font-semibold mb-3">📍 Endereço</h3>
                                    <p className="text-gray-600">
                                        Av. Paulista, 1000<br />
                                        São Paulo - SP<br />
                                        CEP: 01310-100
                                    </p>
                                </div>
                                
                                <div className="bg-white rounded-lg shadow-md p-6">
                                    <h3 className="font-semibold mb-3">💬 Chat Online</h3>
                                    <p className="text-gray-600">Disponível no site</p>
                                    <p className="text-sm text-gray-500">Seg-Sex: 8h às 18h</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </MainLayout>
    );
}
