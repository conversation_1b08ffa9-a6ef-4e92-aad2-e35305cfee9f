<?php

namespace App\Http\Controllers\Public;

use App\Http\Controllers\Controller;
use App\Models\Category;
use App\Models\Part;
use App\Models\Advertisement;
use Inertia\Inertia;

class WelcomeController extends Controller
{
    /**
     * Exibe a página inicial.
     */
    public function index()
    {
        // Carrega as categorias ativas
        $categories = Category::where('is_active', true)
            ->whereNull('parent_id')
            ->with(['children' => function($query) {
                $query->where('is_active', true);
            }])
            ->orderBy('order')
            ->get(['id', 'name', 'slug', 'icon', 'description'])
            ->map(function ($category) {
                return [
                    'id' => $category->id,
                    'name' => $category->name,
                    'slug' => $category->slug,
                    'description' => $category->description ?? 'Categoria de ' . $category->name,
                    'icon' => $category->icon,
                    'url' => '/pesquisar?category_id=' . $category->id,
                    'vehicles_count' => rand(10, 100), // Placeholder - você pode implementar contagem real
                    'parts_count' => rand(5, 50), // Placeholder - você pode implementar contagem real
                ];
            });

        // Busca anúncios de carros em destaque
        $featuredCars = Advertisement::with(['vehicle.brand', 'vehicle.category'])
            ->where('status', 'published')
            ->where('is_featured', true)
            ->whereHas('vehicle', function($query) {
                $query->where('category_id', 1); // ID da categoria "Carros"
            })
            ->latest()
            ->take(6)
            ->get()
            ->map(function ($ad) {
                return [
                    'id' => $ad->id,
                    'model' => $ad->vehicle->model ?? '',
                    'title' => $ad->title,
                    'price' => (float) $ad->price,
                    'promotional_price' => null,
                    'year_manufacture' => $ad->vehicle->year_manufacture ?? null,
                    'mileage' => $ad->vehicle->mileage ?? null,
                    'color' => $ad->vehicle->color ?? null,
                    'fuel_type' => $ad->vehicle->fuel_type ?? null,
                    'transmission' => $ad->vehicle->transmission ?? null,
                    'is_featured' => $ad->is_featured,
                    'is_negotiable' => $ad->is_negotiable ?? false,
                    'main_image_url' => $ad->featured_image_url,
                    'url' => '/anuncios/' . $ad->id,
                    'brand' => [
                        'name' => $ad->vehicle->brand->name ?? 'Sem marca',
                    ],
                    'category' => [
                        'name' => $ad->vehicle->category->name ?? 'Sem categoria',
                        'slug' => $ad->vehicle->category->slug ?? '',
                    ],
                ];
            });

        // Busca anúncios de motos em destaque
        $featuredMotos = Advertisement::with(['vehicle.brand', 'vehicle.category'])
            ->where('status', 'published')
            ->where('is_featured', true)
            ->whereHas('vehicle', function($query) {
                $query->where('category_id', 2); // ID da categoria "Motos"
            })
            ->latest()
            ->take(6)
            ->get()
            ->map(function ($ad) {
                return [
                    'id' => $ad->id,
                    'model' => $ad->vehicle->model ?? '',
                    'title' => $ad->title,
                    'price' => (float) $ad->price,
                    'promotional_price' => null,
                    'year_manufacture' => $ad->vehicle->year_manufacture ?? null,
                    'mileage' => $ad->vehicle->mileage ?? null,
                    'color' => $ad->vehicle->color ?? null,
                    'fuel_type' => $ad->vehicle->fuel_type ?? null,
                    'transmission' => $ad->vehicle->transmission ?? null,
                    'is_featured' => $ad->is_featured,
                    'is_negotiable' => $ad->is_negotiable ?? false,
                    'main_image_url' => $ad->featured_image_url,
                    'url' => '/anuncios/' . $ad->id,
                    'brand' => [
                        'name' => $ad->vehicle->brand->name ?? 'Sem marca',
                    ],
                    'category' => [
                        'name' => $ad->vehicle->category->name ?? 'Sem categoria',
                        'slug' => $ad->vehicle->category->slug ?? '',
                    ],
                ];
            });

        // Busca as peças em destaque
        $featuredParts = Part::with(['brand', 'category'])
            ->where('is_featured', true)
            ->where('status', 'active')
            ->where('stock_quantity', '>', 0)
            ->latest()
            ->take(6)
            ->get()
            ->map(function ($part) {
                return [
                    'id' => $part->id,
                    'name' => $part->name,
                    'slug' => $part->slug,
                    'price' => (float) $part->price,
                    'promotional_price' => $part->promotional_price ? (float) $part->promotional_price : null,
                    'stock_quantity' => $part->stock_quantity,
                    'is_original' => $part->is_original,
                    'is_featured' => $part->is_featured,
                    'main_image_url' => null, // TODO: Implementar imagens para peças
                    'url' => '/pecas/' . $part->slug,
                    'brand' => [
                        'name' => $part->brand->name ?? 'Sem marca',
                    ],
                ];
            });

        return Inertia::render('welcome', [
            'categories' => $categories,
            'featuredCars' => $featuredCars,
            'featuredMotos' => $featuredMotos,
            'featuredParts' => $featuredParts,
        ]);
    }
}
