<?php

namespace App\Console\Commands;

use App\Models\Advertisement;
use App\Notifications\AdvertisementExpiringSoonNotification;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class NotifyExpiringAdvertisements extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'advertisements:notify-expiring
                            {--days=3 : Número de dias para notificar antecipadamente}'
    ;

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Notifica usuários sobre anúncios que estão prestes a expirar';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $days = (int) $this->option('days');
        $this->info("Buscando anúncios que expiram em até {$days} dias...");
        
        $expirationDate = now()->addDays($days);
        $count = 0;
        
        // Buscar anúncios ativos que expiram em até X dias
        $expiringAds = Advertisement::where('status', Advertisement::STATUS_PUBLISHED)
            ->where('expires_at', '<=', $expirationDate)
            ->where('expires_at', '>', now()) // Ainda não expirou
            ->whereDoesntHave('notifications', function($query) {
                $query->where('type', 'App\\Notifications\\AdvertisementExpiringSoonNotification');
            })
            ->with('user')
            ->get();
            
        foreach ($expiringAds as $ad) {
            try {
                // Verificar se o anúncio já foi notificado
                if ($ad->user) {
                    $ad->user->notify(new AdvertisementExpiringSoonNotification($ad, $days));
                    $count++;
                    
                    $this->line("Notificação enviada para o anúncio #{$ad->id} ({$ad->title}) - Expira em: {$ad->expires_at->format('d/m/Y')}");
                }
            } catch (\Exception $e) {
                Log::error("Erro ao notificar sobre anúncio prestes a expirar #{$ad->id}: " . $e->getMessage());
                $this->error("Erro ao notificar sobre anúncio #{$ad->id}: " . $e->getMessage());
            }
        }
        
        $this->info("Processo concluído. {$count} usuários notificados sobre anúncios prestes a expirar.");
        Log::info("Comando de notificação de anúncios prestes a expirar executado. {$count} notificações enviadas.");
        
        return Command::SUCCESS;
    }
}
