import { Link } from '@inertiajs/react';
import { FaHeart, FaRegHeart, FaStar, FaMapMarkerAlt } from 'react-icons/fa';
import { formatCurrency } from '@/lib/utils';

type AdCardProps = {
    id: number;
    title: string;
    price: number;
    location: string;
    image: string;
    isFavorite?: boolean;
    isFeatured?: boolean;
    isNew?: boolean;
    rating?: number;
    reviewCount?: number;
    year?: number;
    mileage?: number;
    transmission?: string;
    fuelType?: string;
    href: string;
    onFavoriteToggle?: (id: number) => void;
};

export default function AdCard({
    id,
    title,
    price,
    location,
    image,
    isFavorite = false,
    isFeatured = false,
    isNew = false,
    rating = 0,
    reviewCount = 0,
    year,
    mileage,
    transmission,
    fuelType,
    href,
    onFavoriteToggle,
}: AdCardProps) {
    const handleFavoriteClick = (e: React.MouseEvent) => {
        e.preventDefault();
        e.stopPropagation();
        onFavoriteToggle?.(id);
    };

    return (
        <Link
            href={href}
            className="group relative flex flex-col overflow-hidden rounded-lg bg-white shadow-sm transition-all duration-200 hover:shadow-md dark:bg-gray-800"
        >
            {/* Badges */}
            <div className="absolute left-3 top-3 z-10 flex space-x-2">
                {isNew && (
                    <span className="rounded-full bg-green-500 px-2 py-1 text-xs font-medium text-white">
                        Novo
                    </span>
                )}
                {isFeatured && (
                    <span className="rounded-full bg-orange-500 px-2 py-1 text-xs font-medium text-white">
                        Destaque
                    </span>
                )}
            </div>

            {/* Favorite Button */}
            <button
                onClick={handleFavoriteClick}
                className="absolute right-3 top-3 z-10 rounded-full bg-white/80 p-2 text-gray-700 transition-colors hover:bg-white hover:text-red-500"
                aria-label={isFavorite ? 'Remover dos favoritos' : 'Adicionar aos favoritos'}
            >
                {isFavorite ? (
                    <FaHeart className="h-5 w-5 text-red-500" />
                ) : (
                    <FaRegHeart className="h-5 w-5" />
                )}
            </button>

            {/* Image */}
            <div className="relative aspect-[4/3] w-full overflow-hidden">
                <img
                    src={image || '/images/placeholder.jpg'}
                    alt={title}
                    className="h-full w-full object-cover transition-transform duration-300 group-hover:scale-105"
                    onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.src = '/images/placeholder.jpg';
                    }}
                />
            </div>

            {/* Content */}
            <div className="flex flex-1 flex-col p-4">
                <div className="mb-2 flex items-start justify-between">
                    <h3 className="line-clamp-2 text-sm font-medium text-gray-900 dark:text-gray-100">
                        {title}
                    </h3>
                    <span className="ml-2 whitespace-nowrap text-sm font-bold text-orange-600">
                        {formatCurrency(price)}
                    </span>
                </div>

                {/* Rating */}
                {(rating > 0 || reviewCount > 0) && (
                    <div className="mb-2 flex items-center">
                        <div className="flex items-center">
                            {[1, 2, 3, 4, 5].map((star) => (
                                <FaStar
                                    key={star}
                                    className={`h-3.5 w-3.5 ${
                                        star <= Math.round(rating)
                                            ? 'text-yellow-400'
                                            : 'text-gray-300'
                                    }`}
                                />
                            ))}
                        </div>
                        <span className="ml-1 text-xs text-gray-500">
                            ({reviewCount})
                        </span>
                    </div>
                )}

                {/* Details */}
                <div className="mt-2 grid grid-cols-2 gap-1 text-xs text-gray-500 dark:text-gray-400">
                    {year && (
                        <div className="flex items-center">
                            <span className="font-medium text-gray-900 dark:text-gray-300">Ano:</span>
                            <span className="ml-1">{year}</span>
                        </div>
                    )}
                    {mileage && (
                        <div className="flex items-center">
                            <span className="font-medium text-gray-900 dark:text-gray-300">KM:</span>
                            <span className="ml-1">
                                {new Intl.NumberFormat('pt-BR').format(mileage)} km
                            </span>
                        </div>
                    )}
                    {transmission && (
                        <div className="flex items-center">
                            <span className="font-medium text-gray-900 dark:text-gray-300">Câmbio:</span>
                            <span className="ml-1 capitalize">{transmission}</span>
                        </div>
                    )}
                    {fuelType && (
                        <div className="flex items-center">
                            <span className="font-medium text-gray-900 dark:text-gray-300">Combustível:</span>
                            <span className="ml-1 capitalize">
                                {fuelType === 'gasoline' 
                                    ? 'Gasolina' 
                                    : fuelType === 'ethanol' 
                                        ? 'Álcool' 
                                        : fuelType === 'flex' 
                                            ? 'Flex' 
                                            : fuelType === 'diesel' 
                                                ? 'Diesel' 
                                                : fuelType === 'electric' 
                                                    ? 'Elétrico' 
                                                    : fuelType}
                            </span>
                        </div>
                    )}
                </div>

                {/* Location */}
                <div className="mt-3 flex items-center text-xs text-gray-500">
                    <FaMapMarkerAlt className="mr-1 h-3.5 w-3.5 text-gray-400" />
                    <span className="truncate">{location}</span>
                </div>
            </div>
        </Link>
    );
}
