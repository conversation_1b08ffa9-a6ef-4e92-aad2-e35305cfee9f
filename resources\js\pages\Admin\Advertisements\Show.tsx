import { <PERSON>, <PERSON> } from '@inertiajs/react';
import AdminLayout from '@/components/Layout/AdminLayout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { formatCurrency, formatDate } from '@/lib/utils';
import { ArrowLeft, Edit, X, Clock, AlertCircle, CheckCircle, Zap, Tag } from 'lucide-react';
import { PageProps } from '@/types';

type BadgeVariant = 'default' | 'secondary' | 'destructive' | 'outline';

const statusVariant: Record<string, BadgeVariant> = {
  draft: 'secondary',
  pending_review: 'secondary',
  approved: 'outline',
  rejected: 'destructive',
  published: 'default',
  expired: 'secondary',
  sold: 'default',
};

const statusIcons: Record<string, React.ReactNode> = {
  draft: <Clock className="h-4 w-4 mr-1" />,
  pending_review: <AlertCircle className="h-4 w-4 mr-1" />,
  approved: <CheckCircle className="h-4 w-4 mr-1" />,
  rejected: <X className="h-4 w-4 mr-1" />,
  published: <Zap className="h-4 w-4 mr-1" />,
  expired: <Clock className="h-4 w-4 mr-1" />,
  sold: <Tag className="h-4 w-4 mr-1" />,
};

interface Image {
    id: number;
    original_url: string;
    thumbnail_url: string;
    order: number;
}

interface Vehicle {
    id: number;
    brand: {
        id: number;
        name: string;
    };
    model: string;
    year_manufacture: number;
    mileage?: number;
    color?: string;
    fuel_type?: string;
    notes?: string;
}

interface User {
    id: number;
    name: string;
    email: string;
}

interface Advertisement {
    id: number;
    title: string;
    description: string;
    price: number;
    is_negotiable: boolean;
    status: string;
    status_label: string;
    featured_image_url: string;
    images: Image[];
    vehicle: Vehicle;
    user: User;
    contact_phone: string;
    contact_email: string;
    location: string;
    latitude?: number;
    longitude?: number;
    published_at: string;
    expires_at?: string;
    views: number;
    is_featured: boolean;
    rejection_reason?: string;
}

interface Props extends PageProps {
    advertisement: Advertisement;
}

export default function AdvertisementShow({ advertisement }: Props) {
  return (
    <AdminLayout>
      <Head title={`Anúncio: ${advertisement.title}`} />
      
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <Link href={route('admin.advertisements.index') as string} className="flex items-center text-sm text-muted-foreground hover:text-foreground">
              <ArrowLeft className="h-4 w-4 mr-1" /> Voltar para a lista
            </Link>
            <h1 className="text-2xl font-bold tracking-tight mt-2">{advertisement.title}</h1>
          </div>
          <div className="flex items-center gap-2">
            <Link href={route('admin.advertisements.edit', { advertisement: advertisement.id })}>
              <Button variant="outline" size="sm">
                <Edit className="h-4 w-4 mr-2" />
                Editar
              </Button>
            </Link>
          </div>
        </div>

        <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
          <div className="lg:col-span-2 space-y-6">
            {/* Featured Image */}
            <Card>
              <CardContent className="p-0">
                <div className="relative aspect-video bg-muted rounded-t-lg overflow-hidden">
                  <img
                    src={advertisement.featured_image_url || '/images/placeholder-vehicle.jpg'}
                    alt={advertisement.title}
                    className="h-full w-full object-cover"
                  />
                  <div className="absolute top-4 right-4">
                    <Badge 
                      variant={statusVariant[advertisement.status] as BadgeVariant} 
                      className="flex items-center"
                    >
                      {statusIcons[advertisement.status as keyof typeof statusIcons]}
                      {advertisement.status_label}
                    </Badge>
                  </div>
                </div>
                
                {/* Image Gallery */}
                {advertisement.images && advertisement.images.length > 0 && (
                  <div className="p-4 grid grid-cols-4 gap-2">
                    {advertisement.images.map((image: Image, index: number) => (
                      <div key={index} className="aspect-square rounded-md overflow-hidden border">
                        <img
                          src={image.original_url}
                          alt={`${advertisement.title} - Imagem ${index + 1}`}
                          className="h-full w-full object-cover"
                        />
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Vehicle Details */}
            <Card>
              <CardHeader>
                <CardTitle>Detalhes do Veículo</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground">Marca</h3>
                    <p>{advertisement.vehicle.brand.name}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground">Modelo</h3>
                    <p>{advertisement.vehicle.model}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground">Ano</h3>
                    <p>{advertisement.vehicle.year_manufacture}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground">Quilometragem</h3>
                    <p>{advertisement.vehicle.mileage?.toLocaleString('pt-BR') || 'Não informada'} km</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground">Cor</h3>
                    <p>{advertisement.vehicle.color || 'Não informada'}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground">Combustível</h3>
                    <p>{advertisement.vehicle.fuel_type || 'Não informado'}</p>
                  </div>
                </div>
                
                {advertisement.vehicle.notes && (
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground">Observações</h3>
                    <p className="whitespace-pre-line">{advertisement.vehicle.notes}</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Description */}
            <Card>
              <CardHeader>
                <CardTitle>Descrição</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="prose max-w-none">
                  {advertisement.description || 'Nenhuma descrição fornecida.'}
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="space-y-6">
            {/* Price Card */}
            <Card>
              <CardContent className="p-6">
                <div className="text-3xl font-bold text-center mb-2">
                  {formatCurrency(advertisement.price)}
                </div>
                {advertisement.is_negotiable && (
                  <div className="text-center text-sm text-muted-foreground">
                    Preço Negociável
                  </div>
                )}
                
                <div className="mt-6 space-y-4">
                  <Button className="w-full" size="lg">
                    Entrar em Contato
                  </Button>
                  
                  <Button variant="outline" className="w-full" size="lg">
                    Fazer Proposta
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Contact Info */}
            <Card>
              <CardHeader>
                <CardTitle>Informações de Contato</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Anunciante</h3>
                  <p>{advertisement.user.name}</p>
                </div>
                
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Telefone</h3>
                  <p>{advertisement.contact_phone}</p>
                </div>
                
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">E-mail</h3>
                  <p>{advertisement.contact_email}</p>
                </div>
                
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Localização</h3>
                  <p>{advertisement.location}</p>
                </div>
                
                {advertisement.latitude && advertisement.longitude && (
                  <div className="h-40 bg-muted rounded-md overflow-hidden">
                    {/* Map integration would go here */}
                    <div className="h-full w-full flex items-center justify-center text-muted-foreground">
                      Mapa: {advertisement.latitude}, {advertisement.longitude}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Ad Info */}
            <Card>
              <CardHeader>
                <CardTitle>Informações do Anúncio</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Código</h3>
                  <p>#{String(advertisement.id).padStart(6, '0')}</p>
                </div>
                
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Publicado em</h3>
                  <p>{formatDate(advertisement.published_at)}</p>
                </div>
                
                {advertisement.expires_at && (
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground">Expira em</h3>
                    <p>{formatDate(advertisement.expires_at)}</p>
                  </div>
                )}
                
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Visualizações</h3>
                  <p>{advertisement.views}</p>
                </div>
                
                {advertisement.is_featured && (
                  <div className="pt-2">
                    <Badge variant="outline" className="bg-yellow-50 text-yellow-800 border-yellow-200">
                      <Zap className="h-3 w-3 mr-1" />
                      Destaque
                    </Badge>
                  </div>
                )}
                
                {advertisement.status === 'rejected' && advertisement.rejection_reason && (
                  <div className="pt-2">
                    <h3 className="text-sm font-medium text-muted-foreground">Motivo da Rejeição</h3>
                    <p className="text-sm text-red-600">{advertisement.rejection_reason}</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
}
