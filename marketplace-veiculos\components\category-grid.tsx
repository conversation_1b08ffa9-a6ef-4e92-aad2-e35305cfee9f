import Link from "next/link"
import { Card, CardContent } from "@/components/ui/card"
import { Car, Bike, Wrench, Truck, Zap, Shield } from "lucide-react"

const categories = [
  {
    title: "Carros",
    description: "Encontre o carro ideal para você",
    icon: Car,
    href: "/carros",
    color: "text-primary",
  },
  {
    title: "Motos",
    description: "Motos e scooters de todas as marcas",
    icon: Bike,
    href: "/motos",
    color: "text-accent",
  },
  {
    title: "Peças",
    description: "Peças originais e compatíveis",
    icon: Wrench,
    href: "/pecas",
    color: "text-secondary",
  },
  {
    title: "Caminhões",
    description: "Veículos comerciais e utilitários",
    icon: Truck,
    href: "/caminhoes",
    color: "text-primary",
  },
  {
    title: "Elétricos",
    description: "Veículos elétricos e híbridos",
    icon: Zap,
    href: "/eletricos",
    color: "text-accent",
  },
  {
    title: "Seguros",
    description: "Proteja seu veículo",
    icon: Shield,
    href: "/seguros",
    color: "text-destructive",
  },
]

export function CategoryGrid() {
  return (
    <section className="py-16 lg:py-24">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl lg:text-4xl font-bold mb-4">Explore por categoria</h2>
          <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
            Encontre exatamente o que você precisa navegando pelas nossas categorias especializadas
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {categories.map((category) => {
            const Icon = category.icon
            return (
              <Link key={category.title} href={category.href}>
                <Card className="group hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
                  <CardContent className="p-6">
                    <div className="flex items-center space-x-4">
                      <div className="p-3 rounded-lg bg-muted group-hover:bg-accent/10 transition-colors">
                        <Icon className={`h-6 w-6 ${category.color}`} />
                      </div>
                      <div>
                        <h3 className="font-semibold text-lg group-hover:text-accent transition-colors">
                          {category.title}
                        </h3>
                        <p className="text-muted-foreground text-sm">{category.description}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </Link>
            )
          })}
        </div>
      </div>
    </section>
  )
}
