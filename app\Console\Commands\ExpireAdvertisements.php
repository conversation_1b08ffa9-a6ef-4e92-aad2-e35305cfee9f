<?php

namespace App\Console\Commands;

use App\Models\Advertisement;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class ExpireAdvertisements extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'advertisements:expire';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Marca anúncios expirados como expirados';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Iniciando processo de expiração de anúncios...');
        
        $count = 0;
        
        // Buscar anúncios ativos que já passaram da data de expiração
        $expiredAds = Advertisement::where('status', '!=', Advertisement::STATUS_EXPIRED)
            ->where('expires_at', '<=', now())
            ->get();
            
        foreach ($expiredAds as $ad) {
            try {
                $ad->status = Advertisement::STATUS_EXPIRED;
                $ad->save();
                $count++;
                
                // O observer irá disparar a notificação de expiração
                $this->line("Anúncio #{$ad->id} ({$ad->title}) expirado com sucesso.");
            } catch (\Exception $e) {
                Log::error("Erro ao expirar anúncio #{$ad->id}: " . $e->getMessage());
                $this->error("Erro ao expirar anúncio #{$ad->id}: " . $e->getMessage());
            }
        }
        
        $this->info("Processo concluído. {$count} anúncios foram marcados como expirados.");
        Log::info("Comando de expiração de anúncios executado. {$count} anúncios expirados.");
        
        return Command::SUCCESS;
    }
}
