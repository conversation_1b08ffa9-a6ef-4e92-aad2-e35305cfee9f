FROM ubuntu:22.04

# Evitar prompts do tzdata
ENV DEBIAN_FRONTEND=noninteractive

# Instalar dependências do sistema
RUN apt-get update && apt-get install -y \
    software-properties-common \
    curl \
    git \
    unzip \
    libpng-dev \
    libjpeg-dev \
    libfreetype6-dev \
    libzip-dev \
    libonig-dev \
    libxml2-dev \
    libpq-dev \
    libicu-dev \
    libmagickwand-dev \
    supervisor \
    cron \
    nano \
    && rm -rf /var/lib/apt/lists/*

# Instalar PHP 8.2 e extensões necessárias
RUN add-apt-repository ppa:ondrej/php -y \
    && apt-get update \
    && apt-get install -y \
    php8.2-fpm \
    php8.2-common \
    php8.2-mysql \
    php8.2-xml \
    php8.2-curl \
    php8.2-mbstring \
    php8.2-zip \
    php8.2-gd \
    php8.2-intl \
    php8.2-bcmath \
    php8.2-redis \
    php8.2-soap \
    php8.2-imagick \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Instalar Composer
COPY --from=composer:latest /usr/bin/composer /usr/local/bin/composer

# Configurar PHP-FPM
RUN mkdir -p /run/php
RUN touch /run/php/php8.2-fpm.sock

# Configurar usuário
ARG uid=1000
ARG user=laravel
RUN useradd -G www-data,root -u $uid -d /home/<USER>
RUN mkdir -p /home/<USER>/.composer && \
    chown -R $user:$user /home/<USER>

# Configurar diretório de trabalho
WORKDIR /var/www

# Copiar arquivos do projeto
COPY . /var/www

# Definir permissões
RUN chown -R $user:www-data /var/www/storage /var/www/bootstrap/cache
RUN chmod -R 775 /var/www/storage /var/www/bootstrap/cache

# Instalar Node.js 18
RUN curl -fsSL https://deb.nodesource.com/setup_18.x | bash - \
    && apt-get install -y nodejs \
    && npm install -g npm@latest

# Script de inicialização
COPY docker/start-container.sh /usr/local/bin/start-container
RUN chmod +x /usr/local/bin/start-container

# Expor a porta 9000 para o PHP-FPM e 8000 para o servidor de desenvolvimento
EXPOSE 9000 8000

# Comando padrão
CMD ["/usr/local/bin/start-container"]
