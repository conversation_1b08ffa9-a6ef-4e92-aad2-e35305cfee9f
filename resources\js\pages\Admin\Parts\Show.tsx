import { Head, <PERSON> } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import { ArrowLeft, Edit, Trash2 } from 'lucide-react';
import AdminLayout from '@/components/Layout/AdminLayout';
import { formatCurrency } from '@/lib/utils';

interface Brand {
  id: number;
  name: string;
}

interface Category {
  id: number;
  name: string;
}

interface Vehicle {
  id: number;
  full_name: string;
  model: string;
  year_manufacture: number;
  brand?: {
    name: string;
  };
}

interface Part {
  id: number;
  name: string;
  description?: string;
  part_number?: string;
  price: number;
  promotional_price?: number;
  stock_quantity: number;
  minimum_stock: number;
  brand_id: number;
  category_id: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  brand?: Brand;
  category?: Category;
  image_url?: string;
  compatible_vehicles?: Vehicle[];
}

interface ShowPartProps {
  part: Part;
  brands: Brand[];
  categories: Category[];
}

export default function ShowPart({ part, brands, categories }: ShowPartProps) {
    const brand = brands.find((b) => b.id === part.brand_id);
    const category = categories.find((c) => c.id === part.category_id);
    
    return (
        <AdminLayout>
            <Head title={`Visualizar Peça: ${part.name}`} />
            
            <div className="space-y-6">
                <div className="flex items-center justify-between">
                    <div>
                        <h2 className="text-2xl font-bold tracking-tight">
                            Visualizar Peça: {part.name}
                        </h2>
                        <p className="text-muted-foreground">
                            Detalhes completos da peça
                        </p>
                    </div>
                    <div className="flex space-x-2">
                        <Button variant="outline" asChild>
                            <Link href={route('admin.parts.index')}>
                                <ArrowLeft className="mr-2 h-4 w-4" />
                                Voltar para a lista
                            </Link>
                        </Button>
                        <Button asChild>
                            <Link href={route('admin.parts.edit', { part: part.id })}>
                                <Edit className="mr-2 h-4 w-4" />
                                Editar Peça
                            </Link>
                        </Button>
                    </div>
                </div>

                <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
                    {/* Left column - Main information */}
                    <div className="lg:col-span-2 space-y-6">
                        <div className="bg-white rounded-lg shadow overflow-hidden">
                            <div className="p-6">
                                <h3 className="text-lg font-medium">Informações da Peça</h3>
                                <div className="mt-4 space-y-4">
                                    <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                                        <div>
                                            <p className="text-sm font-medium text-muted-foreground">Nome</p>
                                            <p className="mt-1">{part.name}</p>
                                        </div>
                                        <div>
                                            <p className="text-sm font-medium text-muted-foreground">Número da Peça</p>
                                            <p className="mt-1">{part.part_number || 'Não informado'}</p>
                                        </div>
                                        <div>
                                            <p className="text-sm font-medium text-muted-foreground">Marca</p>
                                            <p className="mt-1">{brand?.name || 'Não informada'}</p>
                                        </div>
                                        <div>
                                            <p className="text-sm font-medium text-muted-foreground">Categoria</p>
                                            <p className="mt-1">{category?.name || 'Não informada'}</p>
                                        </div>
                                        <div>
                                            <p className="text-sm font-medium text-muted-foreground">Preço</p>
                                            <p className="mt-1">
                                                {part.promotional_price ? (
                                                    <>
                                                        <span className="line-through text-muted-foreground text-sm mr-2">
                                                            {formatCurrency(part.price)}
                                                        </span>
                                                        <span className="text-red-600 font-medium">
                                                            {formatCurrency(part.promotional_price)}
                                                        </span>
                                                    </>
                                                ) : (
                                                    formatCurrency(part.price)
                                                )}
                                            </p>
                                        </div>
                                        <div>
                                            <p className="text-sm font-medium text-muted-foreground">Estoque</p>
                                            <p className="mt-1">
                                                {part.stock_quantity} unidades
                                                {part.stock_quantity <= part.minimum_stock && (
                                                    <span className="ml-2 text-sm text-red-600">
                                                        (Abaixo do estoque mínimo: {part.minimum_stock})
                                                    </span>
                                                )}
                                            </p>
                                        </div>
                                        <div>
                                            <p className="text-sm font-medium text-muted-foreground">Status</p>
                                            <p className="mt-1">
                                                {part.is_active ? (
                                                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                        Ativo
                                                    </span>
                                                ) : (
                                                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                                        Inativo
                                                    </span>
                                                )}
                                            </p>
                                        </div>
                                    </div>
                                    
                                    {part.description && (
                                        <div>
                                            <p className="text-sm font-medium text-muted-foreground">Descrição</p>
                                            <p className="mt-1 whitespace-pre-line">{part.description}</p>
                                        </div>
                                    )}
                                </div>
                            </div>
                        </div>

                        {/* Compatible Vehicles Section */}
                        <div className="bg-white rounded-lg shadow overflow-hidden">
                            <div className="p-6">
                                <h3 className="text-lg font-medium mb-4">Veículos Compatíveis</h3>
                                {part.compatible_vehicles && part.compatible_vehicles.length > 0 ? (
                                    <div className="space-y-2">
                                        {part.compatible_vehicles.map((vehicle) => (
                                            <div key={vehicle.id} className="flex items-center justify-between p-3 bg-muted rounded-lg">
                                                <div>
                                                    <p className="font-medium">{vehicle.full_name}</p>
                                                    <p className="text-sm text-muted-foreground">
                                                        {vehicle.brand?.name} {vehicle.model} - {vehicle.year_manufacture}
                                                    </p>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                ) : (
                                    <div className="text-center py-8">
                                        <p className="text-muted-foreground">Nenhum veículo compatível cadastrado.</p>
                                        <Button variant="outline" className="mt-2" asChild>
                                            <Link href={route('admin.parts.edit', { part: part.id })}>
                                                Adicionar Compatibilidade
                                            </Link>
                                        </Button>
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>

                    {/* Right column - Image and actions */}
                    <div className="space-y-6">
                        <div className="bg-white rounded-lg shadow overflow-hidden">
                            <div className="p-6">
                                <h3 className="text-lg font-medium mb-4">Imagem da Peça</h3>
                                {part.image_url ? (
                                    <img 
                                        src={part.image_url} 
                                        alt={part.name} 
                                        className="w-full h-64 object-contain rounded-md"
                                    />
                                ) : (
                                    <div className="h-64 bg-gray-100 rounded-md flex items-center justify-center">
                                        <p className="text-muted-foreground">Sem imagem</p>
                                    </div>
                                )}
                            </div>
                        </div>

                        <div className="bg-white rounded-lg shadow overflow-hidden">
                            <div className="p-6">
                                <h3 className="text-lg font-medium mb-4">Ações</h3>
                                <div className="space-y-2">
                                    <Button variant="outline" className="w-full" asChild>
                                        <Link href={route('admin.parts.edit', { part: part.id })}>
                                            <Edit className="mr-2 h-4 w-4" />
                                            Editar Peça
                                        </Link>
                                    </Button>
                                    <Button variant="destructive" className="w-full">
                                        <Trash2 className="mr-2 h-4 w-4" />
                                        Excluir Peça
                                    </Button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </AdminLayout>
    );
}
