<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Vehicle;
use App\Models\Part;
use App\Models\Advertisement;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Carbon\Carbon;

class DashboardController extends Controller
{
    /**
     * Display the admin dashboard.
     */
    public function index()
    {
        // Estatísticas gerais
        $stats = [
            'total_users' => User::count(),
            'active_users' => User::where('status', 'active')->count(),
            'total_vehicles' => Vehicle::count(),
            'published_vehicles' => Vehicle::where('status', 'published')->count(),
            'total_parts' => Part::count(),
            'active_parts' => Part::where('status', 'active')->count(),
            'total_advertisements' => Advertisement::count(),
            'pending_advertisements' => Advertisement::where('status', 'pending')->count(),
            'total_views' => 0, // Placeholder - implementar sistema de views
            'total_messages' => 0, // Placeholder - implementar sistema de mensagens
        ];

        // Veículos recentes
        $recentVehicles = Vehicle::with(['brand', 'category', 'user'])
            ->latest()
            ->take(5)
            ->get()
            ->map(function ($vehicle) {
                return [
                    'id' => $vehicle->id,
                    'title' => $vehicle->title ?? $vehicle->brand->name . ' ' . $vehicle->model,
                    'type' => 'vehicle',
                    'status' => $vehicle->status,
                    'created_at' => $vehicle->created_at->toISOString(),
                    'user' => [
                        'name' => $vehicle->user->name,
                        'email' => $vehicle->user->email,
                    ],
                ];
            });

        // Usuários recentes
        $recentUsers = User::latest()
            ->take(5)
            ->get(['id', 'name', 'email', 'created_at', 'status'])
            ->map(function ($user) {
                return [
                    'id' => $user->id,
                    'title' => $user->name,
                    'type' => 'user',
                    'status' => $user->status,
                    'created_at' => $user->created_at->toISOString(),
                    'user' => [
                        'name' => $user->name,
                        'email' => $user->email,
                    ],
                ];
            });

        // Peças recentes
        $recentParts = Part::with(['category', 'user'])
            ->latest()
            ->take(5)
            ->get()
            ->map(function ($part) {
                return [
                    'id' => $part->id,
                    'title' => $part->name,
                    'type' => 'part',
                    'status' => $part->status,
                    'created_at' => $part->created_at->toISOString(),
                    'user' => [
                        'name' => $part->user->name,
                        'email' => $part->user->email,
                    ],
                ];
            });

        // Anúncios recentes
        $recentAdvertisements = Advertisement::with(['user'])
            ->latest()
            ->take(5)
            ->get()
            ->map(function ($ad) {
                return [
                    'id' => $ad->id,
                    'title' => $ad->title,
                    'type' => 'advertisement',
                    'status' => $ad->status,
                    'created_at' => $ad->created_at->toISOString(),
                    'user' => [
                        'name' => $ad->user->name,
                        'email' => $ad->user->email,
                    ],
                ];
            });

        // Dados para gráficos
        $chartData = $this->getChartData();

        return Inertia::render('Admin/Dashboard', [
            'stats' => $stats,
            'chartData' => $chartData,
            'recentUsers' => $recentUsers,
            'recentVehicles' => $recentVehicles,
            'recentParts' => $recentParts,
            'recentAdvertisements' => $recentAdvertisements,
        ]);
    }

    /**
     * Get chart data for dashboard.
     */
    private function getChartData()
    {
        // Dados dos últimos 7 dias
        $days = collect();
        for ($i = 6; $i >= 0; $i--) {
            $date = Carbon::now()->subDays($i);
            $days->push([
                'name' => $date->format('d/m'),
                'users' => User::whereDate('created_at', $date)->count(),
                'vehicles' => Vehicle::whereDate('created_at', $date)->count(),
                'parts' => Part::whereDate('created_at', $date)->count(),
                'advertisements' => Advertisement::whereDate('created_at', $date)->count(),
            ]);
        }

        return $days;
    }
}
