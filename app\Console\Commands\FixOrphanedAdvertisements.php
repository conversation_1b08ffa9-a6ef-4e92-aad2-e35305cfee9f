<?php

namespace App\Console\Commands;

use App\Models\Advertisement;
use App\Models\User;
use App\Models\Vehicle;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class FixOrphanedAdvertisements extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'advertisements:fix-orphaned
                            {--action= : Ação a ser executada (delete, assign, list)}
                            {--user-id= : ID do usuário para atribuir anúncios órfãos}
                            {--dry-run : Executar em modo de teste, sem fazer alterações reais}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Corrige anúncios órfãos (sem veículo ou usuário associado)';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $action = $this->option('action') ?? 'list';
        $dryRun = $this->option('dry-run');
        $userId = $this->option('user-id');
        
        if (!in_array($action, ['delete', 'assign', 'list'])) {
            $this->error('Ação inválida. Use: delete, assign ou list');
            return 1;
        }
        
        if ($action === 'assign' && !$userId) {
            $this->error('É necessário informar o ID do usuário com --user-id=ID para a ação "assign"');
            return 1;
        }
        
        if ($action === 'assign') {
            $user = User::find($userId);
            if (!$user) {
                $this->error("Usuário com ID {$userId} não encontrado.");
                return 1;
            }
        }
        
        // Encontrar anúncios sem veículo
        $adsWithoutVehicle = Advertisement::doesntHave('vehicle')->get();
        
        // Encontrar anúncios sem usuário
        $adsWithoutUser = Advertisement::doesntHave('user')->get();
        
        $totalOrphaned = $adsWithoutVehicle->count() + $adsWithoutUser->count();
        
        if ($totalOrphaned === 0) {
            $this->info('✅ Nenhum anúncio órfão encontrado.');
            return 0;
        }
        
        $this->warn("⚠️  Encontrados {$totalOrphaned} anúncios órfãos:");
        $this->line("- Sem veículo: {$adsWithoutVehicle->count()}");
        $this->line("- Sem usuário: {$adsWithoutUser->count()}");
        
        if ($action === 'list') {
            $this->newLine();
            $this->info('Para corrigir, execute um dos comandos:');
            $this->line('- Para excluir anúncios órfãos: php artisan advertisements:fix-orphaned --action=delete');
            $this->line('- Para atribuir a um usuário: php artisan advertisements:fix-orphaned --action=assign --user-id=ID_DO_USUARIO');
            $this->line('Adicione --dry-run para simular sem fazer alterações reais.');
            return 0;
        }
        
        $this->newLine();
        
        if ($dryRun) {
            $this->info('🚧 MODO DE TESTE - Nenhuma alteração será feita no banco de dados.');
        } else {
            $this->warn('⚠️  ATENÇÃO: Esta operação irá modificar o banco de dados.');
            if (!$this->confirm('Deseja continuar?', false)) {
                $this->info('Operação cancelada pelo usuário.');
                return 0;
            }
        }
        
        // Processar anúncios sem veículo
        if ($action === 'delete') {
            $this->processDeletion($adsWithoutVehicle, 'sem veículo', $dryRun);
        } elseif ($action === 'assign') {
            $this->processAssignment($adsWithoutVehicle, 'sem veículo', $user, $dryRun);
        }
        
        // Processar anúncios sem usuário
        if ($action === 'delete') {
            $this->processDeletion($adsWithoutUser, 'sem usuário', $dryRun);
        } elseif ($action === 'assign') {
            $this->processAssignment($adsWithoutUser, 'sem usuário', $user, $dryRun);
        }
        
        $this->newLine();
        $this->info('✅ Operação concluída com sucesso!');
        
        if ($dryRun) {
            $this->info('🔍 Lembre-se de executar novamente sem --dry-run para aplicar as alterações.');
        }
        
        return 0;
    }
    
    /**
     * Processa a exclusão de anúncios
     */
    private function processDeletion($ads, $type, $dryRun)
    {
        $count = $ads->count();
        
        if ($count === 0) {
            $this->info("Nenhum anúncio {$type} para processar.");
            return;
        }
        
        $this->info("Processando {$count} anúncios {$type} para exclusão...");
        
        $bar = $this->output->createProgressBar($count);
        $bar->start();
        
        $deleted = 0;
        
        foreach ($ads as $ad) {
            try {
                if (!$dryRun) {
                    $ad->delete();
                }
                $deleted++;
            } catch (\Exception $e) {
                $this->error("Erro ao excluir anúncio #{$ad->id}: " . $e->getMessage());
                Log::error("Erro ao excluir anúncio #{$ad->id}", [
                    'error' => $e->getMessage(),
                    'ad' => $ad->toArray(),
                ]);
            }
            
            $bar->advance();
        }
        
        $bar->finish();
        $this->newLine();
        
        $action = $dryRun ? 'Seriam excluídos' : 'Foram excluídos';
        $this->info("✓ {$action} {$deleted}/{$count} anúncios {$type}.");
        
        if (!$dryRun) {
            Log::info("Anúncios {$type} excluídos", [
                'total' => $count,
                'deleted' => $deleted,
                'type' => $type,
            ]);
        }
    }
    
    /**
     * Processa a atribuição de anúncios a um usuário
     */
    private function processAssignment($ads, $type, $user, $dryRun)
    {
        $count = $ads->count();
        
        if ($count === 0) {
            $this->info("Nenhum anúncio {$type} para processar.");
            return;
        }
        
        $this->info("Processando {$count} anúncios {$type} para atribuição ao usuário #{$user->id} ({$user->name})...");
        
        $bar = $this->output->createProgressBar($count);
        $bar->start();
        
        $updated = 0;
        
        foreach ($ads as $ad) {
            try {
                if (!$dryRun) {
                    $ad->user_id = $user->id;
                    $ad->save();
                }
                $updated++;
            } catch (\Exception $e) {
                $this->error("Erro ao atualizar anúncio #{$ad->id}: " . $e->getMessage());
                Log::error("Erro ao atualizar anúncio #{$ad->id}", [
                    'error' => $e->getMessage(),
                    'ad' => $ad->toArray(),
                    'user_id' => $user->id,
                ]);
            }
            
            $bar->advance();
        }
        
        $bar->finish();
        $this->newLine();
        
        $action = $dryRun ? 'Seriam atualizados' : 'Foram atualizados';
        $this->info("✓ {$action} {$updated}/{$count} anúncios {$type} para o usuário #{$user->id}.");
        
        if (!$dryRun) {
            Log::info("Anúncios {$type} atribuídos ao usuário", [
                'total' => $count,
                'updated' => $updated,
                'type' => $type,
                'user_id' => $user->id,
            ]);
        }
    }
}
