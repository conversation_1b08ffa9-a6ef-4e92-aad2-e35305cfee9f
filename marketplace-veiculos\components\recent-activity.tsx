import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Eye, Heart, MessageSquare, Car, Calendar } from "lucide-react"

const activities = [
  {
    id: 1,
    type: "view",
    title: "Seu anúncio Honda Civic 2022 foi visualizado",
    description: "Por um usuário de São Paulo, SP",
    timestamp: "2 horas atrás",
    icon: Eye,
    color: "text-primary",
  },
  {
    id: 2,
    type: "favorite",
    title: "Seu anúncio foi adicionado aos favoritos",
    description: "Yamaha MT-07 2021 foi favoritado por Maria Santos",
    timestamp: "4 horas atrás",
    icon: Heart,
    color: "text-destructive",
  },
  {
    id: 3,
    type: "contact",
    title: "Nova mensagem recebida",
    description: "Sobre Honda Civic 2022 - 'Aceita troca por moto?'",
    timestamp: "6 horas atrás",
    icon: MessageSquare,
    color: "text-accent",
  },
  {
    id: 4,
    type: "listing",
    title: "<PERSON><PERSON><PERSON> publicado com sucesso",
    description: "Kit Suspensão Civic Completo está ativo",
    timestamp: "1 dia atrás",
    icon: Car,
    color: "text-primary",
  },
  {
    id: 5,
    type: "view",
    title: "Pico de visualizações",
    description: "Honda Civic 2022 teve 15 visualizações hoje",
    timestamp: "1 dia atrás",
    icon: Eye,
    color: "text-primary",
  },
  {
    id: 6,
    type: "contact",
    title: "Contato via WhatsApp",
    description: "Sobre Yamaha MT-07 2021 - Interessado em agendar visita",
    timestamp: "2 dias atrás",
    icon: MessageSquare,
    color: "text-accent",
  },
]

const typeConfig = {
  view: { label: "Visualização", variant: "secondary" as const },
  favorite: { label: "Favorito", variant: "outline" as const },
  contact: { label: "Contato", variant: "default" as const },
  listing: { label: "Anúncio", variant: "secondary" as const },
}

export function RecentActivity() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold">Atividade Recente</h2>
        <Badge variant="outline">{activities.length} atividades</Badge>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Últimas Atividades
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {activities.map((activity, index) => {
              const Icon = activity.icon
              const config = typeConfig[activity.type as keyof typeof typeConfig]

              return (
                <div key={activity.id} className="flex items-start space-x-4">
                  <div className={`p-2 rounded-full bg-primary ${activity.color}`}>
                    <Icon className="h-4 w-4 text-primary-foreground" />
                  </div>

                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <p className="font-medium text-sm">{activity.title}</p>
                      <Badge variant={config.variant} className="text-xs">
                        {config.label}
                      </Badge>
                    </div>
                    <p className="text-sm text-muted-foreground mb-1">{activity.description}</p>
                    <p className="text-xs text-muted-foreground">{activity.timestamp}</p>
                  </div>
                </div>
              )
            })}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
