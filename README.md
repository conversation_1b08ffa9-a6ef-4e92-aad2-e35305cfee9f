# Loja Virtual de Veículos e Peças

Plataforma de comércio eletrônico especializada em venda e aluguel de carros, motos e peças automotivas.

## 🚀 Tecnologias

- **Backend**: Laravel 11 com Inertia.js
- **Frontend**: React com TypeScript
- **Banco de Dados**: MySQL 8.0
- **Cache**: Redis
- **Busca**: Mei<PERSON>earch
- **Ferramenta de Build**: Vite

## 🛠️ Pré-requisitos

- Docker e Docker Compose
- Git
- Node.js 18+ (opcional, apenas para desenvolvimento fora do Docker)
- Composer (opcional, apenas para desenvolvimento fora do Docker)

## 🚀 Como executar o projeto

### 1. Clone o repositório

```bash
git clone https://github.com/seu-usuario/loja-virtual.git
cd loja-virtual
```

### 2. Configure o ambiente

1. Copie o arquivo `.env.example` para `.env`:
   ```bash
   cp .env.example .env
   ```

2. Gere uma chave de aplicação:
   ```bash
   docker-compose run --rm app php artisan key:generate
   ```

### 3. Inicie os contêineres

```bash
docker-compose up -d
```

### 4. Instale as dependências

```bash
docker-compose exec app composer install
docker-compose exec app npm install
```

### 5. Execute as migrações e seeders

```bash
docker-compose exec app php artisan migrate --seed
```

### 6. Compile os assets

```bash
docker-compose exec app npm run build
```

### 7. Acesse a aplicação

A aplicação estará disponível em: http://localhost:8000

## 🛠️ Comandos úteis

- **Acessar o container da aplicação**:
  ```bash
  docker-compose exec app bash
  ```

- **Executar testes**:
  ```bash
  docker-compose exec app php artisan test
  ```

- **Monitorar alterações de frontend (hot reload)**:
  ```bash
  docker-compose exec app npm run dev
  ```

- **Acessar o banco de dados**:
  - Host: 127.0.0.1
  - Porta: 3306
  - Banco de dados: loja_virtual
  - Usuário: loja_user
  - Senha: password

- **Acessar o MailHog (para visualizar e-mails)**:
  http://localhost:8025

- **Acessar o Meilisearch (busca)**:
  http://localhost:7700

## 📦 Estrutura do Projeto

```
projeto-loja/
├── app/               # Código fonte do Laravel
├── bootstrap/         # Arquivos de inicialização
├── config/            # Arquivos de configuração
├── database/          # Migrações, seeders e factories
├── public/            # Pasta pública
├── resources/         # Views, assets não compilados
│   ├── js/            # Código JavaScript/TypeScript
│   ├── css/           # Estilos CSS
│   └── views/         # Templates Blade
├── routes/            # Rotas da aplicação
├── storage/           # Armazenamento de arquivos
└── tests/             # Testes automatizados
```

## 🤝 Contribuição

1. Faça um fork do projeto
2. Crie uma branch para sua feature (`git checkout -b feature/AmazingFeature`)
3. Commit suas alterações (`git commit -m 'Add some AmazingFeature'`)
4. Faça o push da branch (`git push origin feature/AmazingFeature`)
5. Abra um Pull Request

## 📄 Licença

Este projeto está licenciado sob a licença MIT - veja o arquivo [LICENSE](LICENSE) para mais detalhes.

## 👨‍💻 Desenvolvido por

[Seu Nome] - [<EMAIL>]
