<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Offer extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'user_id',
        'advertisement_id',
        'amount',
        'message',
        'status',
        'rejection_reason',
        'responded_at',
        'parent_offer_id',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'responded_at' => 'datetime',
    ];

    /**
     * The user who made the offer.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * The advertisement the offer was made for.
     */
    public function advertisement(): BelongsTo
    {
        return $this->belongsTo(Advertisement::class);
    }

    /**
     * The parent offer (for counter-offers).
     */
    public function parentOffer(): BelongsTo
    {
        return $this->belongsTo(Offer::class, 'parent_offer_id');
    }

    /**
     * Counter-offers made to this offer.
     */
    public function counterOffers(): HasMany
    {
        return $this->hasMany(Offer::class, 'parent_offer_id');
    }

    /**
     * Check if the offer is pending.
     */
    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    /**
     * Check if the offer is accepted.
     */
    public function isAccepted(): bool
    {
        return $this->status === 'accepted';
    }

    /**
     * Check if the offer is rejected.
     */
    public function isRejected(): bool
    {
        return $this->status === 'rejected';
    }

    /**
     * Check if the offer is cancelled.
     */
    public function isCancelled(): bool
    {
        return $this->status === 'cancelled';
    }

    /**
     * Check if the offer has been countered.
     */
    public function isCountered(): bool
    {
        return $this->status === 'countered';
    }

    /**
     * Check if the offer is a counter-offer.
     */
    public function isCounterOffer(): bool
    {
        return !is_null($this->parent_offer_id);
    }

    /**
     * Get the percentage difference from the advertisement price.
     */
    public function getPricePercentageAttribute(): float
    {
        if (!$this->advertisement || !$this->advertisement->price) {
            return 0;
        }

        return (($this->amount - $this->advertisement->price) / $this->advertisement->price) * 100;
    }

    /**
     * Get the formatted amount.
     */
    public function getFormattedAmountAttribute(): string
    {
        return 'R$ ' . number_format($this->amount, 2, ',', '.');
    }

    /**
     * Get the status label.
     */
    public function getStatusLabelAttribute(): string
    {
        return match ($this->status) {
            'pending' => 'Pendente',
            'accepted' => 'Aceita',
            'rejected' => 'Rejeitada',
            'cancelled' => 'Cancelada',
            'countered' => 'Contra-oferta',
            default => ucfirst($this->status),
        };
    }

    /**
     * Get the status color for UI.
     */
    public function getStatusColorAttribute(): string
    {
        return match ($this->status) {
            'pending' => 'orange',
            'accepted' => 'green',
            'rejected' => 'red',
            'cancelled' => 'gray',
            'countered' => 'blue',
            default => 'gray',
        };
    }

    /**
     * Scope to filter by status.
     */
    public function scopeStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to filter pending offers.
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope to filter accepted offers.
     */
    public function scopeAccepted($query)
    {
        return $query->where('status', 'accepted');
    }

    /**
     * Scope to filter rejected offers.
     */
    public function scopeRejected($query)
    {
        return $query->where('status', 'rejected');
    }

    /**
     * Scope to filter offers for a specific user.
     */
    public function scopeForUser($query, int $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope to filter offers for advertisements owned by a user.
     */
    public function scopeForAdvertisementOwner($query, int $userId)
    {
        return $query->whereHas('advertisement', function ($q) use ($userId) {
            $q->where('user_id', $userId);
        });
    }

    /**
     * Scope to filter recent offers.
     */
    public function scopeRecent($query, int $days = 30)
    {
        return $query->where('created_at', '>=', now()->subDays($days));
    }

    /**
     * Scope to order by amount.
     */
    public function scopeOrderByAmount($query, string $direction = 'desc')
    {
        return $query->orderBy('amount', $direction);
    }

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($offer) {
            // Ensure amount is positive
            if ($offer->amount <= 0) {
                throw new \InvalidArgumentException('Offer amount must be positive');
            }
        });

        static::updating(function ($offer) {
            // Set responded_at when status changes from pending
            if ($offer->isDirty('status') && $offer->getOriginal('status') === 'pending') {
                $offer->responded_at = now();
            }
        });
    }
}
