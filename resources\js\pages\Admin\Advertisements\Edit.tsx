import AdminLayout from '@/components/Layout/AdminLayout';
import { Button } from '@/components/ui/button';
import {
    Card,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle,
} from '@/components/ui/card';
import { Advertisement, User, Vehicle } from '@/types';
import { Head, Link } from '@inertiajs/react';
import { ArrowLeft } from 'lucide-react';
import AdvertisementForm from './Form';

interface AdvertisementEditProps {
    advertisement: Advertisement;
    vehicles: Vehicle[];
    users: User[];
    statuses: Record<string, string>;
}

export default function AdvertisementEdit({
    advertisement,
    vehicles,
    users,
    statuses,
}: AdvertisementEditProps) {
    return (
        <AdminLayout>
            <Head title={`Editar Anúncio: ${advertisement.title}`} />

            <div className="space-y-6">
                <div className="flex items-center justify-between">
                    <div>
                        <Link
                            href={route('admin.advertisements.index')}
                            className="flex items-center text-sm text-muted-foreground hover:text-foreground"
                        >
                            <ArrowLeft className="mr-1 h-4 w-4" /> Voltar para a
                            lista
                        </Link>
                        <h1 className="mt-2 text-2xl font-bold tracking-tight">
                            Editar Anúncio:{' '}
                            <span className="text-muted-foreground">
                                {advertisement.title}
                            </span>
                        </h1>
                    </div>

                    <div className="flex items-center gap-2">
                        <Link
                            href={route('admin.advertisements.show', {
                                advertisement: advertisement.id,
                            })}
                        >
                            <Button variant="outline" size="sm">
                                Visualizar
                            </Button>
                        </Link>
                    </div>
                </div>

                <Card>
                    <CardHeader>
                        <CardTitle>Editar Anúncio</CardTitle>
                        <CardDescription>
                            Atualize as informações do anúncio abaixo.
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <AdvertisementForm
                            advertisement={advertisement}
                            vehicles={vehicles}
                            users={users}
                            statuses={statuses}
                            isEdit={true}
                        />
                    </CardContent>
                </Card>
            </div>
        </AdminLayout>
    );
}
