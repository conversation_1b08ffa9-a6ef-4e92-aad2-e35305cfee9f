import { ListingFilters } from "@/components/listing-filters"
import { ListingGrid } from "@/components/listing-grid"
import { ListingHeader } from "@/components/listing-header"
import { Head<PERSON> } from "@/components/header"
import { Footer } from "@/components/footer"

export default function ListingsPage() {
  return (
    <div className="min-h-screen bg-background">
      <Header />
      <main className="container mx-auto px-4 py-8">
        <ListingHeader />
        <div className="flex flex-col lg:flex-row gap-8">
          <aside className="lg:w-80 flex-shrink-0">
            <ListingFilters />
          </aside>
          <div className="flex-1">
            <ListingGrid />
          </div>
        </div>
      </main>
      <Footer />
    </div>
  )
}
