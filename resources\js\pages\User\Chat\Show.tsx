import { Button } from '@/components/ui/button';
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import MainLayout from '@/layouts/MainLayout';
import { PageProps } from '@/types';
import { Head, Link, useForm } from '@inertiajs/react';
import { ArrowLeft, ExternalLink, Mail, Phone, Send, User } from 'lucide-react';
import { useEffect, useRef, useState } from 'react';

interface ChatMessage {
    id: number;
    message: string;
    user_id: number;
    read_at?: string;
    created_at: string;
    user: {
        id: number;
        name: string;
        avatar?: string;
    };
}

interface Chat {
    id: number;
    advertisement: {
        id: number;
        title: string;
        price: number;
        vehicle: {
            brand: {
                name: string;
            };
        };
        featured_image?: {
            url: string;
        };
    };
    buyer: {
        id: number;
        name: string;
        avatar?: string;
        phone?: string;
        email: string;
    };
    seller: {
        id: number;
        name: string;
        avatar?: string;
        phone?: string;
        email: string;
    };
}

interface ChatShowProps extends PageProps {
    chat: Chat;
    messages: {
        data: ChatMessage[];
        links: any;
        meta: any;
    };
}

export default function ChatShow({ chat, messages, auth }: ChatShowProps) {
    const [isLoading, setIsLoading] = useState(false);
    const messagesEndRef = useRef<HTMLDivElement>(null);
    const currentUserId = auth.user?.id || 0;

    const { data, setData, post, processing, errors, reset } = useForm({
        message: '',
    });

    const otherParticipant =
        chat.buyer.id === currentUserId ? chat.seller : chat.buyer;

    const scrollToBottom = () => {
        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    };

    useEffect(() => {
        scrollToBottom();
    }, [messages.data]);

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();

        if (!data.message.trim()) return;

        setIsLoading(true);

        post(`/minha-conta/chat/${chat.id}/message`, {
            onSuccess: () => {
                reset('message');
                setIsLoading(false);
                // Aqui você poderia implementar atualização em tempo real
            },
            onError: () => {
                setIsLoading(false);
            },
        });
    };

    const formatTime = (dateString: string) => {
        const date = new Date(dateString);
        return date.toLocaleString('pt-BR', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
        });
    };

    const formatMessageTime = (dateString: string) => {
        const date = new Date(dateString);
        const now = new Date();
        const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

        if (diffInHours < 24) {
            return date.toLocaleTimeString('pt-BR', {
                hour: '2-digit',
                minute: '2-digit',
            });
        } else {
            return date.toLocaleDateString('pt-BR', {
                day: '2-digit',
                month: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
            });
        }
    };

    return (
        <MainLayout>
            <Head title={`Chat - ${chat.advertisement.title}`} />

            <div className="min-h-screen bg-gray-50">
                <div className="container mx-auto px-4 py-6">
                    {/* Header */}
                    <div className="mb-6">
                        <Link href="/minha-conta/chat">
                            <Button variant="ghost" className="mb-4">
                                <ArrowLeft className="mr-2 h-4 w-4" />
                                Voltar para mensagens
                            </Button>
                        </Link>
                    </div>

                    <div className="grid grid-cols-1 gap-6 lg:grid-cols-4">
                        {/* Sidebar com informações */}
                        <div className="space-y-4 lg:col-span-1">
                            {/* Informações do anúncio */}
                            <Card>
                                <CardHeader>
                                    <CardTitle className="text-lg">
                                        Anúncio
                                    </CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="flex gap-3">
                                        <img
                                            src={
                                                chat.advertisement
                                                    .featured_image?.url ||
                                                '/placeholder-car.jpg'
                                            }
                                            alt={chat.advertisement.title}
                                            className="h-16 w-16 rounded-lg object-cover"
                                        />
                                        <div className="flex-1">
                                            <h3 className="text-sm font-medium">
                                                {chat.advertisement.title}
                                            </h3>
                                            <p className="text-sm text-gray-600">
                                                {
                                                    chat.advertisement.vehicle
                                                        .brand.name
                                                }
                                            </p>
                                            <p className="text-lg font-bold text-green-600">
                                                R${' '}
                                                {chat.advertisement.price.toLocaleString(
                                                    'pt-BR',
                                                )}
                                            </p>
                                        </div>
                                    </div>

                                    <Link
                                        href={`/anuncio/${chat.advertisement.id}`}
                                    >
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            className="w-full"
                                        >
                                            <ExternalLink className="mr-2 h-4 w-4" />
                                            Ver anúncio
                                        </Button>
                                    </Link>
                                </CardContent>
                            </Card>

                            {/* Informações do contato */}
                            <Card>
                                <CardHeader>
                                    <CardTitle className="text-lg">
                                        Contato
                                    </CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="flex items-center gap-3">
                                        {otherParticipant.avatar ? (
                                            <img
                                                src={otherParticipant.avatar}
                                                alt={otherParticipant.name}
                                                className="h-12 w-12 rounded-full"
                                            />
                                        ) : (
                                            <div className="flex h-12 w-12 items-center justify-center rounded-full bg-gray-200">
                                                <User className="h-6 w-6 text-gray-400" />
                                            </div>
                                        )}
                                        <div>
                                            <h3 className="font-medium">
                                                {otherParticipant.name}
                                            </h3>
                                            <p className="text-sm text-gray-600">
                                                {chat.buyer.id === currentUserId
                                                    ? 'Vendedor'
                                                    : 'Comprador'}
                                            </p>
                                        </div>
                                    </div>

                                    <div className="space-y-2">
                                        <div className="flex items-center gap-2 text-sm">
                                            <Mail className="h-4 w-4 text-gray-400" />
                                            <span className="text-gray-600">
                                                {otherParticipant.email}
                                            </span>
                                        </div>

                                        {otherParticipant.phone && (
                                            <div className="flex items-center gap-2 text-sm">
                                                <Phone className="h-4 w-4 text-gray-400" />
                                                <span className="text-gray-600">
                                                    {otherParticipant.phone}
                                                </span>
                                            </div>
                                        )}
                                    </div>

                                    <Link
                                        href={`/usuario/${otherParticipant.id}`}
                                    >
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            className="w-full"
                                        >
                                            Ver perfil
                                        </Button>
                                    </Link>
                                </CardContent>
                            </Card>
                        </div>

                        {/* Área de chat */}
                        <div className="lg:col-span-3">
                            <Card className="flex h-[600px] flex-col">
                                <CardHeader className="border-b">
                                    <CardTitle className="flex items-center gap-3">
                                        <div className="flex items-center gap-2">
                                            {otherParticipant.avatar ? (
                                                <img
                                                    src={
                                                        otherParticipant.avatar
                                                    }
                                                    alt={otherParticipant.name}
                                                    className="h-8 w-8 rounded-full"
                                                />
                                            ) : (
                                                <div className="flex h-8 w-8 items-center justify-center rounded-full bg-gray-200">
                                                    <User className="h-4 w-4 text-gray-400" />
                                                </div>
                                            )}
                                            <span>{otherParticipant.name}</span>
                                        </div>
                                    </CardTitle>
                                </CardHeader>

                                {/* Mensagens */}
                                <CardContent className="flex-1 space-y-4 overflow-y-auto p-4">
                                    {messages.data.length === 0 ? (
                                        <div className="py-8 text-center text-gray-500">
                                            <p>Nenhuma mensagem ainda.</p>
                                            <p className="text-sm">
                                                Envie a primeira mensagem para
                                                iniciar a conversa!
                                            </p>
                                        </div>
                                    ) : (
                                        messages.data.map((message) => {
                                            const isCurrentUser =
                                                message.user_id ===
                                                currentUserId;

                                            return (
                                                <div
                                                    key={message.id}
                                                    className={`flex ${isCurrentUser ? 'justify-end' : 'justify-start'}`}
                                                >
                                                    <div
                                                        className={`max-w-xs rounded-lg px-4 py-2 lg:max-w-md ${
                                                            isCurrentUser
                                                                ? 'bg-orange-500 text-white'
                                                                : 'bg-gray-200 text-gray-900'
                                                        }`}
                                                    >
                                                        <p className="text-sm">
                                                            {message.message}
                                                        </p>
                                                        <p
                                                            className={`mt-1 text-xs ${
                                                                isCurrentUser
                                                                    ? 'text-orange-100'
                                                                    : 'text-gray-500'
                                                            }`}
                                                        >
                                                            {formatMessageTime(
                                                                message.created_at,
                                                            )}
                                                            {isCurrentUser &&
                                                                message.read_at && (
                                                                    <span className="ml-1">
                                                                        ✓✓
                                                                    </span>
                                                                )}
                                                        </p>
                                                    </div>
                                                </div>
                                            );
                                        })
                                    )}
                                    <div ref={messagesEndRef} />
                                </CardContent>

                                {/* Formulário de envio */}
                                <div className="border-t p-4">
                                    <form
                                        onSubmit={handleSubmit}
                                        className="flex gap-2"
                                    >
                                        <Textarea
                                            value={data.message}
                                            onChange={(e) =>
                                                setData(
                                                    'message',
                                                    e.target.value,
                                                )
                                            }
                                            placeholder="Digite sua mensagem..."
                                            className="max-h-[120px] min-h-[40px] flex-1 resize-none"
                                            onKeyDown={(e) => {
                                                if (
                                                    e.key === 'Enter' &&
                                                    !e.shiftKey
                                                ) {
                                                    e.preventDefault();
                                                    handleSubmit(e);
                                                }
                                            }}
                                        />
                                        <Button
                                            type="submit"
                                            disabled={
                                                processing ||
                                                isLoading ||
                                                !data.message.trim()
                                            }
                                            className="self-end"
                                        >
                                            <Send className="h-4 w-4" />
                                        </Button>
                                    </form>

                                    {errors.message && (
                                        <p className="mt-1 text-sm text-red-500">
                                            {errors.message}
                                        </p>
                                    )}
                                </div>
                            </Card>
                        </div>
                    </div>
                </div>
            </div>
        </MainLayout>
    );
}
