import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select';
import MainLayout from '@/layouts/MainLayout';
import { PageProps } from '@/types';
import { Head, Link, router } from '@inertiajs/react';
import { 
    Edit, 
    Eye, 
    MapPin, 
    Plus, 
    Search, 
    Trash2 
} from 'lucide-react';
import { useState } from 'react';

interface Advertisement {
    id: number;
    title: string;
    description: string;
    price: number;
    location: string;
    created_at: string;
    status: 'draft' | 'published' | 'paused' | 'sold';
    is_featured: boolean;
    views_count: number;
    featured_image?: {
        url: string;
        alt: string;
    };
    vehicle: {
        id: number;
        model: string;
        year: number;
        mileage: number;
        fuel_type: string;
        transmission: string;
        brand: {
            id: number;
            name: string;
        };
        category: {
            id: number;
            name: string;
        };
    };
}

interface UserAdvertisementsIndexProps extends PageProps {
    advertisements: {
        data: Advertisement[];
        links: any[];
        meta: any;
    };
    filters: Record<string, any>;
}

export default function UserAdvertisementsIndex({
    advertisements,
    filters,
}: UserAdvertisementsIndexProps) {
    const [localFilters, setLocalFilters] = useState({
        search: filters.search || '',
        status: filters.status || '',
    });

    const handleFilterChange = (key: string, value: any) => {
        setLocalFilters(prev => ({ ...prev, [key]: value }));
    };

    const applyFilters = () => {
        const cleanFilters = Object.fromEntries(
            Object.entries(localFilters).filter(([_, value]) => 
                value !== '' && value !== null && value !== undefined
            )
        );
        
        router.get('/minha-conta/anuncios', cleanFilters, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    const formatPrice = (price: number) => {
        return new Intl.NumberFormat('pt-BR', {
            style: 'currency',
            currency: 'BRL',
        }).format(price);
    };

    const formatMileage = (mileage: number) => {
        return new Intl.NumberFormat('pt-BR').format(mileage) + ' km';
    };

    const formatDate = (date: string) => {
        return new Date(date).toLocaleDateString('pt-BR');
    };

    const getStatusBadge = (status: string) => {
        const statusConfig = {
            draft: { label: 'Rascunho', variant: 'secondary' as const },
            published: { label: 'Publicado', variant: 'default' as const },
            paused: { label: 'Pausado', variant: 'outline' as const },
            sold: { label: 'Vendido', variant: 'destructive' as const },
        };

        const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.draft;
        
        return (
            <Badge variant={config.variant}>
                {config.label}
            </Badge>
        );
    };

    const handleDelete = (advertisementId: number) => {
        if (confirm('Tem certeza que deseja excluir este anúncio? Esta ação não pode ser desfeita.')) {
            router.delete(`/minha-conta/anuncios/${advertisementId}`);
        }
    };

    return (
        <MainLayout>
            <Head title="Meus Anúncios" />
            
            <div className="min-h-screen bg-gray-50">
                {/* Header */}
                <div className="bg-white border-b">
                    <div className="container mx-auto px-4 py-6">
                        <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4">
                            <div>
                                <h1 className="text-3xl font-bold">Meus Anúncios</h1>
                                <p className="text-gray-600">
                                    Gerencie seus anúncios de veículos
                                </p>
                            </div>
                            
                            <Link href="/minha-conta/anuncios/create">
                                <Button size="lg">
                                    <Plus className="mr-2 h-4 w-4" />
                                    Criar Anúncio
                                </Button>
                            </Link>
                        </div>

                        {/* Filtros */}
                        <div className="mt-6 flex flex-col lg:flex-row gap-4">
                            <div className="flex-1">
                                <div className="relative">
                                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                                    <Input
                                        placeholder="Buscar nos meus anúncios..."
                                        value={localFilters.search}
                                        onChange={(e) => handleFilterChange('search', e.target.value)}
                                        onKeyPress={(e) => e.key === 'Enter' && applyFilters()}
                                        className="pl-10"
                                    />
                                </div>
                            </div>
                            
                            <Select
                                value={localFilters.status}
                                onValueChange={(value) => {
                                    handleFilterChange('status', value);
                                    applyFilters();
                                }}
                            >
                                <SelectTrigger className="w-48">
                                    <SelectValue placeholder="Todos os status" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="">Todos os status</SelectItem>
                                    <SelectItem value="draft">Rascunho</SelectItem>
                                    <SelectItem value="published">Publicado</SelectItem>
                                    <SelectItem value="paused">Pausado</SelectItem>
                                    <SelectItem value="sold">Vendido</SelectItem>
                                </SelectContent>
                            </Select>
                            
                            <Button onClick={applyFilters}>
                                <Search className="mr-2 h-4 w-4" />
                                Buscar
                            </Button>
                        </div>
                    </div>
                </div>

                <div className="container mx-auto px-4 py-6">
                    {/* Estatísticas rápidas */}
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                        <Card>
                            <CardContent className="p-4">
                                <div className="text-center">
                                    <div className="text-2xl font-bold text-blue-600">
                                        {advertisements.meta.total}
                                    </div>
                                    <div className="text-sm text-gray-600">Total de anúncios</div>
                                </div>
                            </CardContent>
                        </Card>
                        
                        <Card>
                            <CardContent className="p-4">
                                <div className="text-center">
                                    <div className="text-2xl font-bold text-green-600">
                                        {advertisements.data.filter(ad => ad.status === 'published').length}
                                    </div>
                                    <div className="text-sm text-gray-600">Publicados</div>
                                </div>
                            </CardContent>
                        </Card>
                        
                        <Card>
                            <CardContent className="p-4">
                                <div className="text-center">
                                    <div className="text-2xl font-bold text-yellow-600">
                                        {advertisements.data.filter(ad => ad.status === 'draft').length}
                                    </div>
                                    <div className="text-sm text-gray-600">Rascunhos</div>
                                </div>
                            </CardContent>
                        </Card>
                        
                        <Card>
                            <CardContent className="p-4">
                                <div className="text-center">
                                    <div className="text-2xl font-bold text-purple-600">
                                        {advertisements.data.reduce((sum, ad) => sum + (ad.views_count || 0), 0)}
                                    </div>
                                    <div className="text-sm text-gray-600">Total de visualizações</div>
                                </div>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Lista de anúncios */}
                    {advertisements.data.length > 0 ? (
                        <div className="space-y-4">
                            {advertisements.data.map((ad) => (
                                <Card key={ad.id} className="overflow-hidden">
                                    <div className="flex">
                                        {/* Imagem */}
                                        <div className="w-48 h-32 flex-shrink-0 relative">
                                            {ad.featured_image ? (
                                                <img
                                                    src={ad.featured_image.url}
                                                    alt={ad.featured_image.alt}
                                                    className="w-full h-full object-cover"
                                                />
                                            ) : (
                                                <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                                                    <span className="text-gray-400 text-sm">Sem imagem</span>
                                                </div>
                                            )}
                                            
                                            {ad.is_featured && (
                                                <Badge className="absolute top-2 left-2 bg-yellow-500">
                                                    Destaque
                                                </Badge>
                                            )}
                                        </div>
                                        
                                        {/* Conteúdo */}
                                        <div className="flex-1 p-4">
                                            <div className="flex justify-between items-start mb-2">
                                                <div className="flex-1">
                                                    <h3 className="font-semibold text-lg line-clamp-1">
                                                        {ad.title}
                                                    </h3>
                                                    <div className="flex flex-wrap gap-2 text-sm text-gray-600 mb-2">
                                                        <span>{ad.vehicle.brand.name}</span>
                                                        <span>•</span>
                                                        <span>{ad.vehicle.year}</span>
                                                        <span>•</span>
                                                        <span>{formatMileage(ad.vehicle.mileage)}</span>
                                                    </div>
                                                </div>
                                                
                                                <div className="flex items-center gap-2">
                                                    {getStatusBadge(ad.status)}
                                                </div>
                                            </div>
                                            
                                            <div className="flex justify-between items-end">
                                                <div>
                                                    <div className="text-2xl font-bold text-green-600 mb-1">
                                                        {formatPrice(ad.price)}
                                                    </div>
                                                    <div className="flex items-center text-sm text-gray-500">
                                                        <MapPin className="h-3 w-3 mr-1" />
                                                        {ad.location}
                                                    </div>
                                                    <div className="text-xs text-gray-400">
                                                        Criado em {formatDate(ad.created_at)}
                                                    </div>
                                                    {ad.views_count > 0 && (
                                                        <div className="flex items-center text-xs text-gray-500 mt-1">
                                                            <Eye className="h-3 w-3 mr-1" />
                                                            {ad.views_count} visualizações
                                                        </div>
                                                    )}
                                                </div>
                                                
                                                <div className="flex gap-2">
                                                    <Link href={`/minha-conta/anuncios/${ad.id}`}>
                                                        <Button variant="outline" size="sm">
                                                            <Eye className="h-4 w-4" />
                                                        </Button>
                                                    </Link>
                                                    
                                                    <Link href={`/minha-conta/anuncios/${ad.id}/edit`}>
                                                        <Button variant="outline" size="sm">
                                                            <Edit className="h-4 w-4" />
                                                        </Button>
                                                    </Link>
                                                    
                                                    <Button 
                                                        variant="outline" 
                                                        size="sm"
                                                        onClick={() => handleDelete(ad.id)}
                                                    >
                                                        <Trash2 className="h-4 w-4" />
                                                    </Button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </Card>
                            ))}
                        </div>
                    ) : (
                        <div className="text-center py-12">
                            <h3 className="text-xl font-semibold mb-2">Nenhum anúncio encontrado</h3>
                            <p className="text-gray-600 mb-4">
                                {filters.search || filters.status 
                                    ? 'Tente ajustar os filtros de busca'
                                    : 'Você ainda não criou nenhum anúncio.'
                                }
                            </p>
                            <Link href="/minha-conta/anuncios/create">
                                <Button>
                                    <Plus className="mr-2 h-4 w-4" />
                                    Criar primeiro anúncio
                                </Button>
                            </Link>
                        </div>
                    )}

                    {/* Paginação */}
                    {advertisements.links && advertisements.links.length > 3 && (
                        <div className="mt-8 flex justify-center">
                            <div className="flex gap-2">
                                {advertisements.links.map((link, index) => (
                                    <Button
                                        key={index}
                                        variant={link.active ? 'default' : 'outline'}
                                        size="sm"
                                        onClick={() => {
                                            if (link.url) {
                                                router.get(link.url);
                                            }
                                        }}
                                        disabled={!link.url}
                                        dangerouslySetInnerHTML={{ __html: link.label }}
                                    />
                                ))}
                            </div>
                        </div>
                    )}
                </div>
            </div>
        </MainLayout>
    );
}
