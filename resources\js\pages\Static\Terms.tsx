import MainLayout from '@/layouts/MainLayout';
import { Head } from '@inertiajs/react';

export default function Terms() {
    return (
        <MainLayout>
            <Head title="Termos de Uso" />
            
            <div className="container mx-auto px-4 py-8">
                <div className="max-w-4xl mx-auto">
                    <h1 className="text-3xl font-bold mb-8">Termos de Uso</h1>
                    
                    <div className="prose max-w-none">
                        <div className="bg-gray-50 rounded-lg p-6 mb-8">
                            <p className="text-sm text-gray-600">
                                <strong>Última atualização:</strong> 23 de setembro de 2024
                            </p>
                        </div>
                        
                        <section className="mb-8">
                            <h2 className="text-xl font-semibold mb-4">1. Aceitação dos Termos</h2>
                            <p className="text-gray-600 mb-4">
                                Ao acessar e usar a plataforma VeiculosBR, você concorda em cumprir e estar vinculado a estes Termos de Uso. 
                                Se você não concordar com qualquer parte destes termos, não deve usar nossos serviços.
                            </p>
                        </section>
                        
                        <section className="mb-8">
                            <h2 className="text-xl font-semibold mb-4">2. Descrição do Serviço</h2>
                            <p className="text-gray-600 mb-4">
                                A VeiculosBR é uma plataforma online que conecta compradores e vendedores de veículos e peças automotivas. 
                                Facilitamos a comunicação entre as partes, mas não somos responsáveis pelas transações realizadas.
                            </p>
                        </section>
                        
                        <section className="mb-8">
                            <h2 className="text-xl font-semibold mb-4">3. Responsabilidades do Usuário</h2>
                            <ul className="list-disc pl-6 space-y-2 text-gray-600">
                                <li>Fornecer informações verdadeiras e atualizadas</li>
                                <li>Manter a confidencialidade de sua conta</li>
                                <li>Usar a plataforma de forma legal e ética</li>
                                <li>Respeitar os direitos de outros usuários</li>
                                <li>Não publicar conteúdo ofensivo ou ilegal</li>
                            </ul>
                        </section>
                        
                        <section className="mb-8">
                            <h2 className="text-xl font-semibold mb-4">4. Anúncios e Conteúdo</h2>
                            <p className="text-gray-600 mb-4">
                                Os usuários são totalmente responsáveis pelo conteúdo de seus anúncios. 
                                Reservamo-nos o direito de remover qualquer conteúdo que viole nossos termos ou políticas.
                            </p>
                        </section>
                        
                        <section className="mb-8">
                            <h2 className="text-xl font-semibold mb-4">5. Limitação de Responsabilidade</h2>
                            <p className="text-gray-600 mb-4">
                                A VeiculosBR não se responsabiliza por:
                            </p>
                            <ul className="list-disc pl-6 space-y-2 text-gray-600">
                                <li>Qualidade, segurança ou legalidade dos itens anunciados</li>
                                <li>Veracidade das informações fornecidas pelos usuários</li>
                                <li>Transações realizadas entre usuários</li>
                                <li>Danos diretos ou indiretos decorrentes do uso da plataforma</li>
                            </ul>
                        </section>
                        
                        <section className="mb-8">
                            <h2 className="text-xl font-semibold mb-4">6. Modificações dos Termos</h2>
                            <p className="text-gray-600 mb-4">
                                Reservamo-nos o direito de modificar estes termos a qualquer momento. 
                                As alterações entrarão em vigor imediatamente após a publicação na plataforma.
                            </p>
                        </section>
                        
                        <section className="mb-8">
                            <h2 className="text-xl font-semibold mb-4">7. Contato</h2>
                            <p className="text-gray-600">
                                Para dúvidas sobre estes termos, entre em contato conosco através do e-mail: 
                                <a href="mailto:<EMAIL>" className="text-blue-600 hover:underline">
                                    <EMAIL>
                                </a>
                            </p>
                        </section>
                    </div>
                </div>
            </div>
        </MainLayout>
    );
}
