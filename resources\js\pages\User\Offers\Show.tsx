import { Head, <PERSON>, useForm } from '@inertiajs/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { 
    ArrowLeft, 
    DollarSign, 
    Calendar,
    User,
    MessageCircle,
    CheckCircle,
    XCircle,
    Clock
} from 'lucide-react';
import MainLayout from '@/layouts/MainLayout';
import { PageProps } from '@/types';

interface Offer {
    id: number;
    amount: number;
    message: string;
    status: 'pending' | 'accepted' | 'rejected' | 'cancelled' | 'countered';
    created_at: string;
    user: {
        id: number;
        name: string;
    };
    advertisement: {
        id: number;
        title: string;
        price: number;
        location: string;
        vehicle: {
            brand: {
                name: string;
            };
            model: string;
            year_manufacture: number;
        };
        user: {
            id: number;
            name: string;
        };
        images: Array<{
            url: string;
        }>;
    };
}

interface ShowOfferProps extends PageProps {
    offer: Offer;
    canRespond: boolean;
}

export default function ShowOffer({ offer, canRespond }: ShowOfferProps) {
    const { data, setData, post, processing } = useForm({
        rejection_reason: '',
    });

    const formatPrice = (price: number) => {
        return new Intl.NumberFormat('pt-BR', {
            style: 'currency',
            currency: 'BRL',
        }).format(price);
    };

    const formatDate = (date: string) => {
        return new Date(date).toLocaleDateString('pt-BR', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
        });
    };

    const getStatusBadge = (status: string) => {
        const statusConfig = {
            pending: { label: 'Pendente', variant: 'secondary' as const, icon: Clock },
            accepted: { label: 'Aceita', variant: 'default' as const, icon: CheckCircle },
            rejected: { label: 'Rejeitada', variant: 'destructive' as const, icon: XCircle },
            cancelled: { label: 'Cancelada', variant: 'outline' as const, icon: XCircle },
            countered: { label: 'Contra-oferta', variant: 'secondary' as const, icon: MessageCircle },
        };

        const config = statusConfig[status as keyof typeof statusConfig];
        const Icon = config.icon;

        return (
            <Badge variant={config.variant} className="flex items-center gap-1">
                <Icon className="h-3 w-3" />
                {config.label}
            </Badge>
        );
    };

    const handleAccept = () => {
        post(`/minha-conta/ofertas/${offer.id}/aceitar`);
    };

    const handleReject = (e: React.FormEvent) => {
        e.preventDefault();
        post(`/minha-conta/ofertas/${offer.id}/rejeitar`);
    };

    const difference = ((offer.amount - offer.advertisement.price) / offer.advertisement.price) * 100;

    return (
        <MainLayout>
            <Head title={`Oferta - ${offer.advertisement.title}`} />
            
            <div className="min-h-screen bg-gray-50">
                <div className="container mx-auto px-4 py-6">
                    {/* Header */}
                    <div className="mb-6">
                        <Link href="/minha-conta/ofertas">
                            <Button variant="outline" className="mb-4">
                                <ArrowLeft className="mr-2 h-4 w-4" />
                                Voltar para ofertas
                            </Button>
                        </Link>
                        
                        <div className="flex items-center justify-between">
                            <div>
                                <h1 className="text-3xl font-bold">Detalhes da Oferta</h1>
                                <p className="text-gray-600">
                                    Oferta #{offer.id}
                                </p>
                            </div>
                            {getStatusBadge(offer.status)}
                        </div>
                    </div>

                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        {/* Advertisement Info */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Anúncio</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="flex gap-4">
                                    <img
                                        src={offer.advertisement.images[0]?.url || '/placeholder-car.jpg'}
                                        alt={offer.advertisement.title}
                                        className="w-24 h-24 object-cover rounded"
                                    />
                                    <div className="flex-1">
                                        <h3 className="font-semibold text-lg">
                                            {offer.advertisement.title}
                                        </h3>
                                        <p className="text-gray-600">
                                            {offer.advertisement.vehicle.brand.name} {offer.advertisement.vehicle.model} {offer.advertisement.vehicle.year_manufacture}
                                        </p>
                                        <p className="text-2xl font-bold text-green-600">
                                            {formatPrice(offer.advertisement.price)}
                                        </p>
                                        <p className="text-sm text-gray-500">
                                            {offer.advertisement.location}
                                        </p>
                                    </div>
                                </div>
                                
                                <div className="mt-4 pt-4 border-t">
                                    <h4 className="font-medium mb-2">Vendedor</h4>
                                    <p className="text-gray-600">{offer.advertisement.user.name}</p>
                                </div>
                            </CardContent>
                        </Card>

                        {/* Offer Details */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <DollarSign className="h-5 w-5" />
                                    Detalhes da Oferta
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="grid grid-cols-2 gap-4">
                                    <div>
                                        <p className="text-sm text-gray-500">Valor Oferecido</p>
                                        <p className="text-2xl font-bold text-blue-600">
                                            {formatPrice(offer.amount)}
                                        </p>
                                    </div>
                                    <div>
                                        <p className="text-sm text-gray-500">Diferença</p>
                                        <p className={`text-lg font-semibold ${difference >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                                            {difference >= 0 ? '+' : ''}{difference.toFixed(1)}%
                                        </p>
                                    </div>
                                </div>

                                <div className="flex items-center gap-2 text-sm text-gray-600">
                                    <User className="h-4 w-4" />
                                    <span>Por: {offer.user.name}</span>
                                </div>

                                <div className="flex items-center gap-2 text-sm text-gray-600">
                                    <Calendar className="h-4 w-4" />
                                    <span>Em: {formatDate(offer.created_at)}</span>
                                </div>

                                {offer.message && (
                                    <div>
                                        <div className="flex items-center gap-2 mb-2">
                                            <MessageCircle className="h-4 w-4" />
                                            <span className="font-medium">Mensagem</span>
                                        </div>
                                        <p className="text-gray-700 bg-gray-50 p-3 rounded">
                                            {offer.message}
                                        </p>
                                    </div>
                                )}
                            </CardContent>
                        </Card>
                    </div>

                    {/* Actions */}
                    {canRespond && offer.status === 'pending' && (
                        <Card className="mt-6">
                            <CardHeader>
                                <CardTitle>Responder à Oferta</CardTitle>
                                <CardDescription>
                                    Você pode aceitar ou rejeitar esta oferta
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                <div className="flex gap-4">
                                    <Button 
                                        onClick={handleAccept}
                                        disabled={processing}
                                        className="flex-1"
                                    >
                                        <CheckCircle className="mr-2 h-4 w-4" />
                                        Aceitar Oferta
                                    </Button>
                                    
                                    <form onSubmit={handleReject} className="flex-1">
                                        <div className="space-y-2">
                                            <Label htmlFor="rejection_reason">Motivo da rejeição (opcional)</Label>
                                            <Textarea
                                                id="rejection_reason"
                                                placeholder="Explique o motivo da rejeição..."
                                                value={data.rejection_reason}
                                                onChange={(e) => setData('rejection_reason', e.target.value)}
                                                rows={2}
                                            />
                                            <Button 
                                                type="submit"
                                                variant="destructive"
                                                disabled={processing}
                                                className="w-full"
                                            >
                                                <XCircle className="mr-2 h-4 w-4" />
                                                Rejeitar Oferta
                                            </Button>
                                        </div>
                                    </form>
                                </div>
                            </CardContent>
                        </Card>
                    )}
                </div>
            </div>
        </MainLayout>
    );
}
