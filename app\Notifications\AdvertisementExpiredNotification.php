<?php

namespace App\Notifications;

use App\Models\Advertisement;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class AdvertisementExpiredNotification extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new notification instance.
     */
    public function __construct(public Advertisement $advertisement)
    {
        //
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable)
    {
        return (new MailMessage)
                    ->subject('Seu anúncio expirou')
                    ->line('O período de exibição do seu anúncio expirou.')
                    ->line('Detalhes do anúncio:')
                    ->line('- Título: ' . $this->advertisement->title)
                    ->line('- Data de expiração: ' . $this->advertisement->expires_at->format('d/m/Y'))
                    ->line('Você pode renovar o anúncio para continuar recebendo visualizações.')
                    ->action('Renovar Anúncio', route('advertisements.renew', $this->advertisement))
                    ->line('Obrigado por usar nossa plataforma!');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'message' => 'Seu anúncio expirou em ' . $this->advertisement->expires_at->format('d/m/Y'),
            'link' => route('advertisements.renew', $this->advertisement),
            'advertisement_id' => $this->advertisement->id,
            'advertisement_title' => $this->advertisement->title,
        ];
    }
}
