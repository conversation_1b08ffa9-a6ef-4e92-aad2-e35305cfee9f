import Link from "next/link"
import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { MapPin, Calendar, Fuel, Heart, X } from "lucide-react"

const favoriteListings = [
  {
    id: 2,
    title: "Toyota Corolla 2023 Hybrid",
    price: "R$ 125.000",
    location: "Belo Horizonte, MG",
    year: "2023",
    fuel: "Híbrido",
    mileage: "8.000 km",
    image: "/toyota-corolla-2023-white-car.jpg",
    type: "<PERSON><PERSON>",
    seller: "<PERSON>",
    addedAt: "2024-01-20",
  },
  {
    id: 3,
    title: "Volkswagen Golf 2020 TSI",
    price: "R$ 78.500",
    location: "Porto Alegre, RS",
    year: "2020",
    fuel: "Gasolina",
    mileage: "45.000 km",
    image: "/volkswagen-golf-2020-red-car.jpg",
    type: "<PERSON><PERSON>",
    seller: "Pedro Costa",
    addedAt: "2024-01-18",
  },
  {
    id: 4,
    title: "Honda CB 600F Hornet 2019",
    price: "R$ 28.900",
    location: "Salvador, BA",
    year: "2019",
    fuel: "Gasolina",
    mileage: "32.000 km",
    image: "/honda-cb-600f-2019-yellow-motorcycle.jpg",
    type: "Moto",
    seller: "Moto Center BA",
    addedAt: "2024-01-15",
  },
]

export function UserFavorites() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold">Meus Favoritos</h2>
        <p className="text-sm text-muted-foreground">{favoriteListings.length} anúncios salvos</p>
      </div>

      {favoriteListings.length === 0 ? (
        <Card>
          <CardContent className="p-8 text-center">
            <Heart className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">Nenhum favorito ainda</h3>
            <p className="text-muted-foreground mb-4">
              Salve anúncios interessantes clicando no coração para encontrá-los facilmente depois.
            </p>
            <Button asChild>
              <Link href="/anuncios">Explorar anúncios</Link>
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {favoriteListings.map((listing) => (
            <Card key={listing.id} className="group hover:shadow-lg transition-all duration-300">
              <div className="relative">
                <img
                  src={listing.image || "/placeholder.svg"}
                  alt={listing.title}
                  className="w-full h-48 object-cover rounded-t-lg"
                />
                <Badge variant="secondary" className="absolute top-3 left-3 bg-background/90">
                  {listing.type}
                </Badge>
                <Button
                  variant="secondary"
                  size="sm"
                  className="absolute top-3 right-3 h-8 w-8 p-0 bg-background/90 hover:bg-destructive hover:text-destructive-foreground"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>

              <CardContent className="p-4">
                <Link href={`/anuncio/${listing.id}`}>
                  <h3 className="font-semibold text-lg mb-2 group-hover:text-accent transition-colors line-clamp-2">
                    {listing.title}
                  </h3>
                </Link>

                <div className="text-2xl font-bold text-primary mb-3">{listing.price}</div>

                <div className="space-y-2 text-sm text-muted-foreground mb-4">
                  <div className="flex items-center">
                    <MapPin className="h-4 w-4 mr-2 flex-shrink-0" />
                    <span className="truncate">{listing.location}</span>
                  </div>
                  <div className="flex items-center">
                    <Calendar className="h-4 w-4 mr-2 flex-shrink-0" />
                    <span>
                      {listing.year} • {listing.mileage}
                    </span>
                  </div>
                  <div className="flex items-center">
                    <Fuel className="h-4 w-4 mr-2 flex-shrink-0" />
                    <span>{listing.fuel}</span>
                  </div>
                </div>

                <div className="text-xs text-muted-foreground mb-3">
                  Vendedor: {listing.seller} • Salvo em {new Date(listing.addedAt).toLocaleDateString("pt-BR")}
                </div>

                <div className="flex space-x-2">
                  <Button asChild className="flex-1 bg-transparent" variant="outline">
                    <Link href={`/anuncio/${listing.id}`}>Ver detalhes</Link>
                  </Button>
                  <Button className="flex-1">Contatar</Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  )
}
