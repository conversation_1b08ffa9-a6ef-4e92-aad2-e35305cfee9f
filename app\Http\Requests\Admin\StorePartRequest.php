<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;

class StorePartRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // A autorização será tratada pelo middleware de autenticação
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:255',
            'slug' => 'required|string|max:255|unique:parts,slug',
            'part_number' => 'nullable|string|max:100',
            'description' => 'required|string',
            'category_id' => 'required|exists:categories,id',
            'brand_id' => 'required|exists:brands,id',
            'price' => 'required|numeric|min:0',
            'promotional_price' => 'nullable|numeric|lt:price',
            'stock_quantity' => 'required|integer|min:0',
            'weight' => 'nullable|numeric|min:0',
            'dimensions' => 'nullable|string|max:50',
            'is_original' => 'boolean',
            'is_featured' => 'boolean',
            'status' => 'required|in:draft,published,sold,unavailable',
            'seo_title' => 'nullable|string|max:60',
            'seo_description' => 'nullable|string|max:160',
            'seo_keywords' => 'nullable|string|max:255',
            'images.*' => 'nullable|image|mimes:jpeg,png,jpg,webp|max:5120', // 5MB max
            'featured_image' => 'nullable|image|mimes:jpeg,png,jpg,webp|max:5120',
            'compatible_vehicles' => 'nullable|array',
            'compatible_vehicles.*' => 'exists:vehicles,id',
        ];
    }
    
    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'promotional_price.lt' => 'O preço promocional deve ser menor que o preço normal.',
            'images.*.max' => 'Cada imagem não pode ser maior que 5MB.',
            'featured_image.max' => 'A imagem de destaque não pode ser maior que 5MB.',
            'compatible_vehicles.*.exists' => 'Um ou mais veículos selecionados são inválidos.',
        ];
    }
}
