import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select';
import { Head, Link, router } from '@inertiajs/react';
import {
    Bell,
    Check,
    CheckCircle,
    Clock,
    DollarSign,
    Filter,
    Heart,
    MessageCircle,
    Settings,
    Trash2,
    XCircle,
} from 'lucide-react';
import { useState } from 'react';

interface Notification {
    id: string;
    type: string;
    data: any;
    read_at?: string;
    created_at: string;
    formatted_time: string;
    title: string;
    message: string;
    icon: string;
    url: string;
}

interface PaginatedNotifications {
    data: Notification[];
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
    from: number;
    to: number;
}

interface Props {
    notifications: PaginatedNotifications;
    unreadCount: number;
    notificationTypes: Record<string, string>;
    filters: {
        filter?: string;
        type?: string;
    };
}

export default function NotificationsIndex({
    notifications,
    unreadCount,
    notificationTypes,
    filters,
}: Props) {
    const [selectedNotifications, setSelectedNotifications] = useState<
        string[]
    >([]);

    const handleMarkAsRead = (id: string) => {
        router.post(
            `/minha-conta/notificacoes/${id}/marcar-lida`,
            {},
            {
                preserveScroll: true,
            },
        );
    };

    const handleMarkAllAsRead = () => {
        router.post(
            '/minha-conta/notificacoes/marcar-todas-lidas',
            {},
            {
                preserveScroll: true,
            },
        );
    };

    const handleDelete = (id: string) => {
        if (confirm('Tem certeza que deseja excluir esta notificação?')) {
            router.delete(`/minha-conta/notificacoes/${id}`, {
                preserveScroll: true,
            });
        }
    };

    const handleDeleteAllRead = () => {
        if (
            confirm(
                'Tem certeza que deseja excluir todas as notificações lidas?',
            )
        ) {
            router.delete('/minha-conta/notificacoes/excluir-lidas', {
                preserveScroll: true,
            });
        }
    };

    const handleFilterChange = (key: string, value: string) => {
        router.get(
            '/minha-conta/notificacoes',
            { ...filters, [key]: value },
            { preserveState: true },
        );
    };

    const getIcon = (iconName: string) => {
        const icons = {
            MessageCircle,
            DollarSign,
            Clock,
            CheckCircle,
            XCircle,
            Heart,
            Bell,
        };

        const IconComponent = icons[iconName as keyof typeof icons] || Bell;
        return <IconComponent className="h-5 w-5" />;
    };

    const getNotificationColor = (type: string, isRead: boolean) => {
        if (isRead) return 'text-muted-foreground';

        switch (type) {
            case 'App\\Notifications\\NewMessage':
                return 'text-blue-600';
            case 'App\\Notifications\\NewOffer':
                return 'text-green-600';
            case 'App\\Notifications\\AdvertisementExpired':
                return 'text-orange-600';
            case 'App\\Notifications\\AdvertisementApproved':
                return 'text-green-600';
            case 'App\\Notifications\\AdvertisementRejected':
                return 'text-red-600';
            case 'App\\Notifications\\FavoriteAdvertisementUpdated':
                return 'text-pink-600';
            default:
                return 'text-gray-600';
        }
    };

    return (
        <>
            <Head title="Notificações" />

            <div className="space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold tracking-tight">
                            Notificações
                        </h1>
                        <p className="text-muted-foreground">
                            Gerencie suas notificações e preferências
                        </p>
                    </div>
                    <div className="flex items-center gap-2">
                        {unreadCount > 0 && (
                            <Button onClick={handleMarkAllAsRead}>
                                <Check className="mr-2 h-4 w-4" />
                                Marcar todas como lidas
                            </Button>
                        )}
                        <Button variant="outline" onClick={handleDeleteAllRead}>
                            <Trash2 className="mr-2 h-4 w-4" />
                            Excluir lidas
                        </Button>
                        <Button variant="outline" asChild>
                            <Link href="/minha-conta/notificacoes/configuracoes">
                                <Settings className="mr-2 h-4 w-4" />
                                Configurações
                            </Link>
                        </Button>
                    </div>
                </div>

                {/* Stats */}
                <div className="grid gap-4 md:grid-cols-3">
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">
                                Total
                            </CardTitle>
                            <Bell className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">
                                {notifications.total}
                            </div>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">
                                Não Lidas
                            </CardTitle>
                            <Bell className="h-4 w-4 text-orange-600" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-orange-600">
                                {unreadCount}
                            </div>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">
                                Lidas
                            </CardTitle>
                            <Check className="h-4 w-4 text-green-600" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-green-600">
                                {notifications.total - unreadCount}
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Filters */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <Filter className="h-5 w-5" />
                            Filtros
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="grid gap-4 md:grid-cols-2">
                            <Select
                                value={filters.filter || ''}
                                onValueChange={(value) =>
                                    handleFilterChange('filter', value)
                                }
                            >
                                <SelectTrigger>
                                    <SelectValue placeholder="Filtrar por status" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">Todas</SelectItem>
                                    <SelectItem value="unread">
                                        Não lidas
                                    </SelectItem>
                                    <SelectItem value="read">Lidas</SelectItem>
                                </SelectContent>
                            </Select>

                            <Select
                                value={filters.type || ''}
                                onValueChange={(value) =>
                                    handleFilterChange('type', value)
                                }
                            >
                                <SelectTrigger>
                                    <SelectValue placeholder="Filtrar por tipo" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">
                                        Todos os tipos
                                    </SelectItem>
                                    {Object.entries(notificationTypes).map(
                                        ([key, label]) => (
                                            <SelectItem key={key} value={key}>
                                                {label}
                                            </SelectItem>
                                        ),
                                    )}
                                </SelectContent>
                            </Select>
                        </div>
                    </CardContent>
                </Card>

                {/* Notifications List */}
                <Card>
                    <CardHeader>
                        <CardTitle>
                            Notificações ({notifications.from} -{' '}
                            {notifications.to} de {notifications.total})
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        {notifications.data.length === 0 ? (
                            <div className="py-12 text-center">
                                <Bell className="mx-auto mb-4 h-12 w-12 text-muted-foreground" />
                                <h3 className="mb-2 text-lg font-medium">
                                    Nenhuma notificação
                                </h3>
                                <p className="text-muted-foreground">
                                    Você não tem notificações no momento.
                                </p>
                            </div>
                        ) : (
                            <div className="space-y-4">
                                {notifications.data.map((notification) => (
                                    <div
                                        key={notification.id}
                                        className={`flex items-start gap-4 rounded-lg border p-4 transition-colors ${
                                            !notification.read_at
                                                ? 'border-primary/20 bg-muted/50'
                                                : 'hover:bg-muted/30'
                                        }`}
                                    >
                                        <div
                                            className={`rounded-full p-2 ${
                                                !notification.read_at
                                                    ? 'bg-primary/10'
                                                    : 'bg-muted'
                                            }`}
                                        >
                                            <div
                                                className={getNotificationColor(
                                                    notification.type,
                                                    !!notification.read_at,
                                                )}
                                            >
                                                {getIcon(notification.icon)}
                                            </div>
                                        </div>

                                        <div className="min-w-0 flex-1">
                                            <div className="mb-1 flex items-start justify-between">
                                                <h4 className="font-medium">
                                                    {notification.title}
                                                </h4>
                                                <div className="flex items-center gap-2">
                                                    {!notification.read_at && (
                                                        <Badge
                                                            variant="default"
                                                            className="text-xs"
                                                        >
                                                            Nova
                                                        </Badge>
                                                    )}
                                                    <span className="text-xs text-muted-foreground">
                                                        {
                                                            notification.formatted_time
                                                        }
                                                    </span>
                                                </div>
                                            </div>
                                            <p className="mb-3 text-sm text-muted-foreground">
                                                {notification.message}
                                            </p>
                                            <div className="flex items-center gap-2">
                                                <Button
                                                    size="sm"
                                                    variant="outline"
                                                    asChild
                                                >
                                                    <Link
                                                        href={notification.url}
                                                    >
                                                        Ver
                                                    </Link>
                                                </Button>
                                                {!notification.read_at && (
                                                    <Button
                                                        size="sm"
                                                        variant="outline"
                                                        onClick={() =>
                                                            handleMarkAsRead(
                                                                notification.id,
                                                            )
                                                        }
                                                    >
                                                        <Check className="mr-1 h-4 w-4" />
                                                        Marcar como lida
                                                    </Button>
                                                )}
                                                <Button
                                                    size="sm"
                                                    variant="outline"
                                                    onClick={() =>
                                                        handleDelete(
                                                            notification.id,
                                                        )
                                                    }
                                                    className="text-red-600 hover:text-red-700"
                                                >
                                                    <Trash2 className="h-4 w-4" />
                                                </Button>
                                            </div>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        )}

                        {/* Pagination */}
                        {notifications.last_page > 1 && (
                            <div className="mt-6 flex items-center justify-center gap-2">
                                {notifications.current_page > 1 && (
                                    <Button
                                        variant="outline"
                                        onClick={() =>
                                            router.get(
                                                '/minha-conta/notificacoes',
                                                {
                                                    ...filters,
                                                    page:
                                                        notifications.current_page -
                                                        1,
                                                },
                                            )
                                        }
                                    >
                                        Anterior
                                    </Button>
                                )}

                                <span className="text-sm text-muted-foreground">
                                    Página {notifications.current_page} de{' '}
                                    {notifications.last_page}
                                </span>

                                {notifications.current_page <
                                    notifications.last_page && (
                                    <Button
                                        variant="outline"
                                        onClick={() =>
                                            router.get(
                                                '/minha-conta/notificacoes',
                                                {
                                                    ...filters,
                                                    page:
                                                        notifications.current_page +
                                                        1,
                                                },
                                            )
                                        }
                                    >
                                        Próxima
                                    </Button>
                                )}
                            </div>
                        )}
                    </CardContent>
                </Card>
            </div>
        </>
    );
}
