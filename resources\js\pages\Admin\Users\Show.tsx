import { Head, <PERSON>, router } from '@inertiajs/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs';
import { 
    Table, 
    TableBody, 
    TableCell, 
    TableHead, 
    TableHeader, 
    TableRow 
} from '@/components/ui/table';
import { 
    User as UserIcon, 
    Mail, 
    Phone, 
    Calendar, 
    Building, 
    Globe, 
    Edit,
    UserCheck,
    UserX,
    Trash2,
    Car,
    Settings,
    ShoppingBag,
    Eye,
    ArrowLeft
} from 'lucide-react';
import { AvatarImage } from '@/components/ui/responsive-image';

interface User {
    id: number;
    name: string;
    email: string;
    phone?: string;
    cpf_cnpj?: string;
    type: 'individual' | 'company';
    status: 'active' | 'inactive' | 'suspended';
    birth_date?: string;
    company_name?: string;
    trading_name?: string;
    website?: string;
    bio?: string;
    avatar?: string;
    created_at: string;
    email_verified_at?: string;
    last_login_at?: string;
    vehicles: any[];
    parts: any[];
    advertisements: any[];
}

interface Stats {
    total_vehicles: number;
    active_vehicles: number;
    total_parts: number;
    active_parts: number;
    total_advertisements: number;
    active_advertisements: number;
    total_views: number;
    total_messages: number;
}

interface Props {
    user: User;
    stats: Stats;
}

export default function UserShow({ user, stats }: Props) {
    const handleAction = (action: string) => {
        const actions = {
            activate: () => router.post(`/admin/usuarios/${user.id}/ativar`),
            suspend: () => router.post(`/admin/usuarios/${user.id}/suspender`),
            verifyEmail: () => router.post(`/admin/usuarios/${user.id}/verificar-email`),
            delete: () => {
                if (confirm('Tem certeza que deseja excluir este usuário?')) {
                    router.delete(`/admin/usuarios/${user.id}`);
                }
            },
        };

        actions[action as keyof typeof actions]?.();
    };

    const getStatusBadge = (status: string) => {
        const statusConfig = {
            active: { label: 'Ativo', variant: 'default' as const },
            inactive: { label: 'Inativo', variant: 'secondary' as const },
            suspended: { label: 'Suspenso', variant: 'destructive' as const },
        };

        const config = statusConfig[status as keyof typeof statusConfig] || { label: status, variant: 'secondary' as const };
        return <Badge variant={config.variant}>{config.label}</Badge>;
    };

    const getTypeBadge = (type: string) => {
        const typeConfig = {
            individual: { label: 'Pessoa Física', variant: 'outline' as const },
            company: { label: 'Empresa', variant: 'secondary' as const },
        };

        const config = typeConfig[type as keyof typeof typeConfig] || { label: type, variant: 'outline' as const };
        return <Badge variant={config.variant}>{config.label}</Badge>;
    };

    const statCards = [
        {
            title: 'Veículos',
            value: stats.total_vehicles,
            description: `${stats.active_vehicles} ativos`,
            icon: Car,
            color: 'text-blue-600',
            bgColor: 'bg-blue-50',
        },
        {
            title: 'Peças',
            value: stats.total_parts,
            description: `${stats.active_parts} ativas`,
            icon: Settings,
            color: 'text-green-600',
            bgColor: 'bg-green-50',
        },
        {
            title: 'Anúncios',
            value: stats.total_advertisements,
            description: `${stats.active_advertisements} ativos`,
            icon: ShoppingBag,
            color: 'text-purple-600',
            bgColor: 'bg-purple-50',
        },
        {
            title: 'Visualizações',
            value: stats.total_views,
            description: 'Total de visualizações',
            icon: Eye,
            color: 'text-orange-600',
            bgColor: 'bg-orange-50',
        },
    ];

    return (
        <>
            <Head title={`Usuário: ${user.name}`} />
            
            <div className="space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                        <Button variant="outline" size="sm" asChild>
                            <Link href="/admin/usuarios">
                                <ArrowLeft className="h-4 w-4 mr-2" />
                                Voltar
                            </Link>
                        </Button>
                        <div>
                            <h1 className="text-3xl font-bold tracking-tight">{user.name}</h1>
                            <p className="text-muted-foreground">
                                Detalhes do usuário e atividades
                            </p>
                        </div>
                    </div>
                    <div className="flex items-center gap-2">
                        <Button variant="outline" asChild>
                            <Link href={`/admin/usuarios/${user.id}/editar`}>
                                <Edit className="h-4 w-4 mr-2" />
                                Editar
                            </Link>
                        </Button>
                        {user.status === 'active' ? (
                            <Button 
                                variant="outline"
                                onClick={() => handleAction('suspend')}
                                className="text-orange-600"
                            >
                                <UserX className="h-4 w-4 mr-2" />
                                Suspender
                            </Button>
                        ) : (
                            <Button 
                                variant="outline"
                                onClick={() => handleAction('activate')}
                                className="text-green-600"
                            >
                                <UserCheck className="h-4 w-4 mr-2" />
                                Ativar
                            </Button>
                        )}
                        <Button 
                            variant="destructive"
                            onClick={() => handleAction('delete')}
                        >
                            <Trash2 className="h-4 w-4 mr-2" />
                            Excluir
                        </Button>
                    </div>
                </div>

                {/* User Info and Stats */}
                <div className="grid gap-6 md:grid-cols-3">
                    <Card className="md:col-span-2">
                        <CardHeader>
                            <CardTitle>Informações do Usuário</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-6">
                            {/* Avatar and Basic Info */}
                            <div className="flex items-start gap-4">
                                <AvatarImage
                                    src={user.avatar}
                                    alt={user.name}
                                    size="large"
                                />
                                <div className="flex-1">
                                    <div className="flex items-center gap-2 mb-2">
                                        <h2 className="text-xl font-semibold">{user.name}</h2>
                                        {getTypeBadge(user.type)}
                                        {getStatusBadge(user.status)}
                                        {user.email_verified_at && (
                                            <Badge variant="default">Email Verificado</Badge>
                                        )}
                                    </div>
                                    {user.bio && (
                                        <p className="text-muted-foreground">{user.bio}</p>
                                    )}
                                </div>
                            </div>

                            {/* Contact Information */}
                            <div className="grid gap-4 md:grid-cols-2">
                                <div className="flex items-center gap-3">
                                    <Mail className="h-5 w-5 text-muted-foreground" />
                                    <div>
                                        <p className="text-sm font-medium">Email</p>
                                        <p className="text-sm text-muted-foreground">{user.email}</p>
                                    </div>
                                </div>

                                {user.phone && (
                                    <div className="flex items-center gap-3">
                                        <Phone className="h-5 w-5 text-muted-foreground" />
                                        <div>
                                            <p className="text-sm font-medium">Telefone</p>
                                            <p className="text-sm text-muted-foreground">{user.phone}</p>
                                        </div>
                                    </div>
                                )}

                                {user.birth_date && (
                                    <div className="flex items-center gap-3">
                                        <Calendar className="h-5 w-5 text-muted-foreground" />
                                        <div>
                                            <p className="text-sm font-medium">Data de Nascimento</p>
                                            <p className="text-sm text-muted-foreground">
                                                {new Date(user.birth_date).toLocaleDateString()}
                                            </p>
                                        </div>
                                    </div>
                                )}

                                {user.cpf_cnpj && (
                                    <div className="flex items-center gap-3">
                                        <UserIcon className="h-5 w-5 text-muted-foreground" />
                                        <div>
                                            <p className="text-sm font-medium">
                                                {user.type === 'company' ? 'CNPJ' : 'CPF'}
                                            </p>
                                            <p className="text-sm text-muted-foreground">{user.cpf_cnpj}</p>
                                        </div>
                                    </div>
                                )}
                            </div>

                            {/* Company Information */}
                            {user.type === 'company' && (
                                <div className="border-t pt-4">
                                    <h3 className="text-lg font-medium mb-4">Informações da Empresa</h3>
                                    <div className="grid gap-4 md:grid-cols-2">
                                        {user.company_name && (
                                            <div className="flex items-center gap-3">
                                                <Building className="h-5 w-5 text-muted-foreground" />
                                                <div>
                                                    <p className="text-sm font-medium">Razão Social</p>
                                                    <p className="text-sm text-muted-foreground">{user.company_name}</p>
                                                </div>
                                            </div>
                                        )}

                                        {user.trading_name && (
                                            <div className="flex items-center gap-3">
                                                <Building className="h-5 w-5 text-muted-foreground" />
                                                <div>
                                                    <p className="text-sm font-medium">Nome Fantasia</p>
                                                    <p className="text-sm text-muted-foreground">{user.trading_name}</p>
                                                </div>
                                            </div>
                                        )}

                                        {user.website && (
                                            <div className="flex items-center gap-3">
                                                <Globe className="h-5 w-5 text-muted-foreground" />
                                                <div>
                                                    <p className="text-sm font-medium">Website</p>
                                                    <a 
                                                        href={user.website} 
                                                        target="_blank" 
                                                        rel="noopener noreferrer"
                                                        className="text-sm text-blue-600 hover:underline"
                                                    >
                                                        {user.website}
                                                    </a>
                                                </div>
                                            </div>
                                        )}
                                    </div>
                                </div>
                            )}

                            {/* Account Info */}
                            <div className="border-t pt-4">
                                <div className="grid gap-4 md:grid-cols-2">
                                    <div className="flex items-center gap-3">
                                        <Calendar className="h-5 w-5 text-muted-foreground" />
                                        <div>
                                            <p className="text-sm font-medium">Membro desde</p>
                                            <p className="text-sm text-muted-foreground">
                                                {new Date(user.created_at).toLocaleDateString()}
                                            </p>
                                        </div>
                                    </div>
                                    {user.last_login_at && (
                                        <div className="flex items-center gap-3">
                                            <Calendar className="h-5 w-5 text-muted-foreground" />
                                            <div>
                                                <p className="text-sm font-medium">Último acesso</p>
                                                <p className="text-sm text-muted-foreground">
                                                    {new Date(user.last_login_at).toLocaleDateString()}
                                                </p>
                                            </div>
                                        </div>
                                    )}
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Stats */}
                    <div className="space-y-4">
                        <Card>
                            <CardHeader>
                                <CardTitle>Estatísticas</CardTitle>
                                <CardDescription>
                                    Atividade do usuário
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                {statCards.map((stat, index) => (
                                    <div key={index} className="flex items-center gap-3">
                                        <div className={`p-2 rounded-full ${stat.bgColor}`}>
                                            <stat.icon className={`h-4 w-4 ${stat.color}`} />
                                        </div>
                                        <div className="flex-1">
                                            <div className="flex items-center justify-between">
                                                <p className="text-sm font-medium">{stat.title}</p>
                                                <p className="text-lg font-bold">{stat.value}</p>
                                            </div>
                                            <p className="text-xs text-muted-foreground">{stat.description}</p>
                                        </div>
                                    </div>
                                ))}
                            </CardContent>
                        </Card>
                    </div>
                </div>

                {/* Activity Tabs */}
                <Tabs defaultValue="vehicles" className="space-y-4">
                    <TabsList>
                        <TabsTrigger value="vehicles">Veículos ({user.vehicles.length})</TabsTrigger>
                        <TabsTrigger value="parts">Peças ({user.parts.length})</TabsTrigger>
                        <TabsTrigger value="advertisements">Anúncios ({user.advertisements.length})</TabsTrigger>
                    </TabsList>

                    <TabsContent value="vehicles">
                        <Card>
                            <CardHeader>
                                <CardTitle>Veículos do Usuário</CardTitle>
                            </CardHeader>
                            <CardContent>
                                {user.vehicles.length > 0 ? (
                                    <Table>
                                        <TableHeader>
                                            <TableRow>
                                                <TableHead>Veículo</TableHead>
                                                <TableHead>Status</TableHead>
                                                <TableHead>Visualizações</TableHead>
                                                <TableHead>Criado em</TableHead>
                                                <TableHead>Ações</TableHead>
                                            </TableRow>
                                        </TableHeader>
                                        <TableBody>
                                            {user.vehicles.slice(0, 5).map((vehicle: any) => (
                                                <TableRow key={vehicle.id}>
                                                    <TableCell>
                                                        <div>
                                                            <p className="font-medium">{vehicle.title}</p>
                                                            <p className="text-sm text-muted-foreground">
                                                                {vehicle.brand?.name} {vehicle.model}
                                                            </p>
                                                        </div>
                                                    </TableCell>
                                                    <TableCell>
                                                        <Badge variant={vehicle.status === 'published' ? 'default' : 'secondary'}>
                                                            {vehicle.status}
                                                        </Badge>
                                                    </TableCell>
                                                    <TableCell>{vehicle.views}</TableCell>
                                                    <TableCell>
                                                        {new Date(vehicle.created_at).toLocaleDateString()}
                                                    </TableCell>
                                                    <TableCell>
                                                        <Button variant="outline" size="sm" asChild>
                                                            <Link href={`/admin/veiculos/${vehicle.id}`}>
                                                                Ver
                                                            </Link>
                                                        </Button>
                                                    </TableCell>
                                                </TableRow>
                                            ))}
                                        </TableBody>
                                    </Table>
                                ) : (
                                    <p className="text-center text-muted-foreground py-8">
                                        Nenhum veículo encontrado
                                    </p>
                                )}
                            </CardContent>
                        </Card>
                    </TabsContent>

                    <TabsContent value="parts">
                        <Card>
                            <CardHeader>
                                <CardTitle>Peças do Usuário</CardTitle>
                            </CardHeader>
                            <CardContent>
                                {user.parts.length > 0 ? (
                                    <Table>
                                        <TableHeader>
                                            <TableRow>
                                                <TableHead>Peça</TableHead>
                                                <TableHead>Status</TableHead>
                                                <TableHead>Preço</TableHead>
                                                <TableHead>Visualizações</TableHead>
                                                <TableHead>Criado em</TableHead>
                                                <TableHead>Ações</TableHead>
                                            </TableRow>
                                        </TableHeader>
                                        <TableBody>
                                            {user.parts.slice(0, 5).map((part: any) => (
                                                <TableRow key={part.id}>
                                                    <TableCell>
                                                        <div>
                                                            <p className="font-medium">{part.name}</p>
                                                            <p className="text-sm text-muted-foreground">
                                                                {part.category?.name}
                                                            </p>
                                                        </div>
                                                    </TableCell>
                                                    <TableCell>
                                                        <Badge variant={part.status === 'active' ? 'default' : 'secondary'}>
                                                            {part.status}
                                                        </Badge>
                                                    </TableCell>
                                                    <TableCell>
                                                        R$ {part.price?.toLocaleString()}
                                                    </TableCell>
                                                    <TableCell>{part.views}</TableCell>
                                                    <TableCell>
                                                        {new Date(part.created_at).toLocaleDateString()}
                                                    </TableCell>
                                                    <TableCell>
                                                        <Button variant="outline" size="sm" asChild>
                                                            <Link href={`/admin/pecas/${part.id}`}>
                                                                Ver
                                                            </Link>
                                                        </Button>
                                                    </TableCell>
                                                </TableRow>
                                            ))}
                                        </TableBody>
                                    </Table>
                                ) : (
                                    <p className="text-center text-muted-foreground py-8">
                                        Nenhuma peça encontrada
                                    </p>
                                )}
                            </CardContent>
                        </Card>
                    </TabsContent>

                    <TabsContent value="advertisements">
                        <Card>
                            <CardHeader>
                                <CardTitle>Anúncios do Usuário</CardTitle>
                            </CardHeader>
                            <CardContent>
                                {user.advertisements.length > 0 ? (
                                    <Table>
                                        <TableHeader>
                                            <TableRow>
                                                <TableHead>Anúncio</TableHead>
                                                <TableHead>Tipo</TableHead>
                                                <TableHead>Status</TableHead>
                                                <TableHead>Visualizações</TableHead>
                                                <TableHead>Criado em</TableHead>
                                                <TableHead>Ações</TableHead>
                                            </TableRow>
                                        </TableHeader>
                                        <TableBody>
                                            {user.advertisements.slice(0, 5).map((ad: any) => (
                                                <TableRow key={ad.id}>
                                                    <TableCell>
                                                        <div>
                                                            <p className="font-medium">{ad.title}</p>
                                                            <p className="text-sm text-muted-foreground">
                                                                R$ {ad.price?.toLocaleString()}
                                                            </p>
                                                        </div>
                                                    </TableCell>
                                                    <TableCell>
                                                        <Badge variant="outline">
                                                            {ad.type}
                                                        </Badge>
                                                    </TableCell>
                                                    <TableCell>
                                                        <Badge variant={ad.status === 'published' ? 'default' : 'secondary'}>
                                                            {ad.status}
                                                        </Badge>
                                                    </TableCell>
                                                    <TableCell>{ad.views}</TableCell>
                                                    <TableCell>
                                                        {new Date(ad.created_at).toLocaleDateString()}
                                                    </TableCell>
                                                    <TableCell>
                                                        <Button variant="outline" size="sm" asChild>
                                                            <Link href={`/admin/anuncios/${ad.id}`}>
                                                                Ver
                                                            </Link>
                                                        </Button>
                                                    </TableCell>
                                                </TableRow>
                                            ))}
                                        </TableBody>
                                    </Table>
                                ) : (
                                    <p className="text-center text-muted-foreground py-8">
                                        Nenhum anúncio encontrado
                                    </p>
                                )}
                            </CardContent>
                        </Card>
                    </TabsContent>
                </Tabs>
            </div>
        </>
    );
}
