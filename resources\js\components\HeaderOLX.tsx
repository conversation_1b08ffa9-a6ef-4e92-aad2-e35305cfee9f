import CategoryNavigation from '@/components/CategoryNavigation';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuGroup,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { Category } from '@/types';
import { Link, router } from '@inertiajs/react';
import {
    Bell,
    ChevronDown,
    CreditCard,
    Heart,
    LogOut,
    Menu,
    MessageCircle,
    Plus,
    Search,
    Settings,
    ShoppingBag,
} from 'lucide-react';
import { useState } from 'react';
import { SiLaravel } from 'react-icons/si';

interface HeaderProps {
    categories?: Category[];
    auth?: {
        user?: {
            id: number;
            name: string;
            email: string;
            avatar?: string;
        };
    };
}

// Helper function to get user initials
const getUserInitials = (name: string) => {
    return name
        .split(' ')
        .map((word) => word.charAt(0))
        .join('')
        .toUpperCase()
        .slice(0, 2);
};

export default function MainHeader({ categories = [], auth }: HeaderProps) {
    const [isMenuOpen, setIsMenuOpen] = useState(false);
    const [searchQuery, setSearchQuery] = useState('');
    const [notificationCount] = useState(0);
    const isAuthenticated = !!auth?.user;

    const handleSearch = () => {
        if (searchQuery.trim()) {
            router.get('/pesquisar', { search: searchQuery });
            setSearchQuery('');
        }
    };

    const handleLogout = () => {
        router.post('/logout');
    };

    return (
        <header className="w-full bg-white shadow-sm">
            {/* Main Header */}
            <div className="border-b border-gray-200">
                <div className="container mx-auto px-4">
                    <div className="flex h-16 items-center justify-between">
                        {/* Logo */}
                        <div className="flex items-center space-x-8">
                            <Link
                                href="/"
                                className="flex items-center space-x-2"
                            >
                                <SiLaravel className="h-8 w-8 text-red-500" />
                                <span className="text-xl font-bold text-gray-800">
                                    Marketplace
                                </span>
                            </Link>
                        </div>

                        {/* Search Bar - Desktop */}
                        <div className="mx-8 hidden max-w-2xl flex-1 md:flex">
                            <div className="relative flex-1">
                                <Input
                                    type="text"
                                    placeholder="Buscar 'Aparta'"
                                    value={searchQuery}
                                    onChange={(e) =>
                                        setSearchQuery(e.target.value)
                                    }
                                    onKeyDown={(e) => {
                                        if (e.key === 'Enter') {
                                            handleSearch();
                                        }
                                    }}
                                    className="h-12 rounded-l-md rounded-r-none border-gray-300 pr-12 pl-4 focus:border-primary focus:ring-primary"
                                />
                                <Button
                                    size="sm"
                                    onClick={handleSearch}
                                    className="absolute top-0 right-0 h-12 rounded-l-none bg-primary px-6 hover:bg-primary/90"
                                >
                                    <Search className="h-5 w-5" />
                                </Button>
                            </div>
                        </div>

                        {/* Actions */}
                        <div className="hidden items-center space-x-4 md:flex">
                            {isAuthenticated ? (
                                <>
                                    <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={() => router.get('/chat')}
                                        className="text-gray-600 hover:text-primary"
                                    >
                                        <MessageCircle className="h-5 w-5" />
                                    </Button>
                                    <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={() =>
                                            router.get('/notificacoes')
                                        }
                                        className="relative text-gray-600 hover:text-primary"
                                    >
                                        <Bell className="h-5 w-5" />
                                        {notificationCount > 0 && (
                                            <Badge className="absolute -top-1 -right-1 flex h-5 w-5 items-center justify-center rounded-full bg-red-500 p-0 text-xs text-white">
                                                {notificationCount}
                                            </Badge>
                                        )}
                                    </Button>

                                    {/* User Dropdown Menu */}
                                    <DropdownMenu>
                                        <DropdownMenuTrigger asChild>
                                            <Button
                                                variant="ghost"
                                                className="flex items-center space-x-2 text-gray-600 hover:text-primary"
                                            >
                                                <Avatar className="h-8 w-8">
                                                    <AvatarImage
                                                        src={auth.user?.avatar}
                                                        alt={auth.user?.name}
                                                    />
                                                    <AvatarFallback className="bg-primary text-white">
                                                        {auth.user?.name
                                                            ? getUserInitials(
                                                                  auth.user
                                                                      .name,
                                                              )
                                                            : 'U'}
                                                    </AvatarFallback>
                                                </Avatar>
                                                <ChevronDown className="h-4 w-4" />
                                            </Button>
                                        </DropdownMenuTrigger>
                                        <DropdownMenuContent
                                            className="w-56"
                                            align="end"
                                        >
                                            <DropdownMenuLabel className="font-normal">
                                                <div className="flex flex-col space-y-1">
                                                    <p className="text-sm leading-none font-medium">
                                                        {auth.user?.name}
                                                    </p>
                                                    <p className="text-xs leading-none text-muted-foreground">
                                                        {auth.user?.email}
                                                    </p>
                                                </div>
                                            </DropdownMenuLabel>
                                            <DropdownMenuSeparator />

                                            <DropdownMenuGroup>
                                                <DropdownMenuItem asChild>
                                                    <Link
                                                        href="/minha-conta/anuncios"
                                                        className="cursor-pointer"
                                                    >
                                                        <ShoppingBag className="mr-2 h-4 w-4" />
                                                        <span>
                                                            Meus anúncios
                                                        </span>
                                                    </Link>
                                                </DropdownMenuItem>
                                                <DropdownMenuItem asChild>
                                                    <Link
                                                        href="/minha-conta/favoritos"
                                                        className="cursor-pointer"
                                                    >
                                                        <Heart className="mr-2 h-4 w-4" />
                                                        <span>Favoritos</span>
                                                    </Link>
                                                </DropdownMenuItem>
                                            </DropdownMenuGroup>

                                            <DropdownMenuSeparator />

                                            <DropdownMenuGroup>
                                                <DropdownMenuItem asChild>
                                                    <Link
                                                        href="/minha-conta/perfil"
                                                        className="cursor-pointer"
                                                    >
                                                        <Settings className="mr-2 h-4 w-4" />
                                                        <span>Minha conta</span>
                                                    </Link>
                                                </DropdownMenuItem>
                                                <DropdownMenuItem asChild>
                                                    <Link
                                                        href="/minha-conta/configuracoes"
                                                        className="cursor-pointer"
                                                    >
                                                        <CreditCard className="mr-2 h-4 w-4" />
                                                        <span>
                                                            Configurações
                                                        </span>
                                                    </Link>
                                                </DropdownMenuItem>
                                            </DropdownMenuGroup>

                                            <DropdownMenuSeparator />

                                            <DropdownMenuItem
                                                onClick={handleLogout}
                                                className="cursor-pointer text-red-600 focus:text-red-600"
                                            >
                                                <LogOut className="mr-2 h-4 w-4" />
                                                <span>Sair</span>
                                            </DropdownMenuItem>
                                        </DropdownMenuContent>
                                    </DropdownMenu>

                                    <Button
                                        onClick={() => router.get('/anunciar')}
                                        className="bg-primary px-6 text-white hover:bg-primary/90"
                                    >
                                        <Plus className="mr-2 h-4 w-4" />
                                        Anunciar grátis
                                    </Button>
                                </>
                            ) : (
                                <>
                                    <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={() => router.get('/login')}
                                        className="text-gray-600 hover:text-primary"
                                    >
                                        Entrar
                                    </Button>
                                    <Button
                                        onClick={() => router.get('/register')}
                                        className="bg-primary px-6 text-white hover:bg-primary/90"
                                    >
                                        Cadastrar
                                    </Button>
                                </>
                            )}
                        </div>

                        {/* Mobile menu button */}
                        <div className="flex items-center md:hidden">
                            <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => setIsMenuOpen(!isMenuOpen)}
                            >
                                <Menu className="h-6 w-6" />
                                <span className="sr-only">Abrir menu</span>
                            </Button>
                        </div>
                    </div>

                    {/* Mobile Search */}
                    <div className="py-3 md:hidden">
                        <div className="relative">
                            <Input
                                type="text"
                                placeholder="Buscar 'Aparta'"
                                value={searchQuery}
                                onChange={(e) => setSearchQuery(e.target.value)}
                                onKeyDown={(e) => {
                                    if (e.key === 'Enter') {
                                        handleSearch();
                                    }
                                }}
                                className="h-10 border-gray-300 pr-12 pl-4 focus:border-primary focus:ring-primary"
                            />
                            <Button
                                size="sm"
                                onClick={handleSearch}
                                className="absolute top-1 right-1 h-8 bg-primary px-3 hover:bg-primary/90"
                            >
                                <Search className="h-4 w-4" />
                            </Button>
                        </div>
                    </div>
                </div>
            </div>

            {/* Category Navigation */}
            <CategoryNavigation categories={categories} />

            {/* Mobile Navigation Menu */}
            {isMenuOpen && (
                <div className="border-t border-gray-100 bg-white py-4 md:hidden">
                    <div className="container mx-auto px-4">
                        <nav className="flex flex-col space-y-2">
                            {isAuthenticated ? (
                                <>
                                    <Button
                                        variant="ghost"
                                        onClick={() =>
                                            router.get('/minha-conta/anuncios')
                                        }
                                        className="justify-start text-gray-600"
                                    >
                                        Meus Anúncios
                                    </Button>
                                    <Button
                                        variant="ghost"
                                        onClick={() => router.get('/chat')}
                                        className="justify-start text-gray-600"
                                    >
                                        Chat
                                    </Button>
                                    <Button
                                        variant="ghost"
                                        onClick={() =>
                                            router.get('/notificacoes')
                                        }
                                        className="justify-start text-gray-600"
                                    >
                                        Notificações
                                    </Button>
                                    <Button
                                        variant="ghost"
                                        onClick={() =>
                                            router.get('/minha-conta/favoritos')
                                        }
                                        className="justify-start text-gray-600"
                                    >
                                        Favoritos
                                    </Button>
                                    <Button
                                        variant="ghost"
                                        onClick={() =>
                                            router.get('/minha-conta/perfil')
                                        }
                                        className="justify-start text-gray-600"
                                    >
                                        Minha Conta
                                    </Button>
                                    <Button
                                        variant="ghost"
                                        onClick={handleLogout}
                                        className="justify-start text-red-600"
                                    >
                                        Sair
                                    </Button>
                                    <Button
                                        onClick={() => router.get('/anunciar')}
                                        className="mt-4 justify-start bg-primary text-white hover:bg-primary/90"
                                    >
                                        <Plus className="mr-2 h-4 w-4" />
                                        Anunciar grátis
                                    </Button>
                                </>
                            ) : (
                                <>
                                    <Button
                                        variant="ghost"
                                        onClick={() => router.get('/login')}
                                        className="justify-start text-gray-600"
                                    >
                                        Entrar
                                    </Button>
                                    <Button
                                        onClick={() => router.get('/register')}
                                        className="justify-start bg-primary text-white hover:bg-primary/90"
                                    >
                                        Cadastrar
                                    </Button>
                                </>
                            )}
                        </nav>
                    </div>
                </div>
            )}
        </header>
    );
}
