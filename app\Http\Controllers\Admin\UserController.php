<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Illuminate\Validation\Rule;

class UserController extends Controller
{
    /**
     * Display a listing of users.
     */
    public function index(Request $request)
    {
        $query = User::query();

        // Filtros
        if ($request->filled('search')) {
            $search = $request->input('search');
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('cpf_cnpj', 'like', "%{$search}%");
            });
        }

        if ($request->filled('status')) {
            $query->where('status', $request->input('status'));
        }

        if ($request->filled('type')) {
            $query->where('type', $request->input('type'));
        }

        $users = $query->latest()
            ->paginate(15)
            ->withQueryString();

        return Inertia::render('Admin/Users/<USER>', [
            'users' => $users,
            'filters' => $request->all('search', 'status', 'type'),
        ]);
    }

    /**
     * Display the specified user.
     */
    public function show(User $user)
    {
        $user->load(['vehicles', 'parts']);

        $stats = [
            'total_vehicles' => $user->vehicles()->count(),
            'active_vehicles' => $user->vehicles()->where('status', 'published')->count(),
            'total_parts' => $user->parts()->count(),
            'active_parts' => $user->parts()->where('status', 'active')->count(),
        ];

        return Inertia::render('Admin/Users/<USER>', [
            'user' => $user,
            'stats' => $stats,
        ]);
    }

    /**
     * Show the form for editing the specified user.
     */
    public function edit(User $user)
    {
        return Inertia::render('Admin/Users/<USER>', [
            'user' => $user,
        ]);
    }

    /**
     * Update the specified user.
     */
    public function update(Request $request, User $user)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => ['required', 'email', Rule::unique('users')->ignore($user->id)],
            'phone' => 'nullable|string|max:20',
            'cpf_cnpj' => ['nullable', 'string', 'max:20', Rule::unique('users')->ignore($user->id)],
            'type' => 'required|in:individual,company',
            'status' => 'required|in:active,inactive,suspended,pending',
            'birth_date' => 'nullable|date',
        ]);

        $user->update($request->only([
            'name', 'email', 'phone', 'cpf_cnpj', 'type', 'status', 'birth_date'
        ]));

        return redirect()
            ->route('admin.users.show', $user)
            ->with('success', 'Usuário atualizado com sucesso!');
    }

    /**
     * Remove the specified user.
     */
    public function destroy(User $user)
    {
        // Verificar se o usuário tem veículos ou peças ativas
        if ($user->vehicles()->where('status', 'published')->exists() || 
            $user->parts()->where('status', 'active')->exists()) {
            return back()->with('error', 'Não é possível excluir usuário com anúncios ativos.');
        }

        $user->delete();

        return redirect()
            ->route('admin.users.index')
            ->with('success', 'Usuário excluído com sucesso!');
    }

    /**
     * Activate user.
     */
    public function activate(User $user)
    {
        $user->update(['status' => 'active']);

        return back()->with('success', 'Usuário ativado com sucesso!');
    }

    /**
     * Suspend user.
     */
    public function suspend(User $user)
    {
        $user->update(['status' => 'suspended']);

        return back()->with('success', 'Usuário suspenso com sucesso!');
    }

    /**
     * Verify user email.
     */
    public function verifyEmail(User $user)
    {
        $user->update(['email_verified_at' => now()]);

        return back()->with('success', 'E-mail verificado com sucesso!');
    }
}
