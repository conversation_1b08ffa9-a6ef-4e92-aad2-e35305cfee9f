"use client"

import { useState } from "react"
import Link from "next/link"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { User, Car, Heart, MessageSquare, Settings, Star, Shield, Calendar, Plus } from "lucide-react"

const menuItems = [
  { id: "dashboard", label: "Dashboard", icon: User, href: "/perfil" },
  { id: "anuncios", label: "Meus Anúncios", icon: Car, href: "/perfil/anuncios" },
  { id: "favoritos", label: "Favoritos", icon: Heart, href: "/perfil/favoritos" },
  { id: "mensagens", label: "Mensagens", icon: MessageSquare, href: "/perfil/mensagens" },
  { id: "configuracoes", label: "Configurações", icon: Settings, href: "/perfil/configuracoes" },
]

export function UserSidebar() {
  const [activeItem, setActiveItem] = useState("dashboard")

  const userData = {
    name: "João Silva",
    email: "<EMAIL>",
    location: "São Paulo, SP",
    memberSince: "2020",
    rating: 4.8,
    totalSales: 15,
    verified: true,
    activeListings: 3,
  }

  return (
    <div className="space-y-6">
      {/* User Info Card */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center space-x-4 mb-4">
            <Avatar className="h-16 w-16">
              <AvatarImage src="/placeholder.svg" />
              <AvatarFallback className="text-lg">
                {userData.name
                  .split(" ")
                  .map((n) => n[0])
                  .join("")}
              </AvatarFallback>
            </Avatar>
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-1">
                <h3 className="font-semibold text-lg">{userData.name}</h3>
                {userData.verified && (
                  <Badge variant="secondary" className="flex items-center gap-1">
                    <Shield className="h-3 w-3" />
                    Verificado
                  </Badge>
                )}
              </div>
              <div className="flex items-center text-sm text-muted-foreground mb-2">
                <Star className="h-4 w-4 mr-1 fill-accent text-accent" />
                {userData.rating} • {userData.totalSales} vendas
              </div>
              <div className="flex items-center text-sm text-muted-foreground">
                <Calendar className="h-4 w-4 mr-1" />
                Membro desde {userData.memberSince}
              </div>
            </div>
          </div>

          <Button asChild className="w-full mb-3">
            <Link href="/anunciar">
              <Plus className="h-4 w-4 mr-2" />
              Criar Anúncio
            </Link>
          </Button>

          <div className="grid grid-cols-2 gap-4 text-center">
            <div>
              <div className="text-2xl font-bold text-primary">{userData.activeListings}</div>
              <div className="text-xs text-muted-foreground">Anúncios ativos</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-primary">{userData.totalSales}</div>
              <div className="text-xs text-muted-foreground">Vendas realizadas</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Navigation Menu */}
      <Card>
        <CardContent className="p-4">
          <nav className="space-y-1">
            {menuItems.map((item) => {
              const Icon = item.icon
              const isActive = activeItem === item.id

              return (
                <Button
                  key={item.id}
                  variant={isActive ? "secondary" : "ghost"}
                  className="w-full justify-start"
                  asChild
                  onClick={() => setActiveItem(item.id)}
                >
                  <Link href={item.href}>
                    <Icon className="h-4 w-4 mr-3" />
                    {item.label}
                  </Link>
                </Button>
              )
            })}
          </nav>
        </CardContent>
      </Card>
    </div>
  )
}
