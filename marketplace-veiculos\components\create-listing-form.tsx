"use client"

import { useState } from "react"
import { <PERSON>, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { BasicInfoStep } from "@/components/form-steps/basic-info-step"
import { VehicleDetailsStep } from "@/components/form-steps/vehicle-details-step"
import { PhotosStep } from "@/components/form-steps/photos-step"
import { ContactStep } from "@/components/form-steps/contact-step"
import { PreviewStep } from "@/components/form-steps/preview-step"
import { ChevronLeft, ChevronRight } from "lucide-react"

const steps = [
  { id: 1, title: "Informações Básicas", component: BasicInfoStep },
  { id: 2, title: "Detalhes do Veículo", component: VehicleDetailsStep },
  { id: 3, title: "Fotos", component: PhotosStep },
  { id: 4, title: "Contato", component: ContactStep },
  { id: 5, title: "<PERSON><PERSON>ão", component: PreviewStep },
]

export function CreateListingForm() {
  const [currentStep, setCurrentStep] = useState(1)
  const [formData, setFormData] = useState({
    // Basic Info
    category: "",
    type: "",
    brand: "",
    model: "",
    version: "",
    year: "",
    price: "",
    condition: "",

    // Vehicle Details
    mileage: "",
    fuel: "",
    transmission: "",
    color: "",
    doors: "",
    engine: "",
    features: [] as string[],
    description: "",

    // Photos
    photos: [] as File[],

    // Contact
    sellerName: "",
    phone: "",
    whatsapp: "",
    email: "",
    location: "",
    state: "",
    city: "",
  })

  const progress = (currentStep / steps.length) * 100

  const nextStep = () => {
    if (currentStep < steps.length) {
      setCurrentStep(currentStep + 1)
    }
  }

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    }
  }

  const updateFormData = (data: Partial<typeof formData>) => {
    setFormData((prev) => ({ ...prev, ...data }))
  }

  const CurrentStepComponent = steps[currentStep - 1].component

  return (
    <div className="space-y-6">
      {/* Progress */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold">{steps[currentStep - 1].title}</h2>
            <span className="text-sm text-muted-foreground">
              Etapa {currentStep} de {steps.length}
            </span>
          </div>
          <Progress value={progress} className="w-full" />
        </CardContent>
      </Card>

      {/* Step Content */}
      <Card>
        <CardContent className="p-6">
          <CurrentStepComponent
            formData={formData}
            updateFormData={updateFormData}
            onNext={nextStep}
            onPrev={prevStep}
          />
        </CardContent>
      </Card>

      {/* Navigation */}
      <div className="flex justify-between">
        <Button variant="outline" onClick={prevStep} disabled={currentStep === 1} className="bg-transparent">
          <ChevronLeft className="h-4 w-4 mr-2" />
          Anterior
        </Button>

        {currentStep < steps.length ? (
          <Button onClick={nextStep}>
            Próximo
            <ChevronRight className="h-4 w-4 ml-2" />
          </Button>
        ) : (
          <Button className="bg-accent hover:bg-accent/90">Publicar Anúncio</Button>
        )}
      </div>
    </div>
  )
}
