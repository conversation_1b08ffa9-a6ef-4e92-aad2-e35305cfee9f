import { Button } from '@/components/ui/button';
import { Link } from '@inertiajs/react';
import { Facebook, Instagram, Twitter, Youtube } from 'lucide-react';

export default function Footer() {
    return (
        <footer className="bg-primary text-primary-foreground">
            <div className="container mx-auto px-4 py-12">
                <div className="grid grid-cols-1 gap-8 md:grid-cols-3">
                    {/* Brand */}
                    <div>
                        <div className="mb-4 flex items-center space-x-2">
                            <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-primary-foreground">
                                <span className="text-lg font-bold text-primary">
                                    V
                                </span>
                            </div>
                            <span className="text-xl font-bold">
                                VeiculosBR
                            </span>
                        </div>
                        <p className="mb-4 text-primary-foreground/80">
                            A maior plataforma de veículos do Brasil. Compre,
                            venda e alugue com segurança.
                        </p>
                        <div className="flex space-x-4">
                            <Button
                                variant="ghost"
                                size="sm"
                                className="text-primary-foreground hover:bg-primary-foreground/10"
                            >
                                <Facebook className="h-4 w-4" />
                            </Button>
                            <Button
                                variant="ghost"
                                size="sm"
                                className="text-primary-foreground hover:bg-primary-foreground/10"
                            >
                                <Instagram className="h-4 w-4" />
                            </Button>
                            <Button
                                variant="ghost"
                                size="sm"
                                className="text-primary-foreground hover:bg-primary-foreground/10"
                            >
                                <Twitter className="h-4 w-4" />
                            </Button>
                            <Button
                                variant="ghost"
                                size="sm"
                                className="text-primary-foreground hover:bg-primary-foreground/10"
                            >
                                <Youtube className="h-4 w-4" />
                            </Button>
                        </div>
                    </div>

                    {/* Categories */}
                    <div>
                        <h3 className="mb-4 text-lg font-semibold">
                            Categorias
                        </h3>
                        <ul className="space-y-2">
                            <li>
                                <Link
                                    href="/pesquisar?search=carros"
                                    className="text-primary-foreground/80 transition-colors hover:text-primary-foreground"
                                >
                                    Carros
                                </Link>
                            </li>
                            <li>
                                <Link
                                    href="/pesquisar?search=motos"
                                    className="text-primary-foreground/80 transition-colors hover:text-primary-foreground"
                                >
                                    Motos
                                </Link>
                            </li>
                            <li>
                                <Link
                                    href="/pesquisar?search=pecas"
                                    className="text-primary-foreground/80 transition-colors hover:text-primary-foreground"
                                >
                                    Peças
                                </Link>
                            </li>
                            <li>
                                <Link
                                    href="/pesquisar?search=caminhoes"
                                    className="text-primary-foreground/80 transition-colors hover:text-primary-foreground"
                                >
                                    Caminhões
                                </Link>
                            </li>
                            <li>
                                <Link
                                    href="/pesquisar?search=eletricos"
                                    className="text-primary-foreground/80 transition-colors hover:text-primary-foreground"
                                >
                                    Elétricos
                                </Link>
                            </li>
                        </ul>
                    </div>

                    {/* Support */}
                    <div>
                        <h3 className="mb-4 text-lg font-semibold">Suporte</h3>
                        <ul className="space-y-2">
                            <li>
                                <Link
                                    href="/ajuda"
                                    className="text-primary-foreground/80 transition-colors hover:text-primary-foreground"
                                >
                                    Central de Ajuda
                                </Link>
                            </li>
                            <li>
                                <Link
                                    href="/contato"
                                    className="text-primary-foreground/80 transition-colors hover:text-primary-foreground"
                                >
                                    Contato
                                </Link>
                            </li>
                            <li>
                                <Link
                                    href="/seguranca"
                                    className="text-primary-foreground/80 transition-colors hover:text-primary-foreground"
                                >
                                    Dicas de Segurança
                                </Link>
                            </li>
                            <li>
                                <Link
                                    href="/termos"
                                    className="text-primary-foreground/80 transition-colors hover:text-primary-foreground"
                                >
                                    Termos de Uso
                                </Link>
                            </li>
                            <li>
                                <Link
                                    href="/privacidade"
                                    className="text-primary-foreground/80 transition-colors hover:text-primary-foreground"
                                >
                                    Privacidade
                                </Link>
                            </li>
                        </ul>
                    </div>
                </div>

                <div className="mt-8 border-t border-primary-foreground/20 pt-8 text-center">
                    <p className="text-primary-foreground/80">
                        © 2024 VeiculosBR. Todos os direitos reservados.
                    </p>
                </div>
            </div>
        </footer>
    );
}
