<?php

namespace App\Notifications;

use App\Models\Advertisement;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class AdvertisementApprovedNotification extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new notification instance.
     */
    public function __construct(public Advertisement $advertisement)
    {
        //
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable)
    {
        return (new MailMessage)
                    ->subject('Seu anúncio foi aprovado!')
                    ->line('Seu anúncio foi aprovado e já está disponível para visualização.')
                    ->action('Ver Anúncio', route('advertisements.show', $this->advertisement))
                    ->line('Obrigado por usar nossa plataforma!');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'message' => 'Seu anúncio foi aprovado e está ativo.',
            'link' => route('advertisements.show', $this->advertisement),
            'advertisement_id' => $this->advertisement->id,
            'advertisement_title' => $this->advertisement->title,
        ];
    }
}
