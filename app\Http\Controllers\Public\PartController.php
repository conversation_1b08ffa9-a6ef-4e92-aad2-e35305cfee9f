<?php

namespace App\Http\Controllers\Public;

use App\Http\Controllers\Controller;
use App\Models\Part;
use App\Models\Brand;
use App\Models\Category;
use Illuminate\Http\Request;
use Inertia\Inertia;

class PartController extends Controller
{
    /**
     * Display a listing of parts.
     */
    public function index(Request $request)
    {
        $query = Part::with(['brand', 'category', 'user'])
            ->where('status', 'active')
            ->where('stock_quantity', '>', 0);

        // Filtros
        if ($request->filled('search')) {
            $search = $request->input('search');
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('part_number', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        if ($request->filled('category_id')) {
            $query->where('category_id', $request->input('category_id'));
        }

        if ($request->filled('brand_id')) {
            $query->where('brand_id', $request->input('brand_id'));
        }

        if ($request->filled('min_price')) {
            $query->where('price', '>=', $request->input('min_price'));
        }

        if ($request->filled('max_price')) {
            $query->where('price', '<=', $request->input('max_price'));
        }

        if ($request->filled('is_original')) {
            $query->where('is_original', $request->boolean('is_original'));
        }

        // Ordenação
        $sortBy = $request->input('sort', 'created_at');
        $sortDirection = $request->input('direction', 'desc');
        
        switch ($sortBy) {
            case 'price_asc':
                $query->orderBy('price', 'asc');
                break;
            case 'price_desc':
                $query->orderBy('price', 'desc');
                break;
            case 'name':
                $query->orderBy('name', 'asc');
                break;
            default:
                $query->orderBy('created_at', 'desc');
        }

        $parts = $query->paginate(12)->withQueryString();

        // Dados para filtros
        $categories = Category::where('type', 'parts')
            ->whereHas('parts', function($q) {
                $q->where('status', 'active')->where('stock_quantity', '>', 0);
            })
            ->get(['id', 'name']);

        $brands = Brand::whereHas('parts', function($q) {
                $q->where('status', 'active')->where('stock_quantity', '>', 0);
            })
            ->get(['id', 'name']);

        return Inertia::render('Parts/Index', [
            'parts' => $parts,
            'categories' => $categories,
            'brands' => $brands,
            'filters' => $request->all('search', 'category_id', 'brand_id', 'min_price', 'max_price', 'is_original', 'sort', 'direction'),
        ]);
    }

    /**
     * Display the specified part.
     */
    public function show($slug)
    {
        $part = Part::with(['brand', 'category', 'user', 'compatibleVehicles.brand'])
            ->where('slug', $slug)
            ->where('status', 'active')
            ->firstOrFail();

        // Incrementar visualizações
        $part->increment('views');

        // Peças relacionadas
        $relatedParts = Part::where('category_id', $part->category_id)
            ->where('id', '!=', $part->id)
            ->where('status', 'active')
            ->where('stock_quantity', '>', 0)
            ->with(['brand', 'category'])
            ->take(4)
            ->get();

        // Outras peças do mesmo vendedor
        $sellerParts = Part::where('user_id', $part->user_id)
            ->where('id', '!=', $part->id)
            ->where('status', 'active')
            ->where('stock_quantity', '>', 0)
            ->with(['brand', 'category'])
            ->take(4)
            ->get();

        return Inertia::render('Parts/Show', [
            'part' => $part,
            'relatedParts' => $relatedParts,
            'sellerParts' => $sellerParts,
        ]);
    }

    /**
     * Search parts by vehicle compatibility.
     */
    public function searchByVehicle(Request $request)
    {
        $request->validate([
            'brand_id' => 'required|exists:brands,id',
            'model' => 'required|string',
            'year' => 'required|integer|min:1900|max:' . (date('Y') + 1),
        ]);

        $parts = Part::whereHas('compatibleVehicles', function($query) use ($request) {
                $query->where('brand_id', $request->brand_id)
                      ->where('model', 'like', '%' . $request->model . '%')
                      ->where('year_manufacture', '<=', $request->year)
                      ->where('model_year', '>=', $request->year - 5); // Compatibilidade de 5 anos
            })
            ->where('status', 'active')
            ->where('stock_quantity', '>', 0)
            ->with(['brand', 'category', 'user'])
            ->paginate(12);

        return Inertia::render('Parts/VehicleCompatible', [
            'parts' => $parts,
            'vehicle' => $request->only('brand_id', 'model', 'year'),
        ]);
    }
}
