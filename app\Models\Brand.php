<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Brand extends Model
{
    use SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'slug',
        'description',
        'logo',
        'website',
        'is_active',
        'order',
        'country',
        'founded_year',
        'seo_title',
        'seo_description',
        'seo_keywords',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'is_active' => 'boolean',
        'order' => 'integer',
        'founded_year' => 'integer',
    ];

    /**
     * The model's default values for attributes.
     *
     * @var array
     */
    /**
     * The accessors to append to the model's array form.
     *
     * @var array
     */
    protected $appends = ['url', 'logo_url'];

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    /**
     * The model's default values for attributes.
     *
     * @var array
     */
    protected $attributes = [
        'is_active' => true,
        'order' => 0,
        'country' => 'BR',
    ];

    /**
     * Get the vehicles for the brand.
     */
    public function vehicles(): HasMany
    {
        return $this->hasMany(Vehicle::class);
    }

    /**
     * Get the active vehicles for the brand.
     */
    public function activeVehicles(): HasMany
    {
        return $this->vehicles()->where('status', 'published');
    }

    /**
     * Get the featured vehicles for the brand.
     */
    public function featuredVehicles(): HasMany
    {
        return $this->vehicles()
            ->where('is_featured', true)
            ->where('status', 'published')
            ->latest()
            ->take(5);
    }

    /**
     * Get the parts for the brand.
     */
    public function parts(): HasMany
    {
        return $this->hasMany(Part::class);
    }

    /**
     * Get the active parts for the brand.
     */
    public function activeParts(): HasMany
    {
        return $this->parts()->where('status', 'published');
    }

    /**
     * Get the featured parts for the brand.
     */
    public function featuredParts(): HasMany
    {
        return $this->parts()
            ->where('is_featured', true)
            ->where('status', 'published')
            ->latest()
            ->take(5);
    }

    /**
     * Scope a query to only include active brands.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to only include featured brands.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * Scope a query to sort brands by the number of vehicles.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  string  $direction
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeOrderByVehiclesCount($query, $direction = 'desc')
    {
        return $query->withCount('vehicles')
            ->orderBy('vehicles_count', $direction);
    }

    /**
     * Get the URL for the brand.
     *
     * @return string
     */
    public function getUrlAttribute()
    {
        // Temporariamente retornar URL simples até implementar a rota brands.show
        return "/marcas/{$this->slug}";
    }

    /**
     * Get the full URL for the brand's logo.
     *
     * @return string|null
     */
    public function getLogoUrlAttribute()
    {
        if (!$this->logo) {
            return null;
        }

        if (filter_var($this->logo, FILTER_VALIDATE_URL)) {
            return $this->logo;
        }

        return asset('storage/' . $this->logo);
    }

    /**
     * Get the route key for the model.
     *
     * @return string
     */
    public function getRouteKeyName()
    {
        return 'slug';
    }

    /**
     * Get the number of active vehicles for the brand.
     *
     * @return int
     */
    public function getActiveVehiclesCountAttribute()
    {
        return $this->activeVehicles()->count();
    }

    /**
     * Get the number of active parts for the brand.
     *
     * @return int
     */
    public function getActivePartsCountAttribute()
    {
        return $this->activeParts()->count();
    }
}
