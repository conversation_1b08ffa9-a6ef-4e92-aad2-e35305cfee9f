import Link from "next/link"
import { <PERSON>, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { MapPin, Calendar, Fuel, Heart, Share2 } from "lucide-react"

const listings = [
  {
    id: 1,
    title: "Honda Civic 2022 EXL",
    price: "R$ 89.900",
    location: "São Paulo, SP",
    year: "2022",
    fuel: "Flex",
    mileage: "25.000 km",
    image: "/honda-civic-2022-silver-car.jpg",
    type: "Carro",
    featured: true,
    seller: "João Silva",
    phone: "(11) 99999-9999",
  },
  {
    id: 2,
    title: "Yamaha MT-07 2021",
    price: "R$ 32.500",
    location: "Rio de Janeiro, RJ",
    year: "2021",
    fuel: "Gasolina",
    mileage: "15.000 km",
    image: "/yamaha-mt-07-2021-blue-motorcycle.jpg",
    type: "Moto",
    featured: false,
    seller: "Maria Santos",
    phone: "(21) 88888-8888",
  },
  {
    id: 3,
    title: "Toyota Corolla 2023 Hybrid",
    price: "R$ 125.000",
    location: "Belo Horizonte, MG",
    year: "2023",
    fuel: "Híbrido",
    mileage: "8.000 km",
    image: "/toyota-corolla-2023-white-car.jpg",
    type: "Carro",
    featured: true,
    seller: "Carlos Oliveira",
    phone: "(31) 77777-7777",
  },
  {
    id: 4,
    title: "Kit Suspensão Civic Completo",
    price: "R$ 1.250",
    location: "Curitiba, PR",
    year: "2023",
    fuel: "-",
    mileage: "Novo",
    image: "/car-suspension-kit-parts.jpg",
    type: "Peça",
    featured: false,
    seller: "AutoPeças PR",
    phone: "(41) 66666-6666",
  },
  {
    id: 5,
    title: "Volkswagen Golf 2020 TSI",
    price: "R$ 78.500",
    location: "Porto Alegre, RS",
    year: "2020",
    fuel: "Gasolina",
    mileage: "45.000 km",
    image: "/volkswagen-golf-2020-red-car.jpg",
    type: "Carro",
    featured: false,
    seller: "Pedro Costa",
    phone: "(51) 55555-5555",
  },
  {
    id: 6,
    title: "Honda CB 600F Hornet 2019",
    price: "R$ 28.900",
    location: "Salvador, BA",
    year: "2019",
    fuel: "Gasolina",
    mileage: "32.000 km",
    image: "/honda-cb-600f-2019-yellow-motorcycle.jpg",
    type: "Moto",
    featured: false,
    seller: "Moto Center BA",
    phone: "(71) 44444-4444",
  },
  {
    id: 7,
    title: "Ford Ka 2021 SE Plus",
    price: "R$ 52.000",
    location: "Fortaleza, CE",
    year: "2021",
    fuel: "Flex",
    mileage: "18.000 km",
    image: "/ford-ka-2021-white-car.jpg",
    type: "Carro",
    featured: false,
    seller: "Ana Ferreira",
    phone: "(85) 33333-3333",
  },
  {
    id: 8,
    title: "Pneu Michelin 205/55 R16 (Jogo)",
    price: "R$ 890",
    location: "Brasília, DF",
    year: "2024",
    fuel: "-",
    mileage: "Novo",
    image: "/michelin-tire-set-car-parts.jpg",
    type: "Peça",
    featured: false,
    seller: "Pneus Brasília",
    phone: "(61) 22222-2222",
  },
]

export function ListingGrid() {
  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
        {listings.map((listing) => (
          <Card key={listing.id} className="group hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
            <div className="relative">
              <img
                src={listing.image || "/placeholder.svg"}
                alt={listing.title}
                className="w-full h-48 object-cover rounded-t-lg"
              />
              {listing.featured && <Badge className="absolute top-3 left-3 bg-accent">Destaque</Badge>}
              <Badge variant="secondary" className="absolute top-3 right-3 bg-background/90">
                {listing.type}
              </Badge>

              {/* Action buttons */}
              <div className="absolute top-3 right-16 flex space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
                <Button size="sm" variant="secondary" className="h-8 w-8 p-0 bg-background/90">
                  <Heart className="h-4 w-4" />
                </Button>
                <Button size="sm" variant="secondary" className="h-8 w-8 p-0 bg-background/90">
                  <Share2 className="h-4 w-4" />
                </Button>
              </div>
            </div>

            <CardContent className="p-4">
              <Link href={`/anuncio/${listing.id}`}>
                <h3 className="font-semibold text-lg mb-2 group-hover:text-accent transition-colors line-clamp-2">
                  {listing.title}
                </h3>
              </Link>

              <div className="text-2xl font-bold text-primary mb-3">{listing.price}</div>

              <div className="space-y-2 text-sm text-muted-foreground mb-4">
                <div className="flex items-center">
                  <MapPin className="h-4 w-4 mr-2 flex-shrink-0" />
                  <span className="truncate">{listing.location}</span>
                </div>
                <div className="flex items-center">
                  <Calendar className="h-4 w-4 mr-2 flex-shrink-0" />
                  <span>
                    {listing.year} • {listing.mileage}
                  </span>
                </div>
                {listing.fuel !== "-" && (
                  <div className="flex items-center">
                    <Fuel className="h-4 w-4 mr-2 flex-shrink-0" />
                    <span>{listing.fuel}</span>
                  </div>
                )}
              </div>

              <div className="text-xs text-muted-foreground mb-3">Vendedor: {listing.seller}</div>

              <div className="flex space-x-2">
                <Button asChild className="flex-1 bg-transparent" variant="outline">
                  <Link href={`/anuncio/${listing.id}`}>Ver detalhes</Link>
                </Button>
                <Button className="flex-1">Contatar</Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Pagination */}
      <div className="flex items-center justify-center space-x-2 pt-8">
        <Button variant="outline" disabled>
          Anterior
        </Button>
        <Button variant="default">1</Button>
        <Button variant="outline">2</Button>
        <Button variant="outline">3</Button>
        <Button variant="outline">4</Button>
        <Button variant="outline">5</Button>
        <Button variant="outline">Próximo</Button>
      </div>

      <div className="text-center text-sm text-muted-foreground">Mostrando 8 de 12.847 anúncios</div>
    </div>
  )
}
