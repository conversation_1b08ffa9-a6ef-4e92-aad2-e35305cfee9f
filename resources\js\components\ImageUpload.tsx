import { useRef, useState, useCallback } from 'react';
import { X, Upload } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';

export interface ImageFile extends File {
    preview: string;
    id: string;
}

interface ImageUploadProps {
    label?: string;
    description?: string;
    multiple?: boolean;
    maxFiles?: number;
    value?: ImageFile[];
    onChange: (files: ImageFile[]) => void;
    onRemove: (file: ImageFile) => void;
    disabled?: boolean;
    accept?: string;
}

export function ImageUpload({
    label = 'Upload de Imagens',
    description = 'Arraste e solte imagens aqui ou clique para selecionar',
    multiple = true,
    maxFiles = 10,
    value = [],
    onChange,
    onRemove,
    disabled = false,
    accept = 'image/*',
}: ImageUploadProps) {
    const [isDragging, setIsDragging] = useState(false);
    const fileInputRef = useRef<HTMLInputElement>(null);

    const handleFileChange = useCallback(
        (files: FileList | null) => {
            if (!files || files.length === 0) return;

            const newFiles: ImageFile[] = [];
            const remainingSlots = Math.max(0, maxFiles - value.length);
            const filesToProcess = Array.from(files).slice(0, remainingSlots);

            filesToProcess.forEach((file) => {
                if (!file.type.startsWith('image/')) return;

                const imageFile = Object.assign(file, {
                    preview: URL.createObjectURL(file),
                    id: Math.random().toString(36).substring(2, 9),
                }) as ImageFile;

                newFiles.push(imageFile);
            });

            if (newFiles.length > 0) {
                onChange([...value, ...newFiles]);
            }
        },
        [maxFiles, onChange, value]
    );

    const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
        e.preventDefault();
        e.stopPropagation();
        if (disabled) return;
        setIsDragging(true);
    };

    const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
        e.preventDefault();
        e.stopPropagation();
        setIsDragging(false);
    };

    const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
        e.preventDefault();
        e.stopPropagation();
        setIsDragging(false);

        if (disabled) return;
        if (value.length >= maxFiles) return;

        const files = e.dataTransfer.files;
        handleFileChange(files);
    };

    const handleClick = () => {
        if (disabled) return;
        fileInputRef.current?.click();
    };

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        if (e.target.files) {
            handleFileChange(e.target.files);
            // Reset the input value to allow selecting the same file again
            if (fileInputRef.current) {
                fileInputRef.current.value = '';
            }
        }
    };

    return (
        <div className="space-y-2">
            {label && <label className="text-sm font-medium leading-none">{label}</label>}
            {description && <p className="text-sm text-muted-foreground">{description}</p>}

            <input
                type="file"
                ref={fileInputRef}
                onChange={handleInputChange}
                className="hidden"
                multiple={multiple}
                accept={accept}
                disabled={disabled || value.length >= maxFiles}
            />

            <div
                className={`border-2 border-dashed rounded-lg transition-colors ${
                    isDragging ? 'border-primary bg-primary/5' : 'border-border'
                }`}
                onDragOver={handleDragOver}
                onDragLeave={handleDragLeave}
                onDrop={handleDrop}
            >
                <Card
                    className={`border-0 shadow-none transition-colors ${
                        isDragging ? 'bg-primary/5' : ''
                    }`}
                >
                    <CardContent className="p-6">
                        <div className="flex flex-col items-center justify-center space-y-2 text-center">
                            <div className="p-3 rounded-full bg-primary/10">
                                <Upload className="w-6 h-6 text-primary" />
                            </div>
                            <div className="space-y-1">
                                <p className="text-sm font-medium">
                                    {isDragging ? 'Solte as imagens aqui' : 'Arraste e solte imagens aqui'}
                                    <Upload className="w-12 h-12 text-muted-foreground mb-2" />
                                    {multiple
                                        ? `Até ${maxFiles} imagens (${value.length}/${maxFiles} selecionadas)`
                                        : 'Apenas uma imagem'}
                                </p>
                            </div>
                            <Button
                                type="button"
                                variant="outline"
                                size="sm"
                                className="mt-2"
                                onClick={handleClick}
                                disabled={disabled || value.length >= maxFiles}
                            >
                                Selecionar Imagens
                            </Button>
                        </div>
                    </CardContent>
                </Card>
            </div>

            {value.length > 0 && (
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4 mt-4">
                    {value.map((file) => (
                        <div key={file.id} className="relative group">
                            <div className="aspect-square overflow-hidden rounded-md border border-border">
                                <img
                                    src={file.preview}
                                    alt={file.name}
                                    className="w-full h-full object-cover"
                                />
                            </div>
                            <Button
                                type="button"
                                variant="destructive"
                                size="icon"
                                className="absolute -top-2 -right-2 w-6 h-6 rounded-full opacity-0 group-hover:opacity-100 transition-opacity"
                                onClick={() => onRemove(file)}
                            >
                                <X className="w-3 h-3" />
                            </Button>
                        </div>
                    ))}
                </div>
            )}
        </div>
    );
}
