import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
    <PERSON><PERSON>,
    DialogContent,
    <PERSON><PERSON>Header,
    <PERSON><PERSON><PERSON><PERSON>le,
    Di<PERSON>Trigger,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import MainLayout from '@/layouts/MainLayout';
import { PageProps } from '@/types';
import { Head, Link, useForm } from '@inertiajs/react';
import {
    Calendar,
    Clock,
    Flag,
    MapPin,
    MessageCircle,
    Star,
    User as UserIcon,
} from 'lucide-react';
import { useState } from 'react';

interface Advertisement {
    id: number;
    title: string;
    description: string;
    price: number;
    location: string;
    created_at: string;
    is_featured: boolean;
    featured_image?: {
        url: string;
        alt: string;
    };
    vehicle: {
        id: number;
        model: string;
        year: number;
        mileage: number;
        fuel_type: string;
        transmission: string;
        brand: {
            id: number;
            name: string;
        };
        category: {
            id: number;
            name: string;
        };
    };
}

interface User {
    id: number;
    name: string;
    avatar?: string;
    created_at: string;
}

interface Stats {
    total_ads: number;
    active_ads: number;
    member_since: string;
    avg_response_time: string;
    rating: number;
    total_reviews: number;
}

interface Review {
    id: number;
    rating: number;
    comment: string;
    reviewer_name: string;
    created_at: string;
}

interface UserShowProps extends PageProps {
    user: User;
    advertisements: {
        data: Advertisement[];
        links: any[];
        meta: any;
    };
    stats: Stats;
    reviews: Review[];
}

export default function UserShow({
    user,
    advertisements,
    stats,
    reviews,
}: UserShowProps) {
    const [showContactForm, setShowContactForm] = useState(false);
    const [showReportForm, setShowReportForm] = useState(false);

    const { data: contactData, setData: setContactData, post: postContact, processing: contactProcessing } = useForm({
        name: '',
        email: '',
        phone: '',
        subject: '',
        message: '',
    });

    const { data: reportData, setData: setReportData, post: postReport, processing: reportProcessing } = useForm({
        reason: '',
        description: '',
    });

    const formatPrice = (price: number) => {
        return new Intl.NumberFormat('pt-BR', {
            style: 'currency',
            currency: 'BRL',
        }).format(price);
    };

    const formatMileage = (mileage: number) => {
        return new Intl.NumberFormat('pt-BR').format(mileage) + ' km';
    };

    const formatDate = (date: string) => {
        return new Date(date).toLocaleDateString('pt-BR');
    };

    const handleContact = (e: React.FormEvent) => {
        e.preventDefault();
        postContact(`/usuario/${user.id}/contato`, {
            onSuccess: () => {
                setShowContactForm(false);
                // Show success message
            },
        });
    };

    const handleReport = (e: React.FormEvent) => {
        e.preventDefault();
        postReport(`/usuario/${user.id}/denunciar`, {
            onSuccess: () => {
                setShowReportForm(false);
                // Show success message
            },
        });
    };

    const renderStars = (rating: number) => {
        return Array.from({ length: 5 }, (_, i) => (
            <Star
                key={i}
                className={`h-4 w-4 ${
                    i < Math.floor(rating)
                        ? 'text-yellow-400 fill-current'
                        : 'text-gray-300'
                }`}
            />
        ));
    };

    return (
        <MainLayout>
            <Head title={`Perfil de ${user.name}`} />
            
            <div className="min-h-screen bg-gray-50">
                <div className="container mx-auto px-4 py-6">
                    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                        {/* Sidebar - Informações do usuário */}
                        <div className="lg:col-span-1 space-y-6">
                            {/* Perfil principal */}
                            <Card>
                                <CardContent className="p-6">
                                    <div className="text-center">
                                        <div className="w-24 h-24 mx-auto mb-4 bg-gray-200 rounded-full flex items-center justify-center">
                                            {user.avatar ? (
                                                <img
                                                    src={user.avatar}
                                                    alt={user.name}
                                                    className="w-full h-full rounded-full object-cover"
                                                />
                                            ) : (
                                                <UserIcon className="h-12 w-12 text-gray-400" />
                                            )}
                                        </div>
                                        
                                        <h1 className="text-2xl font-bold mb-2">{user.name}</h1>
                                        
                                        <div className="flex items-center justify-center gap-1 mb-2">
                                            {renderStars(stats.rating)}
                                            <span className="ml-2 text-sm text-gray-600">
                                                {stats.rating} ({stats.total_reviews} avaliações)
                                            </span>
                                        </div>
                                        
                                        <div className="flex items-center justify-center text-sm text-gray-600 mb-4">
                                            <Calendar className="h-4 w-4 mr-1" />
                                            Membro desde {stats.member_since}
                                        </div>

                                        <div className="space-y-2">
                                            <Dialog open={showContactForm} onOpenChange={setShowContactForm}>
                                                <DialogTrigger asChild>
                                                    <Button className="w-full">
                                                        <MessageCircle className="mr-2 h-4 w-4" />
                                                        Entrar em contato
                                                    </Button>
                                                </DialogTrigger>
                                                <DialogContent>
                                                    <DialogHeader>
                                                        <DialogTitle>Entrar em contato com {user.name}</DialogTitle>
                                                    </DialogHeader>
                                                    <form onSubmit={handleContact} className="space-y-4">
                                                        <div>
                                                            <Label>Nome</Label>
                                                            <Input
                                                                value={contactData.name}
                                                                onChange={(e) => setContactData('name', e.target.value)}
                                                                required
                                                            />
                                                        </div>
                                                        <div>
                                                            <Label>E-mail</Label>
                                                            <Input
                                                                type="email"
                                                                value={contactData.email}
                                                                onChange={(e) => setContactData('email', e.target.value)}
                                                                required
                                                            />
                                                        </div>
                                                        <div>
                                                            <Label>Telefone (opcional)</Label>
                                                            <Input
                                                                value={contactData.phone}
                                                                onChange={(e) => setContactData('phone', e.target.value)}
                                                            />
                                                        </div>
                                                        <div>
                                                            <Label>Assunto</Label>
                                                            <Input
                                                                value={contactData.subject}
                                                                onChange={(e) => setContactData('subject', e.target.value)}
                                                                required
                                                            />
                                                        </div>
                                                        <div>
                                                            <Label>Mensagem</Label>
                                                            <Textarea
                                                                value={contactData.message}
                                                                onChange={(e) => setContactData('message', e.target.value)}
                                                                required
                                                            />
                                                        </div>
                                                        <Button type="submit" disabled={contactProcessing} className="w-full">
                                                            Enviar mensagem
                                                        </Button>
                                                    </form>
                                                </DialogContent>
                                            </Dialog>

                                            <Dialog open={showReportForm} onOpenChange={setShowReportForm}>
                                                <DialogTrigger asChild>
                                                    <Button variant="outline" className="w-full">
                                                        <Flag className="mr-2 h-4 w-4" />
                                                        Denunciar usuário
                                                    </Button>
                                                </DialogTrigger>
                                                <DialogContent>
                                                    <DialogHeader>
                                                        <DialogTitle>Denunciar usuário</DialogTitle>
                                                    </DialogHeader>
                                                    <form onSubmit={handleReport} className="space-y-4">
                                                        <div>
                                                            <Label>Motivo</Label>
                                                            <select
                                                                value={reportData.reason}
                                                                onChange={(e) => setReportData('reason', e.target.value)}
                                                                className="w-full p-2 border rounded-md"
                                                                required
                                                            >
                                                                <option value="">Selecione um motivo</option>
                                                                <option value="spam">Spam</option>
                                                                <option value="inappropriate">Comportamento inapropriado</option>
                                                                <option value="fake">Perfil falso</option>
                                                                <option value="harassment">Assédio</option>
                                                                <option value="other">Outro</option>
                                                            </select>
                                                        </div>
                                                        <div>
                                                            <Label>Descrição (opcional)</Label>
                                                            <Textarea
                                                                value={reportData.description}
                                                                onChange={(e) => setReportData('description', e.target.value)}
                                                                placeholder="Descreva o problema..."
                                                            />
                                                        </div>
                                                        <Button type="submit" disabled={reportProcessing}>
                                                            Enviar denúncia
                                                        </Button>
                                                    </form>
                                                </DialogContent>
                                            </Dialog>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>

                            {/* Estatísticas */}
                            <Card>
                                <CardHeader>
                                    <CardTitle>Estatísticas</CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <div className="space-y-4">
                                        <div className="flex justify-between">
                                            <span className="text-gray-600">Anúncios ativos:</span>
                                            <span className="font-semibold">{stats.active_ads}</span>
                                        </div>
                                        <div className="flex justify-between">
                                            <span className="text-gray-600">Total de anúncios:</span>
                                            <span className="font-semibold">{stats.total_ads}</span>
                                        </div>
                                        <div className="flex justify-between">
                                            <span className="text-gray-600">Tempo de resposta:</span>
                                            <span className="font-semibold flex items-center">
                                                <Clock className="h-3 w-3 mr-1" />
                                                {stats.avg_response_time}
                                            </span>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>

                            {/* Avaliações recentes */}
                            {reviews.length > 0 && (
                                <Card>
                                    <CardHeader>
                                        <CardTitle>Avaliações recentes</CardTitle>
                                    </CardHeader>
                                    <CardContent>
                                        <div className="space-y-4">
                                            {reviews.slice(0, 3).map((review) => (
                                                <div key={review.id} className="border-b pb-3 last:border-b-0">
                                                    <div className="flex items-center gap-1 mb-1">
                                                        {renderStars(review.rating)}
                                                    </div>
                                                    <p className="text-sm text-gray-700 mb-1">{review.comment}</p>
                                                    <div className="text-xs text-gray-500">
                                                        {review.reviewer_name} • {formatDate(review.created_at)}
                                                    </div>
                                                </div>
                                            ))}
                                        </div>
                                    </CardContent>
                                </Card>
                            )}
                        </div>

                        {/* Conteúdo principal - Anúncios */}
                        <div className="lg:col-span-2">
                            <div className="flex justify-between items-center mb-6">
                                <h2 className="text-2xl font-bold">
                                    Anúncios de {user.name} ({advertisements.meta.total})
                                </h2>
                                
                                <Link href={`/usuario/${user.id}/anuncios`}>
                                    <Button variant="outline">
                                        Ver todos os anúncios
                                    </Button>
                                </Link>
                            </div>

                            {advertisements.data.length > 0 ? (
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    {advertisements.data.map((ad) => (
                                        <Card key={ad.id} className="overflow-hidden hover:shadow-lg transition-shadow">
                                            {/* Imagem */}
                                            <div className="aspect-video relative">
                                                {ad.featured_image ? (
                                                    <img
                                                        src={ad.featured_image.url}
                                                        alt={ad.featured_image.alt}
                                                        className="w-full h-full object-cover"
                                                    />
                                                ) : (
                                                    <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                                                        <span className="text-gray-400">Sem imagem</span>
                                                    </div>
                                                )}
                                                
                                                {ad.is_featured && (
                                                    <Badge className="absolute top-2 left-2 bg-yellow-500">
                                                        Destaque
                                                    </Badge>
                                                )}
                                            </div>
                                            
                                            {/* Conteúdo */}
                                            <CardContent className="p-4">
                                                <div className="space-y-2">
                                                    <h3 className="font-semibold text-lg line-clamp-2">
                                                        <Link 
                                                            href={`/anuncios/${ad.id}`}
                                                            className="hover:text-blue-600"
                                                        >
                                                            {ad.title}
                                                        </Link>
                                                    </h3>
                                                    
                                                    <div className="flex flex-wrap gap-2 text-sm text-gray-600">
                                                        <span>{ad.vehicle.brand.name}</span>
                                                        <span>•</span>
                                                        <span>{ad.vehicle.year}</span>
                                                        <span>•</span>
                                                        <span>{formatMileage(ad.vehicle.mileage)}</span>
                                                    </div>
                                                    
                                                    <div className="flex flex-wrap gap-1">
                                                        <Badge variant="secondary">
                                                            {ad.vehicle.fuel_type}
                                                        </Badge>
                                                        <Badge variant="secondary">
                                                            {ad.vehicle.transmission}
                                                        </Badge>
                                                    </div>
                                                    
                                                    <div className="flex justify-between items-center">
                                                        <span className="text-2xl font-bold text-green-600">
                                                            {formatPrice(ad.price)}
                                                        </span>
                                                        <div className="text-right">
                                                            <div className="flex items-center text-sm text-gray-500">
                                                                <MapPin className="h-3 w-3 mr-1" />
                                                                {ad.location}
                                                            </div>
                                                            <div className="text-xs text-gray-400">
                                                                {formatDate(ad.created_at)}
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </CardContent>
                                        </Card>
                                    ))}
                                </div>
                            ) : (
                                <div className="text-center py-12">
                                    <h3 className="text-xl font-semibold mb-2">Nenhum anúncio encontrado</h3>
                                    <p className="text-gray-600">
                                        Este usuário ainda não publicou nenhum anúncio.
                                    </p>
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            </div>
        </MainLayout>
    );
}
