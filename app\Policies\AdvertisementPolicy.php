<?php

namespace App\Policies;

use App\Models\Advertisement;
use App\Models\User;
use Illuminate\Auth\Access\Response;

class AdvertisementPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->can('view_any_advertisement');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, Advertisement $advertisement): bool
    {
        // Donor can view their own ads
        if ($user->id === $advertisement->user_id) {
            return true;
        }
        
        // Admin/Moderator can view any ad
        return $user->can('view_advertisement');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        // Any authenticated user can create ads
        return $user->hasVerifiedEmail();
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, Advertisement $advertisement): bool
    {
        // Only the owner or admin/moderator can update
        if ($user->id === $advertisement->user_id) {
            // Only allow updates if ad is not published or expired
            return in_array($advertisement->status, [
                Advertisement::STATUS_DRAFT,
                Advertisement::STATUS_REJECTED
            ]);
        }
        
        return $user->can('update_advertisement');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Advertisement $advertisement): bool
    {
        // Only the owner or admin/moderator can delete
        if ($user->id === $advertisement->user_id) {
            return true;
        }
        
        return $user->can('delete_advertisement');
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, Advertisement $advertisement): bool
    {
        return $user->can('restore_advertisement');
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, Advertisement $advertisement): bool
    {
        return $user->can('force_delete_advertisement');
    }
    
    /**
     * Determine whether the user can approve the advertisement.
     */
    public function approve(User $user, Advertisement $advertisement): bool
    {
        return $user->can('approve_advertisement') && 
               $advertisement->status === Advertisement::STATUS_PENDING_REVIEW;
    }
    
    /**
     * Determine whether the user can reject the advertisement.
     */
    public function reject(User $user, Advertisement $advertisement): bool
    {
        return $user->can('reject_advertisement') && 
               $advertisement->status === Advertisement::STATUS_PENDING_REVIEW;
    }
    
    /**
     * Determine whether the user can publish the advertisement.
     */
    public function publish(User $user, Advertisement $advertisement): bool
    {
        // Only admin/moderator can publish
        return $user->can('publish_advertisement') && 
               $advertisement->status === Advertisement::STATUS_APPROVED;
    }
    
    /**
     * Determine whether the user can mark the advertisement as sold.
     */
    public function markAsSold(User $user, Advertisement $advertisement): bool
    {
        // Only the owner or admin/moderator can mark as sold
        if ($user->id === $advertisement->user_id) {
            return in_array($advertisement->status, [
                Advertisement::STATUS_PUBLISHED,
                Advertisement::STATUS_APPROVED
            ]);
        }
        
        return $user->can('mark_sold_advertisement');
    }
}
