<?php

namespace App\Http\Controllers\Public;

use App\Http\Controllers\Controller;
use App\Models\Advertisement;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class AdvertisementController extends Controller
{
    /**
     * Display the specified advertisement
     */
    public function show(Advertisement $advertisement): Response
    {
        // Check if advertisement is published
        if ($advertisement->status !== 'published') {
            abort(404);
        }

        // Load relationships
        $advertisement->load([
            'vehicle.brand',
            'vehicle.category',
            'vehicle.features',
            'user',
            'images'
        ]);

        // Get related advertisements (same category, different ad)
        $relatedAds = Advertisement::with(['vehicle.brand'])
            ->where('status', 'published')
            ->where('id', '!=', $advertisement->id)
            ->whereHas('vehicle', function($query) use ($advertisement) {
                $query->where('category_id', $advertisement->vehicle->category_id);
            })
            ->limit(6)
            ->get();

        // Get other ads from the same seller
        $sellerAds = Advertisement::with(['vehicle.brand'])
            ->where('status', 'published')
            ->where('user_id', $advertisement->user_id)
            ->where('id', '!=', $advertisement->id)
            ->limit(4)
            ->get();

        return Inertia::render('Advertisements/Show', [
            'advertisement' => $advertisement,
            'relatedAds' => $relatedAds,
            'sellerAds' => $sellerAds,
        ]);
    }

    /**
     * Display a listing of advertisements
     */
    public function index(Request $request): Response
    {
        $query = Advertisement::with(['vehicle.brand', 'vehicle.category', 'user', 'featuredImage'])
            ->where('status', 'published');

        // Apply filters
        if ($request->filled('search')) {
            $search = $request->input('search');
            $query->where(function($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhereHas('vehicle', function($vehicleQuery) use ($search) {
                      $vehicleQuery->where('model', 'like', "%{$search}%")
                                  ->orWhere('year', 'like', "%{$search}%");
                  });
            });
        }

        if ($request->filled('category_id')) {
            $query->whereHas('vehicle', function($vehicleQuery) use ($request) {
                $vehicleQuery->where('category_id', $request->input('category_id'));
            });
        }

        if ($request->filled('brand_id')) {
            $query->whereHas('vehicle', function($vehicleQuery) use ($request) {
                $vehicleQuery->where('brand_id', $request->input('brand_id'));
            });
        }

        if ($request->filled('min_price')) {
            $query->where('price', '>=', $request->input('min_price'));
        }

        if ($request->filled('max_price')) {
            $query->where('price', '<=', $request->input('max_price'));
        }

        // Sorting
        $sortBy = $request->input('sort_by', 'created_at');
        $sortOrder = $request->input('sort_order', 'desc');
        
        $validSortFields = ['created_at', 'price', 'title'];
        if (in_array($sortBy, $validSortFields)) {
            $query->orderBy($sortBy, $sortOrder);
        }

        $advertisements = $query->paginate(12)->withQueryString();

        return Inertia::render('Advertisements/Index', [
            'advertisements' => $advertisements,
            'filters' => $request->all(),
        ]);
    }

    /**
     * Contact seller
     */
    public function contact(Request $request, Advertisement $advertisement)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'nullable|string|max:20',
            'message' => 'required|string|max:1000',
        ]);

        // If user is authenticated, create a chat
        if (auth()->check()) {
            // Check if chat already exists
            $existingChat = \App\Models\Chat::where('advertisement_id', $advertisement->id)
                ->where('buyer_id', auth()->id())
                ->first();

            if ($existingChat) {
                // Add message to existing chat
                \App\Models\ChatMessage::create([
                    'chat_id' => $existingChat->id,
                    'user_id' => auth()->id(),
                    'message' => $request->input('message'),
                ]);

                $existingChat->touch(); // Update updated_at

                return response()->json([
                    'chat_id' => $existingChat->id,
                    'message' => 'Mensagem enviada com sucesso!',
                ]);
            }

            // Create new chat
            $chat = \App\Models\Chat::create([
                'advertisement_id' => $advertisement->id,
                'buyer_id' => auth()->id(),
                'seller_id' => $advertisement->user_id,
            ]);

            // Create first message
            \App\Models\ChatMessage::create([
                'chat_id' => $chat->id,
                'user_id' => auth()->id(),
                'message' => $request->input('message'),
            ]);

            return response()->json([
                'chat_id' => $chat->id,
                'message' => 'Chat iniciado com sucesso!',
            ]);
        }

        // For non-authenticated users, just send email (to be implemented)
        return response()->json([
            'message' => 'Mensagem enviada com sucesso! O vendedor entrará em contato em breve.'
        ]);
    }

    /**
     * Add to favorites
     */
    public function favorite(Request $request, Advertisement $advertisement)
    {
        if (!auth()->check()) {
            return response()->json(['message' => 'Você precisa estar logado para favoritar.'], 401);
        }

        $user = auth()->user();
        
        // Check if already favorited
        if ($user->favoriteAdvertisements()->where('advertisement_id', $advertisement->id)->exists()) {
            // Remove from favorites
            $user->favoriteAdvertisements()->detach($advertisement->id);
            return response()->json(['favorited' => false, 'message' => 'Removido dos favoritos']);
        } else {
            // Add to favorites
            $user->favoriteAdvertisements()->attach($advertisement->id);
            return response()->json(['favorited' => true, 'message' => 'Adicionado aos favoritos']);
        }
    }

    /**
     * Report advertisement
     */
    public function report(Request $request, Advertisement $advertisement)
    {
        $request->validate([
            'reason' => 'required|string|in:spam,inappropriate,fake,other',
            'description' => 'nullable|string|max:500',
        ]);

        // Here you would typically create a report record
        // For now, we'll just return a success response
        
        return response()->json([
            'message' => 'Denúncia enviada com sucesso! Nossa equipe irá analisar.'
        ]);
    }
}
