"use client"

import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { UserListings } from "@/components/user-listings"
import { UserFavorites } from "@/components/user-favorites"
import { UserStats } from "@/components/user-stats"
import { RecentActivity } from "@/components/recent-activity"
import { Car, Heart, MessageSquare, TrendingUp } from "lucide-react"

export function UserProfile() {
  return (
    <div className="space-y-6">
      {/* Welcome Header */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
              <h1 className="text-2xl font-bold mb-2">Bem-vindo de volta, <PERSON>!</h1>
              <p className="text-muted-foreground">
                <PERSON><PERSON><PERSON><PERSON> seus anúncio<PERSON>, visualize estatísticas e acompanhe suas vendas
              </p>
            </div>
            <Button asChild>
              <a href="/anunciar">Criar novo anúncio</a>
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Stats Overview */}
      <UserStats />

      {/* Main Content Tabs */}
      <Tabs defaultValue="anuncios" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="anuncios" className="flex items-center gap-2">
            <Car className="h-4 w-4" />
            <span className="hidden sm:inline">Anúncios</span>
          </TabsTrigger>
          <TabsTrigger value="favoritos" className="flex items-center gap-2">
            <Heart className="h-4 w-4" />
            <span className="hidden sm:inline">Favoritos</span>
          </TabsTrigger>
          <TabsTrigger value="atividade" className="flex items-center gap-2">
            <TrendingUp className="h-4 w-4" />
            <span className="hidden sm:inline">Atividade</span>
          </TabsTrigger>
          <TabsTrigger value="mensagens" className="flex items-center gap-2">
            <MessageSquare className="h-4 w-4" />
            <span className="hidden sm:inline">Mensagens</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="anuncios">
          <UserListings />
        </TabsContent>

        <TabsContent value="favoritos">
          <UserFavorites />
        </TabsContent>

        <TabsContent value="atividade">
          <RecentActivity />
        </TabsContent>

        <TabsContent value="mensagens">
          <Card>
            <CardHeader>
              <CardTitle>Mensagens</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <MessageSquare className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                <h3 className="text-lg font-semibold mb-2">Nenhuma mensagem</h3>
                <p className="text-muted-foreground">
                  Quando alguém entrar em contato sobre seus anúncios, as mensagens aparecerão aqui.
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
