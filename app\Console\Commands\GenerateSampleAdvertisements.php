<?php

namespace App\Console\Commands;

use App\Models\Advertisement;
use App\Models\User;
use App\Models\Vehicle;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class GenerateSampleAdvertisements extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'advertisements:generate-samples 
                            {count=10 : Número de anúncios a serem gerados}
                            {--status= : Status dos anúncios (draft, pending_review, approved, published, rejected, expired, sold)}
                            {--user= : ID do usuário que será o dono dos anúncios}
                            {--days=30 : Número de dias para a data de expiração a partir de hoje}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Gera anúncios de exemplo para testes';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $count = (int) $this->argument('count');
        $status = $this->option('status');
        $userId = $this->option('user');
        $days = (int) $this->option('days');
        
        // Validar status
        $validStatuses = [
            'draft', 'pending_review', 'approved', 
            'published', 'rejected', 'expired', 'sold', null
        ];
        
        if (!in_array($status, $validStatuses)) {
            $this->error("Status inválido. Use um dos seguintes: draft, pending_review, approved, published, rejected, expired, sold");
            return 1;
        }
        
        // Obter usuário
        $user = $userId ? User::find($userId) : User::first();
        
        if (!$user) {
            $this->error('Nenhum usuário encontrado. Crie pelo menos um usuário antes de executar este comando.');
            return 1;
        }
        
        // Obter veículos disponíveis
        $vehicles = Vehicle::inRandomOrder()->limit($count * 2)->get();
        
        if ($vehicles->isEmpty()) {
            $this->error('Nenhum veículo encontrado. Crie alguns veículos antes de executar este comando.');
            return 1;
        }
        
        $this->info("Gerando {$count} anúncios de exemplo para o usuário: {$user->name} ({$user->email})");
        
        $bar = $this->output->createProgressBar($count);
        $bar->start();
        
        $created = 0;
        
        for ($i = 0; $i < $count; $i++) {
            try {
                // Usar um veículo aleatório ou o mesmo veículo se não houver o suficiente
                $vehicle = $vehicles->get($i) ?? $vehicles->random();
                
                // Definir datas com base no status
                $now = now();
                $publishedAt = null;
                $expiresAt = null;
                
                if (in_array($status, ['published', 'expired', 'sold'])) {
                    $publishedAt = $now->copy()->subDays(rand(1, $days));
                    $expiresAt = $publishedAt->copy()->addDays($days);
                    
                    if ($status === 'expired') {
                        $expiresAt = $now->copy()->subDays(rand(1, 30));
                    }
                } elseif ($status === 'approved') {
                    $publishedAt = null; // Ainda não publicado
                    $expiresAt = $now->copy()->addDays($days);
                }
                
                // Criar o anúncio
                $ad = Advertisement::create([
                    'user_id' => $user->id,
                    'vehicle_id' => $vehicle->id,
                    'title' => $this->generateTitle($vehicle),
                    'description' => $this->generateDescription($vehicle),
                    'status' => $status ?? 'draft',
                    'price' => $this->generatePrice($vehicle->price ?? 0),
                    'is_negotiable' => (bool) rand(0, 1),
                    'is_featured' => (bool) rand(0, 4), // 20% de chance de ser destaque
                    'contact_phone' => $this->generatePhone(),
                    'contact_email' => $user->email,
                    'location' => $this->generateLocation(),
                    'latitude' => $this->generateCoordinate(-33.75, -33.45, 6), // Aproximadamente SP
                    'longitude' => $this->generateCoordinate(-70.65, -70.35, 6), // Aproximadamente SP
                    'published_at' => $publishedAt,
                    'expires_at' => $expiresAt,
                    'rejection_reason' => $status === 'rejected' ? 'Este anúncio foi rejeitado por não atender aos nossos critérios de publicação. Por favor, revise as diretrizes e tente novamente.' : null,
                ]);
                
                $created++;
                $bar->advance();
                
            } catch (\Exception $e) {
                $this->error("\nErro ao criar anúncio: " . $e->getMessage());
                continue;
            }
        }
        
        $bar->finish();
        
        $this->info("\n{$created} anúncios de exemplo foram criados com sucesso!");
        
        return 0;
    }
    
    /**
     * Gera um título para o anúncio com base no veículo
     */
    private function generateTitle($vehicle): string
    {
        $brand = $vehicle->brand->name ?? 'Veículo';
        $model = $vehicle->model ?? 'Usado';
        $year = $vehicle->year_manufacture ?? '';
        
        $templates = [
            "{brand} {model} {year} - Excelente estado",
            "{brand} {model} {year} - Único dono",
            "Vendo {brand} {model} {year} - Impecável",
            "{brand} {model} {year} - Completo e revisado",
            "{brand} {model} {year} - Toda revisão em dia",
        ];
        
        $template = $templates[array_rand($templates)];
        
        return str_replace(
            ['{brand}', '{model}', '{year}'],
            [$brand, $model, $year],
            $template
        );
    }
    
    /**
     * Gera uma descrição para o anúncio
     */
    private function generateDescription($vehicle): string
    {
        $brand = $vehicle->brand->name ?? 'Veículo';
        $model = $vehicle->model ?? 'usado';
        $year = $vehicle->year_manufacture ?? '';
        
        $descriptions = [
            "Excelente {$brand} {$model} {$year}, em perfeito estado de conservação, toda revisão em dia, único dono, documento pago, não precisa de nada, apenas abastecer e sair rodando.",
            "Vendo meu {$brand} {$model} {$year} em ótimo estado, completo, com ar condicionado, direção hidráulica, vidros e travas elétricas, alarme, som, rodas de liga leve, pneus em bom estado, revisões em dia, nunca sofreu acidentes, pintura original, único dono, documento em dia, aceito troca por veículo de menor valor.",
            "{$brand} {$model} {$year}, completo, revisões na concessionária, pneus novos, suspensão revisada, motor e câmbio em perfeito estado, lataria e pintura originais, sem batidas ou amassados, documentação em dia, IPVA pago, pronto para transferência.",
            "Vendo meu {$brand} {$model} {$year}, carro muito bem conservado, mecânica impecável, interior em ótimo estado, sem barulhos estranhos, ar condicionado gelando, direção hidráulica, vidros e travas elétricas, alarme, som original, rodas de liga leve, pneus com boa vida útil ainda, revisões sempre em dia, nunca deu problema, vendo para comprar um carro maior.",
            "{$brand} {$model} {$year} em excelente estado, único dono, todas as revisões realizadas, pneus novos, suspensão revisada, motor e câmbio em perfeito estado, lataria e pintura originais, sem batidas ou amassados, documentação em dia, IPVA pago, pronto para transferência.",
        ];
        
        return $descriptions[array_rand($descriptions)];
    }
    
    /**
     * Gera um preço com base no preço do veículo
     */
    private function generatePrice($basePrice): float
    {
        if ($basePrice > 0) {
            // Variação de -10% a +10% do preço base
            $variation = rand(-10, 10) / 100;
            return round($basePrice * (1 + $variation), 2);
        }
        
        // Se não houver preço base, gera um valor aleatório entre 10.000 e 200.000
        return round(rand(10000, 200000) * 0.95, 2);
    }
    
    /**
     * Gera um número de telefone aleatório
     */
    private function generatePhone(): string
    {
        $prefixes = ['11', '12', '13', '14', '15', '16', '17', '18', '19', '21', '22', '24', '27', '28', '31', '32', '33', '34', '35', '37', '38', '41', '42', '43', '44', '45', '46', '47', '48', '49', '51', '53', '54', '55', '61', '62', '63', '64', '65', '66', '67', '68', '69', '71', '73', '74', '75', '77', '79', '81', '82', '83', '84', '85', '86', '87', '88', '89', '91', '92', '93', '94', '95', '96', '97', '98', '99'];
        
        $prefix = $prefixes[array_rand($prefixes)];
        $number = rand(80000000, 99999999);
        
        return "($prefix) 9{$number}";
    }
    
    /**
     * Gera uma localização aleatória
     */
    private function generateLocation(): string
    {
        $cities = [
            'São Paulo, SP', 'Rio de Janeiro, RJ', 'Belo Horizonte, MG', 'Porto Alegre, RS', 
            'Curitiba, PR', 'Brasília, DF', 'Salvador, BA', 'Fortaleza, CE', 'Recife, PE',
            'Manaus, AM', 'Belém, PA', 'Goiânia, GO', 'Campinas, SP', 'São Luís, MA',
            'Maceió, AL', 'João Pessoa, PB', 'Natal, RN', 'Teresina, PI', 'Aracaju, SE',
            'Florianópolis, SC', 'Vitória, ES', 'Cuiabá, MT', 'Campo Grande, MS', 'Boa Vista, RR',
            'Porto Velho, RO', 'Rio Branco, AC', 'Palmas, TO', 'Macapá, AP', 'Porto Alegre, RS',
            'São José dos Campos, SP', 'Ribeirão Preto, SP', 'Uberlândia, MG', 'Sorocaba, SP',
            'São José do Rio Preto, SP', 'São Bernardo do Campo, SP', 'Santos, SP', 'Mauá, SP',
            'São José dos Pinhais, PR', 'Caxias do Sul, RS', 'São João de Meriti, RJ', 'Betim, MG',
            'Juiz de Fora, MG', 'Contagem, MG', 'Joinville, SC', 'Aracaju, SE', 'Feira de Santana, BA',
            'Carapicuíba, SP', 'São Vicente, SP', 'Pelotas, RS', 'Canoas, RS', 'Vitória da Conquista, BA',
        ];
        
        return $cities[array_rand($cities)];
    }
    
    /**
     * Gera uma coordenada aleatória dentro de um intervalo
     */
    private function generateCoordinate($min, $max, $decimals = 6): float
    {
        $scale = pow(10, $decimals);
        return mt_rand($min * $scale, $max * $scale) / $scale;
    }
}
