<?php

namespace App\Http\Controllers\Public;

use App\Http\Controllers\Controller;
use App\Models\Category;
use Illuminate\Http\Request;
use Inertia\Inertia;

class CategoryController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $categories = Category::where('is_active', true)
            ->whereNull('parent_id')
            ->with(['children' => function($query) {
                $query->where('is_active', true);
            }])
            ->orderBy('order')
            ->get(['id', 'name', 'slug', 'icon']);

        return Inertia::render('Categories/Index', [
            'categories' => $categories
        ]);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $slug)
    {
        $category = Category::where('slug', $slug)
            ->where('is_active', true)
            ->with(['children' => function($query) {
                $query->where('is_active', true);
            }])
            ->firstOrFail();

        // Buscar veículos da categoria
        $vehicles = $category->vehicles()
            ->with(['brand', 'category', 'media'])
            ->where('status', 'published')
            ->latest()
            ->get();

        // Buscar peças da categoria
        $parts = $category->parts()
            ->with(['brand', 'category', 'images'])
            ->where('status', 'published')
            ->latest()
            ->get();

        // Combinar veículos e peças em uma única coleção
        $allListings = $vehicles->concat($parts)->sortByDesc('created_at');

        // Simular paginação manual
        $perPage = 12;
        $currentPage = request()->get('page', 1);
        $offset = ($currentPage - 1) * $perPage;
        $items = $allListings->slice($offset, $perPage)->values();

        $listings = new \Illuminate\Pagination\LengthAwarePaginator(
            $items,
            $allListings->count(),
            $perPage,
            $currentPage,
            ['path' => request()->url()]
        );

        return Inertia::render('Categories/Show', [
            'category' => $category,
            'listings' => $listings
        ]);
    }
}
