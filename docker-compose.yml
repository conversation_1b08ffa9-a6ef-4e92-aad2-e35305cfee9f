version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: loja-virtual-app
    restart: unless-stopped
    working_dir: /var/www/
    volumes:
      - .:/var/www
    networks:
      - loja-virtual-network
    depends_on:
      - db
      - redis
      - meilisearch
    ports:
      - "8000:8000"
    environment:
      - APP_ENV=local
      - APP_DEBUG=true
      - APP_KEY=
      - APP_URL=http://localhost:8000
      
      - DB_CONNECTION=mysql
      - DB_HOST=db
      - DB_PORT=3306
      - DB_DATABASE=loja_virtual
      - DB_USERNAME=loja_user
      - DB_PASSWORD=password
      
      - REDIS_HOST=redis
      - REDIS_PASSWORD=null
      - REDIS_PORT=6379
      
      - MEILISEARCH_HOST=http://meilisearch:7700
      - MEILISEARCH_KEY=masterKey
      
      - MAIL_MAILER=smtp
      - MAIL_HOST=mailhog
      - MAIL_PORT=1025
      - MAIL_USERNAME=null
      - MAIL_PASSWORD=null
      - MAIL_ENCRYPTION=null
      - MAIL_FROM_ADDRESS="<EMAIL>"
      - MAIL_FROM_NAME="${APP_NAME}"

  db:
    image: mysql:8.0
    container_name: loja-virtual-db
    restart: unless-stopped
    environment:
      MYSQL_DATABASE: loja_virtual
      MYSQL_USER: loja_user
      MYSQL_PASSWORD: password
      MYSQL_ROOT_PASSWORD: root
      SERVICE_TAGS: dev
      SERVICE_NAME: mysql
    volumes:
      - dbdata:/var/lib/mysql
    networks:
      - loja-virtual-network
    ports:
      - "3306:3306"
    command: --default-authentication-plugin=mysql_native_password

  redis:
    image: redis:alpine
    container_name: loja-virtual-redis
    restart: unless-stopped
    networks:
      - loja-virtual-network
    ports:
      - "6379:6379"
    volumes:
      - redisdata:/data

  meilisearch:
    image: getmeili/meilisearch:latest
    container_name: loja-virtual-meilisearch
    restart: unless-stopped
    networks:
      - loja-virtual-network
    environment:
      MEILI_MASTER_KEY: masterKey
      MEILI_ENV: development
    volumes:
      - meilisearch_data:/meili_data
    ports:
      - "7700:7700"

  mailhog:
    image: mailhog/mailhog:latest
    container_name: loja-virtual-mailhog
    restart: unless-stopped
    networks:
      - loja-virtual-network
    ports:
      - "1025:1025" # SMTP
      - "8025:8025" # Web UI

volumes:
  dbdata:
    driver: local
  redisdata:
    driver: local
  meilisearch_data:
    driver: local

networks:
  loja-virtual-network:
    driver: bridge
