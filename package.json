{"private": true, "type": "module", "scripts": {"build": "vite build", "build:ssr": "vite build && vite build --ssr", "dev": "vite", "format": "prettier --write resources/", "format:check": "prettier --check resources/", "lint": "eslint . --fix", "types": "tsc --noEmit"}, "devDependencies": {"@eslint/js": "^9.19.0", "@headlessui/react": "^2.2.8", "@heroicons/react": "^2.2.0", "@inertiajs/progress": "^0.2.7", "@inertiajs/react": "^2.1.7", "@laravel/vite-plugin-wayfinder": "^0.1.3", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/line-clamp": "^0.4.4", "@tailwindcss/typography": "^0.5.16", "@types/node": "^22.18.6", "@types/react": "^19.1.13", "@types/react-dom": "^19.1.9", "@types/react-router-dom": "^5.3.3", "autoprefixer": "^10.4.21", "eslint": "^9.17.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-react": "^7.37.3", "eslint-plugin-react-hooks": "^5.1.0", "postcss": "^8.5.6", "prettier": "^3.4.2", "prettier-plugin-organize-imports": "^4.1.0", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^4.1.13", "typescript": "^5.9.2", "typescript-eslint": "^8.23.0"}, "dependencies": {"@hookform/resolvers": "^5.2.2", "@inertiajs/inertia": "^0.11.1", "@radix-ui/react-accordion": "^1.2.12", "@radix-ui/react-alert-dialog": "^1.1.15", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-collapsible": "^1.1.12", "@radix-ui/react-context-menu": "^2.2.16", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-hover-card": "^1.1.15", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.16", "@radix-ui/react-navigation-menu": "^1.2.14", "@radix-ui/react-popover": "^1.1.15", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.8", "@radix-ui/react-scroll-area": "^1.2.10", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.6", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.6", "@radix-ui/react-tabs": "^1.1.13", "@radix-ui/react-toggle": "^1.1.10", "@radix-ui/react-toggle-group": "^1.1.11", "@radix-ui/react-tooltip": "^1.2.8", "@tailwindcss/vite": "^4.1.11", "@tanstack/react-table": "^8.21.3", "@vitejs/plugin-react": "^4.6.0", "axios": "^1.12.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "concurrently": "^9.0.1", "date-fns": "^4.1.0", "embla-carousel-react": "^8.6.0", "globals": "^15.14.0", "input-otp": "^1.4.2", "laravel-vite-plugin": "^2.0", "lucide-react": "^0.475.0", "next-themes": "^0.4.6", "radix-ui": "^1.4.3", "react": "^19.1.1", "react-day-picker": "^9.10.0", "react-dom": "^19.1.1", "react-dropzone": "^14.3.8", "react-hook-form": "^7.62.0", "react-icons": "^5.5.0", "react-resizable-panels": "^3.0.6", "recharts": "^2.15.4", "sonner": "^2.0.7", "swiper": "^12.0.2", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.2", "vite": "^7.0.4", "ziggy-js": "^2.6.0", "zod": "^4.1.9"}, "optionalDependencies": {"@rollup/rollup-linux-x64-gnu": "4.9.5", "@tailwindcss/oxide-linux-x64-gnu": "^4.0.1", "lightningcss-linux-x64-gnu": "^1.29.1"}}