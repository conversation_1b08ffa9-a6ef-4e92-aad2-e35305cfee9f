<?php

namespace App\Console\Commands;

use App\Models\Advertisement;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class CheckAdvertisementIntegrity extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'advertisements:check-integrity';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Verifica a integridade dos anúncios, identificando problemas potenciais';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Iniciando verificação de integridade dos anúncios...');
        
        $problems = [];
        $totalAds = Advertisement::count();
        
        $this->line("Verificando {$totalAds} anúncios...");
        $bar = $this->output->createProgressBar($totalAds);
        $bar->start();
        
        // Verificar anúncios sem veículo associado
        $adsWithoutVehicle = Advertisement::doesntHave('vehicle')
            ->select('id', 'title')
            ->get();
            
        if (!$adsWithoutVehicle->isEmpty()) {
            $problems[] = [
                'type' => 'Sem veículo',
                'count' => $adsWithoutVehicle->count(),
                'examples' => $adsWithoutVehicle->take(5)->pluck('id', 'title')->toArray(),
                'fix' => 'Use o comando: php artisan ads:fix-orphaned --action=delete',
            ];
        }
        
        // Verificar anúncios sem usuário
        $adsWithoutUser = Advertisement::doesntHave('user')
            ->select('id', 'title')
            ->get();
            
        if (!$adsWithoutUser->isEmpty()) {
            $problems[] = [
                'type' => 'Sem usuário',
                'count' => $adsWithoutUser->count(),
                'examples' => $adsWithoutUser->take(5)->pluck('id', 'title')->toArray(),
                'fix' => 'Use o comando: php artisan ads:fix-orphaned --action=assign --user-id=ID_DO_USUARIO',
            ];
        }
        
        // Verificar anúncios publicados sem data de publicação
        $publishedWithoutDate = Advertisement::where('status', 'published')
            ->whereNull('published_at')
            ->select('id', 'title')
            ->get();
            
        if (!$publishedWithoutDate->isEmpty()) {
            $problems[] = [
                'type' => 'Publicados sem data de publicação',
                'count' => $publishedWithoutDate->count(),
                'examples' => $publishedWithoutDate->take(5)->pluck('id', 'title')->toArray(),
                'fix' => 'Use o comando: php artisan ads:fix-dates',
            ];
        }
        
        // Verificar anúncios expirados que não estão marcados como expirados
        $expiredButNotMarked = Advertisement::where('expires_at', '<', now())
            ->where('status', '!=', 'expired')
            ->select('id', 'title', 'expires_at')
            ->get();
            
        if (!$expiredButNotMarked->isEmpty()) {
            $problems[] = [
                'type' => 'Expirados não marcados',
                'count' => $expiredButNotMarked->count(),
                'examples' => $expiredButNotMarked->take(5)->mapWithKeys(function($item) {
                    return [$item->title => "ID: {$item->id} (Expiração: {$item->expires_at->format('d/m/Y')})"];
                })->toArray(),
                'fix' => 'Execute: php artisan advertisements:expire',
            ];
        }
        
        // Verificar anúncios com status inválido
        $invalidStatus = DB::table('advertisements')
            ->select('status', DB::raw('count(*) as count'))
            ->whereNotIn('status', [
                'draft', 'pending_review', 'approved', 
                'published', 'rejected', 'expired', 'sold'
            ])
            ->groupBy('status')
            ->get();
            
        if (!$invalidStatus->isEmpty()) {
            $problems[] = [
                'type' => 'Status inválido',
                'count' => $invalidStatus->sum('count'),
                'examples' => $invalidStatus->pluck('count', 'status')->toArray(),
                'fix' => 'Atualize manualmente os status inválidos para um dos valores permitidos.',
            ];
        }
        
        $bar->finish();
        $this->newLine(2);
        
        // Exibir relatório
        if (empty($problems)) {
            $this->info('✅ Nenhum problema encontrado. Todos os anúncios estão íntegros!');
            return 0;
        }
        
        $this->warn("⚠️  Foram encontrados " . count($problems) . " tipos de problemas:");
        
        $rows = [];
        foreach ($problems as $problem) {
            $examples = [];
            foreach ($problem['examples'] as $title => $id) {
                $examples[] = "- {$title} ({$id})";
                if (count($examples) >= 3) {
                    $examples[] = '...'; // Limitar a 3 exemplos
                    break;
                }
            }
            
            $rows[] = [
                $problem['type'],
                $problem['count'],
                implode("\n", $examples),
                $problem['fix']
            ];
        }
        
        $this->table(
            ['Tipo de Problema', 'Quantidade', 'Exemplos', 'Como Corrigir'],
            $rows
        );
        
        $this->warn('Recomendações:');
        $this->line('1. Execute os comandos de correção sugeridos para cada problema.');
        $this->line('2. Verifique os logs do sistema para mais detalhes.');
        $this->line('3. Faça backup do banco de dados antes de executar correções em massa.');
        
        Log::warning('Verificação de integridade encontrou problemas', [
            'total_problems' => count($problems),
            'problems' => $problems,
        ]);
        
        return count($problems); // Retorna o número de problemas encontrados
    }
}
