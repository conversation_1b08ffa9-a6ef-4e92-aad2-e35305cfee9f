<?php

namespace App\Notifications;

use App\Models\Advertisement;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class AdvertisementPublishedNotification extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new notification instance.
     */
    public function __construct(public Advertisement $advertisement)
    {
        //
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable)
    {
        return (new MailMessage)
                    ->subject('Seu anúncio está publicado!')
                    ->line('Seu anúncio foi publicado com sucesso e já está visível para todos os usuários.')
                    ->line('Detalhes do anúncio:')
                    ->line('- Título: ' . $this->advertisement->title)
                    ->line('- Preço: ' . number_format($this->advertisement->price, 2, ',', '.'))
                    ->line('- Data de expiração: ' . $this->advertisement->expires_at->format('d/m/Y'))
                    ->action('Ver Anúncio', route('advertisements.show', $this->advertisement))
                    ->line('Obrigado por usar nossa plataforma!');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'message' => 'Seu anúncio foi publicado com sucesso!',
            'link' => route('advertisements.show', $this->advertisement),
            'advertisement_id' => $this->advertisement->id,
            'advertisement_title' => $this->advertisement->title,
        ];
    }
}
