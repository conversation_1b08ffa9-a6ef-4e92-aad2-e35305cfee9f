/.phpunit.cache
/bootstrap/ssr
/node_modules
/public/build
/public/hot
/public/storage
/resources/js/actions
/resources/js/routes
/resources/js/wayfinder
/storage/*.key
/storage/pail
/vendor
.env
.env.backup
.env.production
.phpactor.json
.phpunit.result.cache
Homestead.json
Homestead.yaml
npm-debug.log
yarn-error.log
/auth.json
/.fleet
/.idea
/.nova
/.vscode
/.zed

# Docker
/docker/start-container.sh
!.gitkeep

# Arquivos de configuração local
.phpunit.result.cache
.php-cs-fixer.cache
.php-cs-fixer.php
.phpstorm.meta.php

# Logs
*.log

# IDE
.idea/
.vscode/
*.swp
*.swo

# macOS
.DS_Store
.AppleDouble
.LSOverride

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/

# Composer
composer.phar
composer.lock

# Node
package-lock.json
yarn.lock

# Editor directories and files
.idea
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Arquivos de ambiente local
.env.local
.env.*.local
