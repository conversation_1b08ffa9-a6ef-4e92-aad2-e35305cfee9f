<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class Image extends Model
{
    use SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'path',
        'url',
        'mime_type',
        'size',
        'width',
        'height',
        'alt_text',
        'title',
        'description',
        'is_main',
        'order',
        'imageable_type',
        'imageable_id',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'size' => 'integer',
        'width' => 'integer',
        'height' => 'integer',
        'is_main' => 'boolean',
        'order' => 'integer',
    ];

    /**
     * The model's default values for attributes.
     *
     * @var array
     */
    protected $attributes = [
        'is_main' => false,
        'order' => 0,
    ];

    /**
     * Get the parent imageable model (vehicle, part, etc.).
     */
    public function imageable(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Scope a query to only include main images.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeMain($query)
    {
        return $query->where('is_main', true);
    }

    /**
     * Get the image URL.
     *
     * @return string
     */
    public function getImageUrl(): string
    {
        return $this->url ?: asset('storage/' . $this->path);
    }

    /**
     * Get the image thumbnail URL.
     *
     * @return string
     */
    public function getThumbnailUrl(): string
    {
        $pathInfo = pathinfo($this->path);
        $thumbnailPath = $pathInfo['dirname'] . '/thumbs/' . $pathInfo['filename'] . '.' . $pathInfo['extension'];
        
        return file_exists(storage_path('app/public/' . $thumbnailPath)) 
            ? asset('storage/' . $thumbnailPath)
            : $this->getImageUrl();
    }
}
