import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select';
import MainLayout from '@/layouts/MainLayout';
import { PageProps } from '@/types';
import { Head, Link, router } from '@inertiajs/react';
import { 
    Plus, 
    X, 
    Search, 
    ArrowLeftRight, 
    Car, 
    Calendar, 
    Gauge, 
    Fuel, 
    Settings, 
    MapPin,
    User,
    Phone,
    Mail,
    Heart,
    MessageCircle
} from 'lucide-react';
import { useState } from 'react';

interface Advertisement {
    id: number;
    title: string;
    price: number;
    location: string;
    vehicle: {
        brand: {
            name: string;
        };
        category: {
            name: string;
        };
        model: string;
        year: number;
        mileage: number;
        fuel_type: string;
        transmission: string;
        condition: string;
        color: string;
        doors: string;
        engine_size?: string;
    };
    featured_image?: {
        url: string;
    };
    user: {
        id: number;
        name: string;
        phone?: string;
        email: string;
    };
}

interface ComparisonIndexProps extends PageProps {
    advertisements: Advertisement[];
    advertisementIds: string[];
}

export default function ComparisonIndex({ advertisements, advertisementIds }: ComparisonIndexProps) {
    const [searchQuery, setSearchQuery] = useState('');
    const [isSearching, setIsSearching] = useState(false);

    const maxSlots = 4;
    const emptySlots = maxSlots - advertisements.length;

    const handleRemoveFromComparison = (advertisementId: number) => {
        const newIds = advertisementIds.filter(id => id !== advertisementId.toString());
        const params = newIds.length > 0 ? `?ads=${newIds.join(',')}` : '';
        router.get(`/comparacao${params}`);
    };

    const handleClearComparison = () => {
        router.get('/comparacao');
    };

    const handleAddToComparison = () => {
        setIsSearching(true);
        // Aqui você implementaria um modal ou redirecionamento para busca
        // Por simplicidade, vamos redirecionar para a página de anúncios
        router.get('/anuncios');
    };

    const formatPrice = (price: number) => {
        return new Intl.NumberFormat('pt-BR', {
            style: 'currency',
            currency: 'BRL',
        }).format(price);
    };

    const formatMileage = (mileage: number) => {
        return new Intl.NumberFormat('pt-BR').format(mileage) + ' km';
    };

    const getFuelTypeLabel = (fuelType: string) => {
        const labels: Record<string, string> = {
            'gasoline': 'Gasolina',
            'ethanol': 'Etanol',
            'flex': 'Flex',
            'diesel': 'Diesel',
            'electric': 'Elétrico',
            'hybrid': 'Híbrido',
        };
        return labels[fuelType] || fuelType;
    };

    const getTransmissionLabel = (transmission: string) => {
        const labels: Record<string, string> = {
            'manual': 'Manual',
            'automatic': 'Automático',
            'cvt': 'CVT',
        };
        return labels[transmission] || transmission;
    };

    const getConditionLabel = (condition: string) => {
        const labels: Record<string, string> = {
            'new': 'Novo',
            'used': 'Usado',
            'certified': 'Seminovo certificado',
        };
        return labels[condition] || condition;
    };

    return (
        <MainLayout>
            <Head title="Comparar Veículos" />
            
            <div className="min-h-screen bg-gray-50">
                <div className="container mx-auto px-4 py-6">
                    {/* Header */}
                    <div className="mb-6">
                        <h1 className="text-3xl font-bold flex items-center gap-2">
                            <ArrowLeftRight className="h-8 w-8 text-blue-500" />
                            Comparar Veículos
                        </h1>
                        <p className="text-gray-600">
                            Compare até 4 veículos lado a lado para tomar a melhor decisão
                        </p>
                    </div>

                    {/* Toolbar */}
                    <Card className="mb-6">
                        <CardContent className="py-4">
                            <div className="flex items-center justify-between">
                                <div className="flex items-center gap-4">
                                    <span className="text-sm text-gray-600">
                                        {advertisements.length} de {maxSlots} veículos selecionados
                                    </span>
                                    
                                    {advertisements.length > 0 && (
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={handleClearComparison}
                                        >
                                            Limpar comparação
                                        </Button>
                                    )}
                                </div>

                                <div className="flex items-center gap-2">
                                    <Button
                                        onClick={handleAddToComparison}
                                        disabled={advertisements.length >= maxSlots}
                                    >
                                        <Plus className="mr-2 h-4 w-4" />
                                        Adicionar veículo
                                    </Button>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Comparison Grid */}
                    {advertisements.length === 0 ? (
                        <Card>
                            <CardContent className="py-12 text-center">
                                <ArrowLeftRight className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                                <h3 className="text-lg font-medium text-gray-900 mb-2">
                                    Nenhum veículo selecionado
                                </h3>
                                <p className="text-gray-600 mb-4">
                                    Adicione veículos para compará-los lado a lado e encontrar o melhor para você.
                                </p>
                                <Link href="/anuncios">
                                    <Button>
                                        <Search className="mr-2 h-4 w-4" />
                                        Buscar veículos
                                    </Button>
                                </Link>
                            </CardContent>
                        </Card>
                    ) : (
                        <div className="overflow-x-auto">
                            <div className="min-w-max">
                                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                                    {/* Render advertisements */}
                                    {advertisements.map((ad) => (
                                        <Card key={ad.id} className="w-80">
                                            <CardHeader className="relative">
                                                <Button
                                                    variant="ghost"
                                                    size="sm"
                                                    className="absolute top-2 right-2 h-8 w-8 p-0"
                                                    onClick={() => handleRemoveFromComparison(ad.id)}
                                                >
                                                    <X className="h-4 w-4" />
                                                </Button>
                                                
                                                <img
                                                    src={ad.featured_image?.url || '/placeholder-car.jpg'}
                                                    alt={ad.title}
                                                    className="w-full h-48 object-cover rounded-lg"
                                                />
                                            </CardHeader>
                                            
                                            <CardContent className="space-y-4">
                                                <div>
                                                    <h3 className="font-bold text-lg line-clamp-2">{ad.title}</h3>
                                                    <p className="text-2xl font-bold text-green-600">
                                                        {formatPrice(ad.price)}
                                                    </p>
                                                </div>

                                                {/* Vehicle Details */}
                                                <div className="space-y-3">
                                                    <div className="flex items-center gap-2 text-sm">
                                                        <Car className="h-4 w-4 text-gray-400" />
                                                        <span>{ad.vehicle.brand.name} {ad.vehicle.model}</span>
                                                    </div>
                                                    
                                                    <div className="flex items-center gap-2 text-sm">
                                                        <Calendar className="h-4 w-4 text-gray-400" />
                                                        <span>{ad.vehicle.year}</span>
                                                    </div>
                                                    
                                                    <div className="flex items-center gap-2 text-sm">
                                                        <Gauge className="h-4 w-4 text-gray-400" />
                                                        <span>{formatMileage(ad.vehicle.mileage)}</span>
                                                    </div>
                                                    
                                                    <div className="flex items-center gap-2 text-sm">
                                                        <Fuel className="h-4 w-4 text-gray-400" />
                                                        <span>{getFuelTypeLabel(ad.vehicle.fuel_type)}</span>
                                                    </div>
                                                    
                                                    <div className="flex items-center gap-2 text-sm">
                                                        <Settings className="h-4 w-4 text-gray-400" />
                                                        <span>{getTransmissionLabel(ad.vehicle.transmission)}</span>
                                                    </div>
                                                    
                                                    <div className="flex items-center gap-2 text-sm">
                                                        <MapPin className="h-4 w-4 text-gray-400" />
                                                        <span>{ad.location}</span>
                                                    </div>
                                                </div>

                                                {/* Additional Details */}
                                                <div className="border-t pt-3 space-y-2">
                                                    <div className="grid grid-cols-2 gap-2 text-sm">
                                                        <div>
                                                            <span className="text-gray-600">Condição:</span>
                                                            <p className="font-medium">{getConditionLabel(ad.vehicle.condition)}</p>
                                                        </div>
                                                        <div>
                                                            <span className="text-gray-600">Cor:</span>
                                                            <p className="font-medium">{ad.vehicle.color}</p>
                                                        </div>
                                                        <div>
                                                            <span className="text-gray-600">Portas:</span>
                                                            <p className="font-medium">{ad.vehicle.doors}</p>
                                                        </div>
                                                        {ad.vehicle.engine_size && (
                                                            <div>
                                                                <span className="text-gray-600">Motor:</span>
                                                                <p className="font-medium">{ad.vehicle.engine_size}</p>
                                                            </div>
                                                        )}
                                                    </div>
                                                </div>

                                                {/* Seller Info */}
                                                <div className="border-t pt-3">
                                                    <div className="flex items-center gap-2 text-sm mb-2">
                                                        <User className="h-4 w-4 text-gray-400" />
                                                        <span className="font-medium">{ad.user.name}</span>
                                                    </div>
                                                </div>

                                                {/* Actions */}
                                                <div className="space-y-2">
                                                    <Link href={`/anuncio/${ad.id}`}>
                                                        <Button className="w-full">
                                                            Ver detalhes
                                                        </Button>
                                                    </Link>
                                                    
                                                    <div className="grid grid-cols-2 gap-2">
                                                        <Button variant="outline" size="sm">
                                                            <Heart className="mr-1 h-4 w-4" />
                                                            Favoritar
                                                        </Button>
                                                        
                                                        <Button variant="outline" size="sm">
                                                            <MessageCircle className="mr-1 h-4 w-4" />
                                                            Conversar
                                                        </Button>
                                                    </div>
                                                </div>
                                            </CardContent>
                                        </Card>
                                    ))}

                                    {/* Empty slots */}
                                    {Array.from({ length: emptySlots }).map((_, index) => (
                                        <Card key={`empty-${index}`} className="w-80 border-dashed">
                                            <CardContent className="py-12 text-center">
                                                <Plus className="mx-auto h-8 w-8 text-gray-400 mb-2" />
                                                <p className="text-gray-500 text-sm mb-4">
                                                    Adicionar veículo para comparar
                                                </p>
                                                <Button
                                                    variant="outline"
                                                    onClick={handleAddToComparison}
                                                >
                                                    <Search className="mr-2 h-4 w-4" />
                                                    Buscar
                                                </Button>
                                            </CardContent>
                                        </Card>
                                    ))}
                                </div>
                            </div>
                        </div>
                    )}

                    {/* Tips */}
                    {advertisements.length > 0 && (
                        <Card className="mt-6">
                            <CardHeader>
                                <CardTitle>Dicas para comparação</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-sm">
                                    <div>
                                        <h4 className="font-medium mb-1">💰 Preço</h4>
                                        <p className="text-gray-600">Compare os preços e considere o custo-benefício de cada veículo.</p>
                                    </div>
                                    <div>
                                        <h4 className="font-medium mb-1">📅 Ano</h4>
                                        <p className="text-gray-600">Veículos mais novos geralmente têm menos desgaste e mais tecnologia.</p>
                                    </div>
                                    <div>
                                        <h4 className="font-medium mb-1">🛣️ Quilometragem</h4>
                                        <p className="text-gray-600">Menor quilometragem pode indicar menos desgaste do veículo.</p>
                                    </div>
                                    <div>
                                        <h4 className="font-medium mb-1">⛽ Combustível</h4>
                                        <p className="text-gray-600">Considere o tipo de combustível e o consumo para seu uso diário.</p>
                                    </div>
                                    <div>
                                        <h4 className="font-medium mb-1">⚙️ Transmissão</h4>
                                        <p className="text-gray-600">Escolha entre manual (mais econômico) ou automático (mais conforto).</p>
                                    </div>
                                    <div>
                                        <h4 className="font-medium mb-1">📍 Localização</h4>
                                        <p className="text-gray-600">Considere a distância para visualização e possíveis custos de transporte.</p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    )}
                </div>
            </div>
        </MainLayout>
    );
}
