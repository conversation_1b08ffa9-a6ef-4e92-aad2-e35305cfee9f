<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rule;
use Inertia\Inertia;

class ProfileController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Display user profile.
     */
    public function show()
    {
        $user = auth()->user();
        $user->load(['vehicles', 'parts']);

        $stats = [
            'total_vehicles' => $user->vehicles()->count(),
            'active_vehicles' => $user->vehicles()->where('status', 'published')->count(),
            'total_parts' => $user->parts()->count(),
            'active_parts' => $user->parts()->where('status', 'active')->count(),
            'total_views' => $user->vehicles()->sum('views') + $user->parts()->sum('views'),
        ];

        return Inertia::render('User/Profile/Show', [
            'user' => $user,
            'stats' => $stats,
        ]);
    }

    /**
     * Show the form for editing the profile.
     */
    public function edit()
    {
        return Inertia::render('User/Profile/Edit', [
            'user' => auth()->user(),
        ]);
    }

    /**
     * Update the user's profile information.
     */
    public function update(Request $request)
    {
        $user = auth()->user();

        $request->validate([
            'name' => 'required|string|max:255',
            'email' => ['required', 'email', Rule::unique('users')->ignore($user->id)],
            'phone' => 'nullable|string|max:20',
            'cpf_cnpj' => ['nullable', 'string', 'max:20', Rule::unique('users')->ignore($user->id)],
            'type' => 'required|in:individual,company',
            'birth_date' => 'nullable|date',
            'company_name' => 'nullable|string|max:255',
            'trading_name' => 'nullable|string|max:255',
            'state_registration' => 'nullable|string|max:50',
            'corporate_document' => 'nullable|string|max:50',
            'website' => 'nullable|url|max:255',
            'bio' => 'nullable|string|max:1000',
        ]);

        $user->update($request->only([
            'name', 'email', 'phone', 'cpf_cnpj', 'type', 'birth_date',
            'company_name', 'trading_name', 'state_registration', 
            'corporate_document', 'website', 'bio'
        ]));

        return back()->with('success', 'Perfil atualizado com sucesso!');
    }

    /**
     * Update the user's password.
     */
    public function updatePassword(Request $request)
    {
        $request->validate([
            'current_password' => 'required|current_password',
            'password' => 'required|string|min:8|confirmed',
        ]);

        auth()->user()->update([
            'password' => Hash::make($request->password),
        ]);

        return back()->with('success', 'Senha atualizada com sucesso!');
    }

    /**
     * Update the user's avatar.
     */
    public function updateAvatar(Request $request)
    {
        $request->validate([
            'avatar' => 'required|image|mimes:jpeg,png,jpg|max:2048',
        ]);

        $user = auth()->user();

        if ($request->hasFile('avatar')) {
            // Remove avatar anterior se existir
            if ($user->avatar) {
                $user->clearMediaCollection('avatar');
            }

            // Adicionar novo avatar
            $user->addMediaFromRequest('avatar')
                ->toMediaCollection('avatar');

            // Atualizar campo avatar com URL
            $avatarUrl = $user->getFirstMediaUrl('avatar');
            $user->update(['avatar' => $avatarUrl]);
        }

        return back()->with('success', 'Avatar atualizado com sucesso!');
    }

    /**
     * Update notification preferences.
     */
    public function updateNotifications(Request $request)
    {
        $request->validate([
            'email_notifications' => 'boolean',
            'sms_notifications' => 'boolean',
            'marketing_emails' => 'boolean',
            'new_message_email' => 'boolean',
            'new_offer_email' => 'boolean',
        ]);

        $preferences = $request->only([
            'email_notifications', 'sms_notifications', 'marketing_emails',
            'new_message_email', 'new_offer_email'
        ]);

        auth()->user()->update([
            'notification_preferences' => $preferences,
        ]);

        return back()->with('success', 'Preferências de notificação atualizadas!');
    }

    /**
     * Delete the user's account.
     */
    public function destroy(Request $request)
    {
        $request->validate([
            'password' => 'required|current_password',
        ]);

        $user = auth()->user();

        // Verificar se o usuário tem anúncios ativos
        if ($user->vehicles()->where('status', 'published')->exists() || 
            $user->parts()->where('status', 'active')->exists()) {
            return back()->with('error', 'Não é possível excluir conta com anúncios ativos. Remova ou desative todos os anúncios primeiro.');
        }

        // Logout
        auth()->logout();

        // Deletar conta
        $user->delete();

        return redirect()->route('home')->with('success', 'Conta excluída com sucesso!');
    }

    /**
     * Show user's public profile.
     */
    public function publicProfile($id)
    {
        $user = User::with(['vehicles' => function($query) {
                $query->where('status', 'published')->latest()->take(6);
            }, 'parts' => function($query) {
                $query->where('status', 'active')->latest()->take(6);
            }])
            ->findOrFail($id);

        $stats = [
            'total_vehicles' => $user->vehicles()->where('status', 'published')->count(),
            'total_parts' => $user->parts()->where('status', 'active')->count(),
            'member_since' => $user->created_at->format('Y'),
        ];

        return Inertia::render('User/Profile/Public', [
            'seller' => $user,
            'stats' => $stats,
        ]);
    }
}
