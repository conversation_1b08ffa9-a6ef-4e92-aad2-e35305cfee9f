import { Button } from '@/components/ui/button';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card';
import MainLayout from '@/layouts/MainLayout';
import { Category, PageProps } from '@/types';
import { Head, Link } from '@inertiajs/react';

interface Listing {
    id: number;
    title: string;
    price: number;
    location: string;
    created_at: string;
    main_image?: {
        url: string;
    };
    category: {
        name: string;
    };
    brand: {
        name: string;
    };
}

interface PaginatedListings {
    data: Listing[];
    links: Array<{
        url: string | null;
        label: string;
        active: boolean;
    }>;
}

interface CategoryShowProps extends PageProps {
    category: Category;
    listings: PaginatedListings;
}

export default function CategoryShow({
    category,
    listings,
}: CategoryShowProps) {
    return (
        <MainLayout>
            <Head title={category.name} />

            <div className="container mx-auto px-4 py-8">
                <div className="mb-8">
                    <h1 className="mb-2 text-3xl font-bold">{category.name}</h1>
                    {category.description && (
                        <p className="text-muted-foreground">
                            {category.description}
                        </p>
                    )}
                </div>

                {category.children && category.children.length > 0 && (
                    <div className="mb-8">
                        <h2 className="mb-4 text-xl font-semibold">
                            Subcategorias
                        </h2>
                        <div className="grid grid-cols-2 gap-4 md:grid-cols-3 lg:grid-cols-4">
                            {category.children.map((subcategory) => (
                                <Link
                                    key={subcategory.id}
                                    href={`/pesquisar?category_id=${subcategory.id}`}
                                    className="block"
                                >
                                    <Card className="h-full transition-colors hover:bg-accent/50">
                                        <CardHeader className="pb-2">
                                            <CardTitle className="text-lg">
                                                {subcategory.name}
                                            </CardTitle>
                                        </CardHeader>
                                        <CardContent>
                                            <Button
                                                variant="link"
                                                className="h-auto p-0"
                                            >
                                                Ver anúncios
                                            </Button>
                                        </CardContent>
                                    </Card>
                                </Link>
                            ))}
                        </div>
                    </div>
                )}

                <div>
                    <div className="mb-6 flex items-center justify-between">
                        <h2 className="text-2xl font-bold">
                            Anúncios em {category.name}
                        </h2>
                        <div className="flex space-x-2">
                            <Button variant="outline">Ordenar por</Button>
                            <Button variant="outline">Filtrar</Button>
                        </div>
                    </div>

                    {listings.data.length > 0 ? (
                        <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
                            {listings.data.map((listing) => (
                                <div
                                    key={listing.id}
                                    className="overflow-hidden rounded-lg border transition-shadow hover:shadow-md"
                                >
                                    <div className="relative aspect-video bg-muted">
                                        {listing.main_image && (
                                            <img
                                                src={listing.main_image.url}
                                                alt={listing.title}
                                                className="h-full w-full object-cover"
                                            />
                                        )}
                                    </div>
                                    <div className="p-4">
                                        <h3 className="mb-1 text-lg font-semibold">
                                            {listing.title}
                                        </h3>
                                        <p className="mb-2 text-lg font-bold text-primary">
                                            {new Intl.NumberFormat('pt-BR', {
                                                style: 'currency',
                                                currency: 'BRL',
                                            }).format(listing.price)}
                                        </p>
                                        <div className="flex items-center text-sm text-muted-foreground">
                                            <span>{listing.location}</span>
                                            <span className="mx-2">•</span>
                                            <span>
                                                {new Date(
                                                    listing.created_at,
                                                ).toLocaleDateString()}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    ) : (
                        <div className="py-12 text-center">
                            <p className="mb-4 text-muted-foreground">
                                Nenhum anúncio encontrado nesta categoria.
                            </p>
                            <Button asChild>
                                <Link href="/anunciar">Anunciar agora</Link>
                            </Button>
                        </div>
                    )}

                    {listings.links.length > 3 && (
                        <div className="mt-8 flex justify-center">
                            <nav className="flex items-center space-x-2">
                                {listings.links.map((link, index) => (
                                    <Link
                                        key={index}
                                        href={link.url || '#'}
                                        className={`rounded-md px-4 py-2 ${
                                            link.active
                                                ? 'bg-primary text-primary-foreground'
                                                : 'hover:bg-accent'
                                        } ${!link.url ? 'cursor-not-allowed opacity-50' : ''}`}
                                        dangerouslySetInnerHTML={{
                                            __html: link.label,
                                        }}
                                    />
                                ))}
                            </nav>
                        </div>
                    )}
                </div>
            </div>
        </MainLayout>
    );
}
