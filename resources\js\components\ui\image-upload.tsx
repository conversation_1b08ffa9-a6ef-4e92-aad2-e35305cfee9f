import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { cn } from '@/lib/utils';
import { Upload, X, Image as ImageIcon, Eye } from 'lucide-react';
import { useCallback, useState } from 'react';
import { useDropzone } from 'react-dropzone';

interface ImageFile {
    id?: string;
    file?: File;
    url: string;
    name: string;
    size?: number;
    preview?: string;
}

interface ImageUploadProps {
    value?: ImageFile[];
    onChange: (files: ImageFile[]) => void;
    maxFiles?: number;
    maxSize?: number; // in MB
    accept?: string[];
    className?: string;
    disabled?: boolean;
    showPreview?: boolean;
    allowReorder?: boolean;
}

export function ImageUpload({
    value = [],
    onChange,
    maxFiles = 10,
    maxSize = 5,
    accept = ['image/jpeg', 'image/png', 'image/jpg', 'image/webp'],
    className,
    disabled = false,
    showPreview = true,
    allowReorder = true,
}: ImageUploadProps) {
    const [draggedIndex, setDraggedIndex] = useState<number | null>(null);

    const onDrop = useCallback(
        (acceptedFiles: File[]) => {
            const newFiles: ImageFile[] = acceptedFiles.map((file) => ({
                file,
                url: URL.createObjectURL(file),
                name: file.name,
                size: file.size,
                preview: URL.createObjectURL(file),
            }));

            const updatedFiles = [...value, ...newFiles].slice(0, maxFiles);
            onChange(updatedFiles);
        },
        [value, onChange, maxFiles],
    );

    const { getRootProps, getInputProps, isDragActive } = useDropzone({
        onDrop,
        accept: accept.reduce((acc, type) => ({ ...acc, [type]: [] }), {}),
        maxSize: maxSize * 1024 * 1024,
        disabled: disabled || value.length >= maxFiles,
        multiple: true,
    });

    const removeFile = (index: number) => {
        const newFiles = value.filter((_, i) => i !== index);
        onChange(newFiles);
    };

    const moveFile = (fromIndex: number, toIndex: number) => {
        if (!allowReorder) return;

        const newFiles = [...value];
        const [movedFile] = newFiles.splice(fromIndex, 1);
        newFiles.splice(toIndex, 0, movedFile);
        onChange(newFiles);
    };

    const handleDragStart = (index: number) => {
        setDraggedIndex(index);
    };

    const handleDragOver = (e: React.DragEvent, index: number) => {
        e.preventDefault();
        if (draggedIndex !== null && draggedIndex !== index) {
            moveFile(draggedIndex, index);
            setDraggedIndex(index);
        }
    };

    const handleDragEnd = () => {
        setDraggedIndex(null);
    };

    return (
        <div className={cn('space-y-4', className)}>
            {/* Upload Area */}
            {value.length < maxFiles && (
                <Card
                    {...getRootProps()}
                    className={cn(
                        'border-2 border-dashed transition-colors cursor-pointer',
                        isDragActive
                            ? 'border-primary bg-primary/5'
                            : 'border-muted-foreground/25 hover:border-primary/50',
                        disabled && 'opacity-50 cursor-not-allowed',
                    )}
                >
                    <CardContent className="flex flex-col items-center justify-center p-8 text-center">
                        <input {...getInputProps()} />
                        <Upload className="h-10 w-10 text-muted-foreground mb-4" />
                        <div className="space-y-2">
                            <p className="text-sm font-medium">
                                {isDragActive
                                    ? 'Solte as imagens aqui'
                                    : 'Clique ou arraste imagens para fazer upload'}
                            </p>
                            <p className="text-xs text-muted-foreground">
                                Máximo {maxFiles} imagens, até {maxSize}MB cada
                            </p>
                            <p className="text-xs text-muted-foreground">
                                Formatos: {accept.map(type => type.split('/')[1]).join(', ')}
                            </p>
                        </div>
                    </CardContent>
                </Card>
            )}

            {/* Preview Grid */}
            {showPreview && value.length > 0 && (
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                    {value.map((file, index) => (
                        <Card
                            key={file.url}
                            className={cn(
                                'relative group overflow-hidden',
                                allowReorder && 'cursor-move',
                                draggedIndex === index && 'opacity-50',
                            )}
                            draggable={allowReorder}
                            onDragStart={() => handleDragStart(index)}
                            onDragOver={(e) => handleDragOver(e, index)}
                            onDragEnd={handleDragEnd}
                        >
                            <div className="aspect-square relative">
                                {file.preview || file.url ? (
                                    <img
                                        src={file.preview || file.url}
                                        alt={file.name}
                                        className="w-full h-full object-cover"
                                    />
                                ) : (
                                    <div className="w-full h-full bg-muted flex items-center justify-center">
                                        <ImageIcon className="h-8 w-8 text-muted-foreground" />
                                    </div>
                                )}

                                {/* Overlay */}
                                <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2">
                                    <Button
                                        size="sm"
                                        variant="secondary"
                                        className="h-8 w-8 p-0"
                                        onClick={() => {
                                            // Open preview modal
                                            window.open(file.preview || file.url, '_blank');
                                        }}
                                    >
                                        <Eye className="h-4 w-4" />
                                    </Button>
                                    <Button
                                        size="sm"
                                        variant="destructive"
                                        className="h-8 w-8 p-0"
                                        onClick={() => removeFile(index)}
                                    >
                                        <X className="h-4 w-4" />
                                    </Button>
                                </div>

                                {/* File info */}
                                <div className="absolute bottom-0 left-0 right-0 bg-black/75 text-white p-2">
                                    <p className="text-xs truncate">{file.name}</p>
                                    {file.size && (
                                        <p className="text-xs text-gray-300">
                                            {(file.size / 1024 / 1024).toFixed(1)} MB
                                        </p>
                                    )}
                                </div>

                                {/* Main image indicator */}
                                {index === 0 && (
                                    <div className="absolute top-2 left-2 bg-primary text-primary-foreground px-2 py-1 rounded text-xs font-medium">
                                        Principal
                                    </div>
                                )}
                            </div>
                        </Card>
                    ))}
                </div>
            )}

            {/* File List (alternative view) */}
            {!showPreview && value.length > 0 && (
                <div className="space-y-2">
                    {value.map((file, index) => (
                        <div
                            key={file.url}
                            className="flex items-center justify-between p-3 border rounded-lg"
                        >
                            <div className="flex items-center gap-3">
                                <ImageIcon className="h-5 w-5 text-muted-foreground" />
                                <div>
                                    <p className="text-sm font-medium">{file.name}</p>
                                    {file.size && (
                                        <p className="text-xs text-muted-foreground">
                                            {(file.size / 1024 / 1024).toFixed(1)} MB
                                        </p>
                                    )}
                                </div>
                            </div>
                            <Button
                                size="sm"
                                variant="ghost"
                                onClick={() => removeFile(index)}
                            >
                                <X className="h-4 w-4" />
                            </Button>
                        </div>
                    ))}
                </div>
            )}

            {/* Upload Progress */}
            {value.length > 0 && (
                <div className="text-sm text-muted-foreground">
                    {value.length} de {maxFiles} imagens selecionadas
                </div>
            )}
        </div>
    );
}
