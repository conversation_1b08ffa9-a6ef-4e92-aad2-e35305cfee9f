import { <PERSON>, <PERSON> } from '@inertiajs/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
    User as UserIcon, 
    Mail, 
    Phone, 
    Calendar, 
    Building, 
    Globe, 
    Edit,
    Car,
    Settings,
    ShoppingBag,
    Eye,
    MapPin
} from 'lucide-react';
import { AvatarImage } from '@/components/ui/responsive-image';

interface User {
    id: number;
    name: string;
    email: string;
    phone?: string;
    cpf_cnpj?: string;
    type: 'individual' | 'company';
    birth_date?: string;
    company_name?: string;
    trading_name?: string;
    website?: string;
    bio?: string;
    avatar?: string;
    created_at: string;
    email_verified_at?: string;
    vehicles: any[];
    parts: any[];
}

interface Stats {
    total_vehicles: number;
    active_vehicles: number;
    total_parts: number;
    active_parts: number;
    total_views: number;
}

interface Props {
    user: User;
    stats: Stats;
}

export default function ProfileShow({ user, stats }: Props) {
    const getTypeBadge = (type: string) => {
        const typeConfig = {
            individual: { label: 'Pessoa Física', variant: 'outline' as const },
            company: { label: 'Empresa', variant: 'secondary' as const },
        };

        const config = typeConfig[type as keyof typeof typeConfig] || { label: type, variant: 'outline' as const };
        return <Badge variant={config.variant}>{config.label}</Badge>;
    };

    const statCards = [
        {
            title: 'Veículos',
            value: stats.total_vehicles,
            description: `${stats.active_vehicles} ativos`,
            icon: Car,
            color: 'text-blue-600',
            bgColor: 'bg-blue-50',
        },
        {
            title: 'Peças',
            value: stats.total_parts,
            description: `${stats.active_parts} ativas`,
            icon: Settings,
            color: 'text-green-600',
            bgColor: 'bg-green-50',
        },
        {
            title: 'Visualizações',
            value: stats.total_views,
            description: 'Total de visualizações',
            icon: Eye,
            color: 'text-purple-600',
            bgColor: 'bg-purple-50',
        },
    ];

    return (
        <>
            <Head title="Meu Perfil" />
            
            <div className="space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold tracking-tight">Meu Perfil</h1>
                        <p className="text-muted-foreground">
                            Gerencie suas informações pessoais e configurações
                        </p>
                    </div>
                    <Button asChild>
                        <Link href="/minha-conta/perfil/editar">
                            <Edit className="h-4 w-4 mr-2" />
                            Editar Perfil
                        </Link>
                    </Button>
                </div>

                {/* Profile Info */}
                <div className="grid gap-6 md:grid-cols-3">
                    <Card className="md:col-span-2">
                        <CardHeader>
                            <CardTitle>Informações Pessoais</CardTitle>
                            <CardDescription>
                                Suas informações básicas e de contato
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-6">
                            {/* Avatar and Basic Info */}
                            <div className="flex items-start gap-4">
                                <AvatarImage
                                    src={user.avatar}
                                    alt={user.name}
                                    size="large"
                                />
                                <div className="flex-1">
                                    <div className="flex items-center gap-2 mb-2">
                                        <h2 className="text-xl font-semibold">{user.name}</h2>
                                        {getTypeBadge(user.type)}
                                        {user.email_verified_at && (
                                            <Badge variant="default">Email Verificado</Badge>
                                        )}
                                    </div>
                                    {user.bio && (
                                        <p className="text-muted-foreground">{user.bio}</p>
                                    )}
                                </div>
                            </div>

                            {/* Contact Information */}
                            <div className="grid gap-4 md:grid-cols-2">
                                <div className="flex items-center gap-3">
                                    <Mail className="h-5 w-5 text-muted-foreground" />
                                    <div>
                                        <p className="text-sm font-medium">Email</p>
                                        <p className="text-sm text-muted-foreground">{user.email}</p>
                                    </div>
                                </div>

                                {user.phone && (
                                    <div className="flex items-center gap-3">
                                        <Phone className="h-5 w-5 text-muted-foreground" />
                                        <div>
                                            <p className="text-sm font-medium">Telefone</p>
                                            <p className="text-sm text-muted-foreground">{user.phone}</p>
                                        </div>
                                    </div>
                                )}

                                {user.birth_date && (
                                    <div className="flex items-center gap-3">
                                        <Calendar className="h-5 w-5 text-muted-foreground" />
                                        <div>
                                            <p className="text-sm font-medium">Data de Nascimento</p>
                                            <p className="text-sm text-muted-foreground">
                                                {new Date(user.birth_date).toLocaleDateString()}
                                            </p>
                                        </div>
                                    </div>
                                )}

                                {user.cpf_cnpj && (
                                    <div className="flex items-center gap-3">
                                        <UserIcon className="h-5 w-5 text-muted-foreground" />
                                        <div>
                                            <p className="text-sm font-medium">
                                                {user.type === 'company' ? 'CNPJ' : 'CPF'}
                                            </p>
                                            <p className="text-sm text-muted-foreground">{user.cpf_cnpj}</p>
                                        </div>
                                    </div>
                                )}
                            </div>

                            {/* Company Information */}
                            {user.type === 'company' && (
                                <div className="border-t pt-4">
                                    <h3 className="text-lg font-medium mb-4">Informações da Empresa</h3>
                                    <div className="grid gap-4 md:grid-cols-2">
                                        {user.company_name && (
                                            <div className="flex items-center gap-3">
                                                <Building className="h-5 w-5 text-muted-foreground" />
                                                <div>
                                                    <p className="text-sm font-medium">Razão Social</p>
                                                    <p className="text-sm text-muted-foreground">{user.company_name}</p>
                                                </div>
                                            </div>
                                        )}

                                        {user.trading_name && (
                                            <div className="flex items-center gap-3">
                                                <Building className="h-5 w-5 text-muted-foreground" />
                                                <div>
                                                    <p className="text-sm font-medium">Nome Fantasia</p>
                                                    <p className="text-sm text-muted-foreground">{user.trading_name}</p>
                                                </div>
                                            </div>
                                        )}

                                        {user.website && (
                                            <div className="flex items-center gap-3">
                                                <Globe className="h-5 w-5 text-muted-foreground" />
                                                <div>
                                                    <p className="text-sm font-medium">Website</p>
                                                    <a 
                                                        href={user.website} 
                                                        target="_blank" 
                                                        rel="noopener noreferrer"
                                                        className="text-sm text-blue-600 hover:underline"
                                                    >
                                                        {user.website}
                                                    </a>
                                                </div>
                                            </div>
                                        )}
                                    </div>
                                </div>
                            )}

                            {/* Account Info */}
                            <div className="border-t pt-4">
                                <div className="flex items-center gap-3">
                                    <Calendar className="h-5 w-5 text-muted-foreground" />
                                    <div>
                                        <p className="text-sm font-medium">Membro desde</p>
                                        <p className="text-sm text-muted-foreground">
                                            {new Date(user.created_at).toLocaleDateString()}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Stats */}
                    <div className="space-y-4">
                        <Card>
                            <CardHeader>
                                <CardTitle>Estatísticas</CardTitle>
                                <CardDescription>
                                    Resumo da sua atividade
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                {statCards.map((stat, index) => (
                                    <div key={index} className="flex items-center gap-3">
                                        <div className={`p-2 rounded-full ${stat.bgColor}`}>
                                            <stat.icon className={`h-4 w-4 ${stat.color}`} />
                                        </div>
                                        <div className="flex-1">
                                            <div className="flex items-center justify-between">
                                                <p className="text-sm font-medium">{stat.title}</p>
                                                <p className="text-lg font-bold">{stat.value}</p>
                                            </div>
                                            <p className="text-xs text-muted-foreground">{stat.description}</p>
                                        </div>
                                    </div>
                                ))}
                            </CardContent>
                        </Card>

                        {/* Quick Actions */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Ações Rápidas</CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-2">
                                <Button asChild variant="outline" className="w-full justify-start">
                                    <Link href="/minha-conta/anuncios">
                                        <ShoppingBag className="h-4 w-4 mr-2" />
                                        Meus Anúncios
                                    </Link>
                                </Button>
                                <Button asChild variant="outline" className="w-full justify-start">
                                    <Link href="/minha-conta/favoritos">
                                        <ShoppingBag className="h-4 w-4 mr-2" />
                                        Favoritos
                                    </Link>
                                </Button>
                                <Button asChild variant="outline" className="w-full justify-start">
                                    <Link href="/minha-conta/chat">
                                        <ShoppingBag className="h-4 w-4 mr-2" />
                                        Mensagens
                                    </Link>
                                </Button>
                            </CardContent>
                        </Card>
                    </div>
                </div>
            </div>
        </>
    );
}
