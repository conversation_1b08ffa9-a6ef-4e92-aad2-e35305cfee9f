import { Header } from "@/components/header"
import { Footer } from "@/components/footer"
import { VehicleGallery } from "@/components/vehicle-gallery"
import { VehicleDetails } from "@/components/vehicle-details"
import { SellerContact } from "@/components/seller-contact"
import { RelatedListings } from "@/components/related-listings"
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"

// Mock data - in a real app this would come from an API
const vehicleData = {
  id: 1,
  title: "Honda Civic 2022 EXL",
  price: "R$ 89.900",
  location: "São Paulo, SP",
  year: "2022",
  fuel: "Flex",
  mileage: "25.000 km",
  transmission: "Automático",
  color: "Prata",
  doors: "4 portas",
  engine: "2.0 16V",
  condition: "Usado",
  type: "Carro",
  brand: "Honda",
  model: "Civic",
  version: "EXL",
  description:
    "Honda Civic 2022 em excelente estado de conservação. Veículo sempre revisado na concessionária, com todos os manuais e chaves reserva. Interior em couro, ar condicionado digital, central multimídia com Android Auto e Apple CarPlay, câmera de ré, sensores de estacionamento. Pneus novos, bateria nova. Aceito financiamento e troca por veículo de menor valor.",
  features: [
    "Ar condicionado digital",
    "Central multimídia",
    "Câmera de ré",
    "Sensores de estacionamento",
    "Bancos de couro",
    "Direção elétrica",
    "Vidros elétricos",
    "Travas elétricas",
    "Airbags frontais e laterais",
    "Freios ABS",
    "Controle de estabilidade",
    "Android Auto / Apple CarPlay",
  ],
  images: [
    "/honda-civic-2022-silver-car.jpg",
    "/honda-civic-interior-dashboard.jpg",
    "/honda-civic-rear-view.jpg",
    "/honda-civic-engine-bay.jpg",
    "/honda-civic-side-profile.jpg",
  ],
  seller: {
    name: "João Silva",
    phone: "(11) 99999-9999",
    whatsapp: "(11) 99999-9999",
    email: "<EMAIL>",
    location: "São Paulo, SP",
    memberSince: "2020",
    rating: 4.8,
    totalSales: 15,
    verified: true,
  },
  createdAt: "2024-01-15",
  views: 1247,
  favorites: 23,
}

export default function VehicleDetailsPage({ params }: { params: { id: string } }) {
  return (
    <div className="min-h-screen bg-background">
      <Header />
      <main className="container mx-auto px-4 py-6">
        {/* Breadcrumb */}
        <Breadcrumb className="mb-6">
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink href="/">Início</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink href="/anuncios">Anúncios</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink href="/carros">Carros</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage>{vehicleData.title}</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-8">
            <VehicleGallery images={vehicleData.images} title={vehicleData.title} />
            <VehicleDetails vehicle={vehicleData} />
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            <SellerContact seller={vehicleData.seller} vehicle={vehicleData} />
          </div>
        </div>

        {/* Related Listings */}
        <div className="mt-16">
          <RelatedListings currentVehicle={vehicleData} />
        </div>
      </main>
      <Footer />
    </div>
  )
}
