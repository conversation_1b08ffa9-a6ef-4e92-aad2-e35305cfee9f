import { useCallback, useState, useRef, useEffect } from 'react';
import { X, Upload as UploadIcon, Image as ImageIcon } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

export interface ImageFile {
  id: string;
  file: File;
  preview: string;
  name: string;
  size: number;
  type: string;
}

interface FormImageUploadProps {
  value: ImageFile[];
  onChange: (files: ImageFile[]) => void;
  maxFiles?: number;
  maxSizeMB?: number;
  disabled?: boolean;
  className?: string;
}

export function FormImageUpload({
  value = [],
  onChange,
  maxFiles = 10,
  maxSizeMB = 5,
  disabled = false,
  className,
}: FormImageUploadProps) {
  const [isDragging, setIsDragging] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const maxSize = maxSizeMB * 1024 * 1024; // Convert MB to bytes

  const processFiles = useCallback((files: FileList) => {
    const newFiles: ImageFile[] = [];
    const remainingSlots = Math.max(0, maxFiles - value.length);
    const filesToProcess = Array.from(files).slice(0, remainingSlots);

    for (const file of filesToProcess) {
      if (!file.type.startsWith('image/')) {
        setError('Apenas arquivos de imagem são permitidos');
        continue;
      }

      if (file.size > maxSize) {
        setError(`Arquivo ${file.name} excede o tamanho máximo de ${maxSizeMB}MB`);
        continue;
      }

      newFiles.push({
        id: Math.random().toString(36).substring(2, 9),
        file,
        preview: URL.createObjectURL(file),
        name: file.name,
        size: file.size,
        type: file.type,
      });
    }

    if (newFiles.length > 0) {
      onChange([...value, ...newFiles]);
      setError(null);
    }
  }, [maxFiles, maxSize, value, onChange, maxSizeMB]);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!e.target.files || e.target.files.length === 0) return;
    processFiles(e.target.files);
    e.target.value = ''; // Reset input to allow selecting the same file again
  };

  const handleRemove = (id: string) => {
    const updatedFiles = value.filter((file) => file.id !== id);
    onChange(updatedFiles);
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    if (disabled) return;
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
    
    if (disabled) return;
    
    const { files } = e.dataTransfer;
    if (files && files.length > 0) {
      processFiles(files);
    }
  };

  // Clean up object URLs to avoid memory leaks
  useEffect(() => {
    return () => {
      value.forEach((file) => URL.revokeObjectURL(file.preview));
    };
  }, [value]);

  const openFileDialog = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const hasFiles = value.length > 0;
  const canAddMore = value.length < maxFiles;

  return (
    <div className={className}>
      <div
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        className={cn(
          'border-2 border-dashed rounded-lg p-6 transition-colors',
          isDragging ? 'border-primary bg-primary/5' : 'border-muted-foreground/25',
          disabled && 'opacity-50 cursor-not-allowed'
        )}
      >
        <input
          ref={fileInputRef}
          type="file"
          className="hidden"
          onChange={handleFileChange}
          accept="image/*"
          multiple={maxFiles > 1}
          disabled={disabled || !canAddMore}
        />

        {!hasFiles ? (
          <div className="flex flex-col items-center justify-center space-y-4 text-center">
            <div className="rounded-full bg-primary/10 p-3">
              <ImageIcon className="h-6 w-6 text-primary" />
            </div>
            <div className="space-y-1">
              <p className="text-sm font-medium">
                {isDragging ? 'Solte as imagens aqui' : 'Arraste e solte imagens aqui'}
              </p>
              <p className="text-sm text-muted-foreground">
                {maxFiles > 1
                  ? `Até ${maxFiles} imagens (${value.length}/${maxFiles} selecionadas)`
                  : 'Apenas uma imagem'}
              </p>
            </div>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={openFileDialog}
              disabled={disabled || !canAddMore}
            >
              <UploadIcon className="mr-2 h-4 w-4" />
              Selecionar arquivos
            </Button>
          </div>
        ) : (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h4 className="text-sm font-medium">
                {value.length} {value.length === 1 ? 'imagem' : 'imagens'} selecionada{value.length !== 1 ? 's' : ''}
              </h4>
              {canAddMore && (
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={openFileDialog}
                  disabled={disabled}
                >
                  <UploadIcon className="mr-2 h-4 w-4" />
                  Adicionar mais
                </Button>
              )}
            </div>

            <div className="grid grid-cols-2 gap-4 sm:grid-cols-3 md:grid-cols-4">
              {value.map((file) => (
                <div key={file.id} className="group relative">
                  <div className="aspect-square overflow-hidden rounded-md border">
                    <img
                      src={file.preview}
                      alt={file.name}
                      className="h-full w-full object-cover"
                    />
                  </div>
                  <Button
                    type="button"
                    variant="destructive"
                    size="icon"
                    className="absolute -right-2 -top-2 h-6 w-6 rounded-full opacity-0 transition-opacity group-hover:opacity-100"
                    onClick={() => handleRemove(file.id)}
                    disabled={disabled}
                  >
                    <X className="h-3 w-3" />
                    <span className="sr-only">Remover</span>
                  </Button>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      {error && (
        <p className="mt-2 flex items-center text-sm text-destructive">
          <X className="mr-1 h-4 w-4" />
          {error}
        </p>
      )}
    </div>
  );
}
