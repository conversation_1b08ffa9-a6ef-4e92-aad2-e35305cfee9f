"use client"

import { useState } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Slider } from "@/components/ui/slider"
import { Badge } from "@/components/ui/badge"
import { X } from "lucide-react"

export function ListingFilters() {
  const [priceRange, setPriceRange] = useState([0, 500000])
  const [yearRange, setYearRange] = useState([2010, 2024])
  const [selectedBrands, setSelectedBrands] = useState<string[]>([])
  const [selectedFuelTypes, setSelectedFuelTypes] = useState<string[]>([])

  const brands = ["Toyota", "Honda", "Volkswagen", "Ford", "Chevrolet", "Fiat", "Hyundai", "Nissan"]
  const fuelTypes = ["Flex", "Gasolina", "Diesel", "Elétrico", "Híbrido", "GNV"]

  const activeFilters = [
    ...selectedBrands.map((brand) => ({ type: "brand", value: brand })),
    ...selectedFuelTypes.map((fuel) => ({ type: "fuel", value: fuel })),
  ]

  const removeFilter = (type: string, value: string) => {
    if (type === "brand") {
      setSelectedBrands((prev) => prev.filter((b) => b !== value))
    } else if (type === "fuel") {
      setSelectedFuelTypes((prev) => prev.filter((f) => f !== value))
    }
  }

  const clearAllFilters = () => {
    setSelectedBrands([])
    setSelectedFuelTypes([])
    setPriceRange([0, 500000])
    setYearRange([2010, 2024])
  }

  return (
    <div className="space-y-6">
      {/* Active Filters */}
      {activeFilters.length > 0 && (
        <Card>
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="text-sm">Filtros ativos</CardTitle>
              <Button variant="ghost" size="sm" onClick={clearAllFilters}>
                Limpar todos
              </Button>
            </div>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="flex flex-wrap gap-2">
              {activeFilters.map((filter, index) => (
                <Badge key={index} variant="secondary" className="flex items-center gap-1">
                  {filter.value}
                  <X className="h-3 w-3 cursor-pointer" onClick={() => removeFilter(filter.type, filter.value)} />
                </Badge>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Category Filter */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">Categoria</CardTitle>
        </CardHeader>
        <CardContent>
          <Select>
            <SelectTrigger>
              <SelectValue placeholder="Todas as categorias" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Todas as categorias</SelectItem>
              <SelectItem value="carros">Carros</SelectItem>
              <SelectItem value="motos">Motos</SelectItem>
              <SelectItem value="pecas">Peças</SelectItem>
              <SelectItem value="caminhoes">Caminhões</SelectItem>
            </SelectContent>
          </Select>
        </CardContent>
      </Card>

      {/* Price Range */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">Faixa de preço</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="px-2">
            <Slider
              value={priceRange}
              onValueChange={setPriceRange}
              max={500000}
              min={0}
              step={5000}
              className="w-full"
            />
          </div>
          <div className="flex items-center space-x-2">
            <div className="flex-1">
              <Label htmlFor="min-price" className="text-xs">
                Mínimo
              </Label>
              <Input
                id="min-price"
                type="number"
                value={priceRange[0]}
                onChange={(e) => setPriceRange([Number.parseInt(e.target.value) || 0, priceRange[1]])}
                className="h-8"
              />
            </div>
            <div className="flex-1">
              <Label htmlFor="max-price" className="text-xs">
                Máximo
              </Label>
              <Input
                id="max-price"
                type="number"
                value={priceRange[1]}
                onChange={(e) => setPriceRange([priceRange[0], Number.parseInt(e.target.value) || 500000])}
                className="h-8"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Year Range */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">Ano</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="px-2">
            <Slider value={yearRange} onValueChange={setYearRange} max={2024} min={1990} step={1} className="w-full" />
          </div>
          <div className="flex items-center space-x-2">
            <div className="flex-1">
              <Label htmlFor="min-year" className="text-xs">
                De
              </Label>
              <Input
                id="min-year"
                type="number"
                value={yearRange[0]}
                onChange={(e) => setYearRange([Number.parseInt(e.target.value) || 1990, yearRange[1]])}
                className="h-8"
              />
            </div>
            <div className="flex-1">
              <Label htmlFor="max-year" className="text-xs">
                Até
              </Label>
              <Input
                id="max-year"
                type="number"
                value={yearRange[1]}
                onChange={(e) => setYearRange([yearRange[0], Number.parseInt(e.target.value) || 2024])}
                className="h-8"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Brand Filter */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">Marca</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3 max-h-48 overflow-y-auto">
            {brands.map((brand) => (
              <div key={brand} className="flex items-center space-x-2">
                <Checkbox
                  id={brand}
                  checked={selectedBrands.includes(brand)}
                  onCheckedChange={(checked) => {
                    if (checked) {
                      setSelectedBrands([...selectedBrands, brand])
                    } else {
                      setSelectedBrands(selectedBrands.filter((b) => b !== brand))
                    }
                  }}
                />
                <Label htmlFor={brand} className="text-sm font-normal cursor-pointer">
                  {brand}
                </Label>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Fuel Type Filter */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">Combustível</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {fuelTypes.map((fuel) => (
              <div key={fuel} className="flex items-center space-x-2">
                <Checkbox
                  id={fuel}
                  checked={selectedFuelTypes.includes(fuel)}
                  onCheckedChange={(checked) => {
                    if (checked) {
                      setSelectedFuelTypes([...selectedFuelTypes, fuel])
                    } else {
                      setSelectedFuelTypes(selectedFuelTypes.filter((f) => f !== fuel))
                    }
                  }}
                />
                <Label htmlFor={fuel} className="text-sm font-normal cursor-pointer">
                  {fuel}
                </Label>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Location Filter */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">Localização</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <Select>
            <SelectTrigger>
              <SelectValue placeholder="Estado" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="sp">São Paulo</SelectItem>
              <SelectItem value="rj">Rio de Janeiro</SelectItem>
              <SelectItem value="mg">Minas Gerais</SelectItem>
              <SelectItem value="pr">Paraná</SelectItem>
              <SelectItem value="rs">Rio Grande do Sul</SelectItem>
            </SelectContent>
          </Select>
          <Select>
            <SelectTrigger>
              <SelectValue placeholder="Cidade" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="sao-paulo">São Paulo</SelectItem>
              <SelectItem value="rio-janeiro">Rio de Janeiro</SelectItem>
              <SelectItem value="belo-horizonte">Belo Horizonte</SelectItem>
              <SelectItem value="curitiba">Curitiba</SelectItem>
              <SelectItem value="porto-alegre">Porto Alegre</SelectItem>
            </SelectContent>
          </Select>
        </CardContent>
      </Card>
    </div>
  )
}
