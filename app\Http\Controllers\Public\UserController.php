<?php

namespace App\Http\Controllers\Public;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Advertisement;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class UserController extends Controller
{
    /**
     * Display the specified user's public profile
     */
    public function show(User $user): Response
    {
        // Get user's published advertisements
        $advertisements = Advertisement::with(['vehicle.brand', 'vehicle.category', 'featuredImage'])
            ->where('user_id', $user->id)
            ->where('status', 'published')
            ->latest()
            ->paginate(12);

        // Get user statistics
        $stats = [
            'total_ads' => Advertisement::where('user_id', $user->id)
                ->where('status', 'published')
                ->count(),
            'active_ads' => Advertisement::where('user_id', $user->id)
                ->where('status', 'published')
                ->count(),
            'member_since' => $user->created_at->format('Y'),
            'avg_response_time' => '2 horas', // This would be calculated from actual data
            'rating' => 4.5, // This would come from a ratings system
            'total_reviews' => 23, // This would come from a reviews system
        ];

        // Get recent reviews (if you have a review system)
        $reviews = []; // This would be populated from a reviews table

        return Inertia::render('Users/Show', [
            'user' => $user->only(['id', 'name', 'avatar', 'created_at']),
            'advertisements' => $advertisements,
            'stats' => $stats,
            'reviews' => $reviews,
        ]);
    }

    /**
     * Display user's advertisements with filters
     */
    public function advertisements(Request $request, User $user): Response
    {
        $query = Advertisement::with(['vehicle.brand', 'vehicle.category', 'featuredImage'])
            ->where('user_id', $user->id)
            ->where('status', 'published');

        // Apply filters
        if ($request->filled('search')) {
            $search = $request->input('search');
            $query->where(function($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhereHas('vehicle', function($vehicleQuery) use ($search) {
                      $vehicleQuery->where('model', 'like', "%{$search}%");
                  });
            });
        }

        if ($request->filled('category_id')) {
            $query->whereHas('vehicle', function($vehicleQuery) use ($request) {
                $vehicleQuery->where('category_id', $request->input('category_id'));
            });
        }

        if ($request->filled('min_price')) {
            $query->where('price', '>=', $request->input('min_price'));
        }

        if ($request->filled('max_price')) {
            $query->where('price', '<=', $request->input('max_price'));
        }

        // Sorting
        $sortBy = $request->input('sort_by', 'created_at');
        $sortOrder = $request->input('sort_order', 'desc');
        
        $validSortFields = ['created_at', 'price', 'title'];
        if (in_array($sortBy, $validSortFields)) {
            $query->orderBy($sortBy, $sortOrder);
        }

        $advertisements = $query->paginate(12)->withQueryString();

        return Inertia::render('Users/Advertisements', [
            'user' => $user->only(['id', 'name', 'avatar', 'created_at']),
            'advertisements' => $advertisements,
            'filters' => $request->all(),
        ]);
    }

    /**
     * Contact user
     */
    public function contact(Request $request, User $user)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'nullable|string|max:20',
            'message' => 'required|string|max:1000',
            'subject' => 'required|string|max:255',
        ]);

        // Here you would typically send an email or create a message record
        // For now, we'll just return a success response
        
        return response()->json([
            'message' => 'Mensagem enviada com sucesso! O usuário entrará em contato em breve.'
        ]);
    }

    /**
     * Report user
     */
    public function report(Request $request, User $user)
    {
        $request->validate([
            'reason' => 'required|string|in:spam,inappropriate,fake,harassment,other',
            'description' => 'nullable|string|max:500',
        ]);

        // Here you would typically create a report record
        // For now, we'll just return a success response
        
        return response()->json([
            'message' => 'Denúncia enviada com sucesso! Nossa equipe irá analisar.'
        ]);
    }
}
