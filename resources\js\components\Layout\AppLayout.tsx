import { ReactNode } from 'react';
import { Head } from '@inertiajs/react';
import HeaderOLX from '@/components/HeaderOLX';
import Footer from './Footer';
import { Category } from '@/types';

type Props = {
    children: ReactNode;
    title?: string;
    description?: string;
    showHeader?: boolean;
    showFooter?: boolean;
    categories?: Category[];
};

export default function AppLayout({
    children,
    title = 'AutoMercado - Compre e venda veículos e peças',
    description = 'A maior plataforma de compra e venda de veículos e peças automotivas do Brasil. Encontre as melhores ofertas de carros, motos, caminhões e peças.',
    showHeader = true,
    showFooter = true,
    categories = [],
}: Props) {
    return (
        <div className="flex flex-col min-h-screen bg-gray-50">
            <Head>
                <title>{title}</title>
                <meta name="description" content={description} />
                <link rel="icon" href="/favicon.ico" />
                
                {/* Open Graph / Facebook */}
                <meta property="og:type" content="website" />
                <meta property="og:title" content={title} />
                <meta property="og:description" content={description} />
                <meta property="og:image" content="/images/og-image.jpg" />
                
                {/* Twitter */}
                <meta property="twitter:card" content="summary_large_image" />
                <meta property="twitter:title" content={title} />
                <meta property="twitter:description" content={description} />
                <meta property="twitter:image" content="/images/og-image.jpg" />
            </Head>

            {showHeader && <HeaderOLX categories={categories} />}
            
            <main className="flex-grow pt-16">
                {children}
            </main>
            
            {showFooter && <Footer />}
        </div>
    );
}
