"use client"

import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"

interface BasicInfoStepProps {
  formData: any
  updateFormData: (data: any) => void
  onNext: () => void
  onPrev: () => void
}

const categories = [
  { value: "carros", label: "Carros" },
  { value: "motos", label: "Motos" },
  { value: "pecas", label: "Peças" },
  { value: "caminhoes", label: "<PERSON>inhões" },
]

const carBrands = [
  "Toyota",
  "Honda",
  "Volkswagen",
  "Ford",
  "Chevrolet",
  "Fiat",
  "Hyundai",
  "Nissan",
  "Renault",
  "Peugeot",
  "Citroën",
  "Jeep",
]

const motoBrands = [
  "Honda",
  "Yamaha",
  "Suzuki",
  "Kawasaki",
  "BMW",
  "Harley-Davidson",
  "Ducati",
  "KTM",
  "Triumph",
  "Royal Enfield",
]

export function BasicInfoStep({ formData, updateFormData }: BasicInfoStepProps) {
  const brands = formData.category === "motos" ? motoBrands : carBrands

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Category */}
        <div className="space-y-2">
          <Label htmlFor="category">Categoria *</Label>
          <Select value={formData.category} onValueChange={(value) => updateFormData({ category: value })}>
            <SelectTrigger>
              <SelectValue placeholder="Selecione a categoria" />
            </SelectTrigger>
            <SelectContent>
              {categories.map((category) => (
                <SelectItem key={category.value} value={category.value}>
                  {category.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Type */}
        <div className="space-y-2">
          <Label>Tipo de anúncio *</Label>
          <RadioGroup
            value={formData.type}
            onValueChange={(value) => updateFormData({ type: value })}
            className="flex space-x-6"
          >
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="venda" id="venda" />
              <Label htmlFor="venda">Venda</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="aluguel" id="aluguel" />
              <Label htmlFor="aluguel">Aluguel</Label>
            </div>
          </RadioGroup>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Brand */}
        <div className="space-y-2">
          <Label htmlFor="brand">Marca *</Label>
          <Select value={formData.brand} onValueChange={(value) => updateFormData({ brand: value })}>
            <SelectTrigger>
              <SelectValue placeholder="Selecione a marca" />
            </SelectTrigger>
            <SelectContent>
              {brands.map((brand) => (
                <SelectItem key={brand} value={brand.toLowerCase()}>
                  {brand}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Model */}
        <div className="space-y-2">
          <Label htmlFor="model">Modelo *</Label>
          <Input
            id="model"
            placeholder="Ex: Civic, Corolla, CB 600"
            value={formData.model}
            onChange={(e) => updateFormData({ model: e.target.value })}
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Version */}
        <div className="space-y-2">
          <Label htmlFor="version">Versão</Label>
          <Input
            id="version"
            placeholder="Ex: EXL, XEI, Sport"
            value={formData.version}
            onChange={(e) => updateFormData({ version: e.target.value })}
          />
        </div>

        {/* Year */}
        <div className="space-y-2">
          <Label htmlFor="year">Ano *</Label>
          <Select value={formData.year} onValueChange={(value) => updateFormData({ year: value })}>
            <SelectTrigger>
              <SelectValue placeholder="Ano" />
            </SelectTrigger>
            <SelectContent>
              {Array.from({ length: 35 }, (_, i) => 2024 - i).map((year) => (
                <SelectItem key={year} value={year.toString()}>
                  {year}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Condition */}
        <div className="space-y-2">
          <Label htmlFor="condition">Estado *</Label>
          <Select value={formData.condition} onValueChange={(value) => updateFormData({ condition: value })}>
            <SelectTrigger>
              <SelectValue placeholder="Estado" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="novo">Novo</SelectItem>
              <SelectItem value="usado">Usado</SelectItem>
              <SelectItem value="seminovo">Seminovo</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Price */}
      <div className="space-y-2">
        <Label htmlFor="price">Preço *</Label>
        <div className="relative">
          <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground">R$</span>
          <Input
            id="price"
            type="number"
            placeholder="0,00"
            className="pl-10"
            value={formData.price}
            onChange={(e) => updateFormData({ price: e.target.value })}
          />
        </div>
      </div>
    </div>
  )
}
