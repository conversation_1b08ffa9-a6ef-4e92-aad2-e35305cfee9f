<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Inertia\Inertia;
use Inertia\Response;

class SettingsController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Display the user settings page.
     */
    public function index(): Response
    {
        return Inertia::render('User/Settings');
    }

    /**
     * Update the user's notification preferences.
     */
    public function update(Request $request): RedirectResponse
    {
        $request->validate([
            'email_notifications' => 'boolean',
            'sms_notifications' => 'boolean',
            'push_notifications' => 'boolean',
            'marketing_emails' => 'boolean',
        ]);

        $user = auth()->user();
        
        $notificationPreferences = [
            'email_notifications' => $request->boolean('email_notifications'),
            'sms_notifications' => $request->boolean('sms_notifications'),
            'push_notifications' => $request->boolean('push_notifications'),
            'marketing_emails' => $request->boolean('marketing_emails'),
        ];

        $user->update([
            'notification_preferences' => $notificationPreferences,
        ]);

        return back()->with('success', 'Configurações atualizadas com sucesso!');
    }
}
