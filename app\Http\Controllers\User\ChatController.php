<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Models\Chat;
use App\Models\ChatMessage;
use App\Models\Advertisement;
use App\Models\User;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class ChatController extends Controller
{


    /**
     * Display a listing of user's chats
     */
    public function index(Request $request): Response
    {
        $chats = Chat::with(['advertisement.vehicle.brand', 'buyer', 'seller', 'lastMessage'])
            ->where(function($query) {
                $query->where('buyer_id', auth()->id())
                      ->orWhere('seller_id', auth()->id());
            })
            ->latest('updated_at')
            ->paginate(20);

        return Inertia::render('User/Chat/Index', [
            'chats' => $chats,
        ]);
    }

    /**
     * Display the specified chat
     */
    public function show(Chat $chat): Response
    {
        // Check if user is part of this chat
        if ($chat->buyer_id !== auth()->id() && $chat->seller_id !== auth()->id()) {
            abort(403);
        }

        $chat->load(['advertisement.vehicle.brand', 'buyer', 'seller']);

        $messages = ChatMessage::with('user')
            ->where('chat_id', $chat->id)
            ->orderBy('created_at', 'asc')
            ->paginate(50);

        // Mark messages as read
        ChatMessage::where('chat_id', $chat->id)
            ->where('user_id', '!=', auth()->id())
            ->whereNull('read_at')
            ->update(['read_at' => now()]);

        return Inertia::render('User/Chat/Show', [
            'chat' => $chat,
            'messages' => $messages,
        ]);
    }

    /**
     * Start a new chat about an advertisement
     */
    public function start(Request $request, Advertisement $advertisement)
    {
        $request->validate([
            'message' => 'required|string|max:1000',
        ]);

        // Check if user is not the owner of the advertisement
        if ($advertisement->user_id === auth()->id()) {
            return response()->json(['error' => 'Você não pode iniciar um chat com seu próprio anúncio.'], 400);
        }

        // Check if chat already exists
        $existingChat = Chat::where('advertisement_id', $advertisement->id)
            ->where('buyer_id', auth()->id())
            ->where('seller_id', $advertisement->user_id)
            ->first();

        if ($existingChat) {
            // Add message to existing chat
            $message = ChatMessage::create([
                'chat_id' => $existingChat->id,
                'user_id' => auth()->id(),
                'message' => $request->input('message'),
            ]);

            $existingChat->touch(); // Update updated_at

            return response()->json([
                'chat_id' => $existingChat->id,
                'message' => 'Mensagem enviada com sucesso!',
            ]);
        }

        // Create new chat
        $chat = Chat::create([
            'advertisement_id' => $advertisement->id,
            'buyer_id' => auth()->id(),
            'seller_id' => $advertisement->user_id,
        ]);

        // Create first message
        $message = ChatMessage::create([
            'chat_id' => $chat->id,
            'user_id' => auth()->id(),
            'message' => $request->input('message'),
        ]);

        return response()->json([
            'chat_id' => $chat->id,
            'message' => 'Chat iniciado com sucesso!',
        ]);
    }

    /**
     * Send a message in a chat
     */
    public function sendMessage(Request $request, Chat $chat)
    {
        // Check if user is part of this chat
        if ($chat->buyer_id !== auth()->id() && $chat->seller_id !== auth()->id()) {
            abort(403);
        }

        $request->validate([
            'message' => 'required|string|max:1000',
        ]);

        $message = ChatMessage::create([
            'chat_id' => $chat->id,
            'user_id' => auth()->id(),
            'message' => $request->input('message'),
        ]);

        $chat->touch(); // Update updated_at

        $message->load('user');

        return response()->json([
            'message' => $message,
            'success' => 'Mensagem enviada com sucesso!',
        ]);
    }

    /**
     * Mark chat as read
     */
    public function markAsRead(Chat $chat)
    {
        // Check if user is part of this chat
        if ($chat->buyer_id !== auth()->id() && $chat->seller_id !== auth()->id()) {
            abort(403);
        }

        ChatMessage::where('chat_id', $chat->id)
            ->where('user_id', '!=', auth()->id())
            ->whereNull('read_at')
            ->update(['read_at' => now()]);

        return response()->json(['success' => true]);
    }

    /**
     * Delete a chat
     */
    public function destroy(Chat $chat)
    {
        // Check if user is part of this chat
        if ($chat->buyer_id !== auth()->id() && $chat->seller_id !== auth()->id()) {
            abort(403);
        }

        $chat->delete();

        return redirect()->route('user.chat.index')
            ->with('success', 'Chat excluído com sucesso!');
    }

    /**
     * Get unread messages count
     */
    public function unreadCount()
    {
        $count = ChatMessage::whereHas('chat', function($query) {
                $query->where('buyer_id', auth()->id())
                      ->orWhere('seller_id', auth()->id());
            })
            ->where('user_id', '!=', auth()->id())
            ->whereNull('read_at')
            ->count();

        return response()->json(['count' => $count]);
    }
}
