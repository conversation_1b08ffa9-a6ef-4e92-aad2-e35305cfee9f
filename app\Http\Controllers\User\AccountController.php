<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Models\Chat;
use Inertia\Inertia;
use Inertia\Response;

class AccountController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Display the user account page.
     */
    public function index(): Response
    {
        $user = auth()->user();
        
        // Estatísticas do usuário
        $stats = [
            'total_advertisements' => $user->advertisements()->count(),
            'active_advertisements' => $user->advertisements()->where('status', 'published')->count(),
            'pending_advertisements' => $user->advertisements()->where('status', 'pending_review')->count(),
            'total_favorites' => $user->favorites()->count(),
            'total_chats' => Chat::where(function($query) use ($user) {
                $query->where('buyer_id', $user->id)
                      ->orWhere('seller_id', $user->id);
            })->count(),
            'unread_messages' => Chat::where(function($query) use ($user) {
                $query->where('buyer_id', $user->id)
                      ->orWhere('seller_id', $user->id);
            })->sum('unread_messages_count'),
        ];

        // Anúncios recentes
        $recentAdvertisements = $user->advertisements()
            ->latest()
            ->take(5)
            ->get()
            ->map(function ($advertisement) {
                return [
                    'id' => $advertisement->id,
                    'title' => $advertisement->title,
                    'price' => $advertisement->price,
                    'status' => $advertisement->status,
                    'views' => $advertisement->views ?? 0,
                    'created_at' => $advertisement->created_at->toISOString(),
                    'featured_image_url' => $advertisement->featured_image_url,
                ];
            });

        return Inertia::render('User/Account', [
            'stats' => $stats,
            'recentAdvertisements' => $recentAdvertisements,
        ]);
    }
}
