<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;
use Illuminate\Support\Facades\Log;

class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [
        Commands\ExpireAdvertisements::class,
        Commands\NotifyExpiringAdvertisements::class,
        Commands\GenerateSampleAdvertisements::class,
        Commands\CheckAdvertisementIntegrity::class,
        Commands\FixOrphanedAdvertisements::class,
        Commands\FixAdvertisementDates::class,
    ];

    /**
     * Define the application's command schedule.
     *
     * @param  \Illuminate\Console\Scheduling\Schedule  $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        // Verificar e expirar anúncios diariamente à meia-noite
        $schedule->command('advertisements:expire')
                 ->dailyAt('00:00')
                 ->onSuccess(function () {
                     Log::info('Comando de expiração de anúncios executado com sucesso.');
                 })
                 ->onFailure(function () {
                     Log::error('Falha ao executar o comando de expiração de anúncios.');
                 });
                 
        // Notificar sobre anúncios prestes a expirar diariamente às 10:00
        $schedule->command('advertisements:notify-expiring --days=3')
                 ->dailyAt('10:00')
                 ->onSuccess(function () {
                     Log::info('Comando de notificação de anúncios prestes a expirar executado com sucesso.');
                 })
                 ->onFailure(function () {
                     Log::error('Falha ao executar o comando de notificação de anúncios prestes a expirar.');
                 });
                 
        // Verificar a integridade dos anúncios semanalmente às segundas-feiras às 2:00
        $schedule->command('advertisements:check-integrity')
                 ->weeklyOn(1, '2:00')
                 ->onSuccess(function () {
                     Log::info('Verificação de integridade dos anúncios executada com sucesso.');
                 })
                 ->onFailure(function () {
                     Log::error('Falha ao executar a verificação de integridade dos anúncios.');
                 });
                 
        // Corrigir datas de anúncios diariamente às 3:00
        $schedule->command('advertisements:fix-dates')
                 ->dailyAt('03:00')
                 ->onSuccess(function () {
                     Log::info('Correção de datas de anúncios executada com sucesso.');
                 })
                 ->onFailure(function () {
                     Log::error('Falha ao executar a correção de datas de anúncios.');
                 });
                 
        // Corrigir anúncios órfãos semanalmente às terças-feiras às 4:00
        $schedule->command('advertisements:fix-orphaned')
                 ->weeklyOn(2, '04:00')
                 ->onSuccess(function () {
                     Log::info('Correção de anúncios órfãos executada com sucesso.');
                 })
                 ->onFailure(function () {
                     Log::error('Falha ao executar a correção de anúncios órfãos.');
                 });
    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
