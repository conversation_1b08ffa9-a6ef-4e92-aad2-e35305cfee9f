import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import MainLayout from '@/layouts/MainLayout';
import { PageProps } from '@/types';
import { Head, Link, router } from '@inertiajs/react';
import { Grid, List } from 'lucide-react';
import { useState } from 'react';

interface Advertisement {
    id: number;
    title: string;
    description: string;
    price: number;
    location: string;
    created_at: string;
    featured_image?: {
        url: string;
        alt: string;
    };
    vehicle: {
        id: number;
        model: string;
        year: number;
        mileage: number;
        fuel_type: string;
        transmission: string;
        condition: string;
        brand: {
            id: number;
            name: string;
        };
        category: {
            id: number;
            name: string;
        };
    };
    user: {
        id: number;
        name: string;
    };
}

interface Brand {
    id: number;
    name: string;
}

interface Category {
    id: number;
    name: string;
    slug: string;
    url: string;
}

interface SearchPageProps extends PageProps {
    advertisements: {
        data: Advertisement[];
        links: Array<{ url: string | null; label: string; active: boolean }>;
        meta: {
            current_page: number;
            last_page: number;
            per_page: number;
            total: number;
        };
    };
    brands: Array<
        Brand & { slug: string; created_at: string; updated_at: string }
    >;
    categories: Category[];
    filters: Record<string, string | number | null>;
    priceRange: {
        min_price: number;
        max_price: number;
    };
    yearRange: {
        min_year: number;
        max_year: number;
    };
    fuelTypes: Record<string, string>;
    transmissions: Record<string, string>;
    conditions: Record<string, string>;
}

export default function SearchIndex({
    advertisements = {
        data: [],
        links: [],
        meta: { current_page: 1, last_page: 1, per_page: 15, total: 0 },
    },
    brands = [],
    categories = [],
    filters = {},
    priceRange = { min_price: 0, max_price: 1000000 },
    yearRange = { min_year: 1990, max_year: new Date().getFullYear() },
    fuelTypes = {},
    transmissions = {},
    // conditions, // Unused for now
}: SearchPageProps) {
    // Destructure advertisements data
    const {
        data: ads = [],
        links = [],
        meta = { current_page: 1, last_page: 1, per_page: 15, total: 0 },
    } = advertisements;
    const [showFilters, setShowFilters] = useState(false);
    const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
    const [localFilters, setLocalFilters] = useState({
        search: filters.search || '',
        category_id: filters.category_id || '',
        brand_id: filters.brand_id || '',
        min_price: filters.min_price || priceRange?.min_price || 0,
        max_price: filters.max_price || priceRange?.max_price || 1000000,
        year_min: filters.year_min || yearRange?.min_year || 1990,
        year_max:
            filters.year_max || yearRange?.max_year || new Date().getFullYear(),
        fuel_type: filters.fuel_type || '',
        transmission: filters.transmission || '',
        condition: filters.condition || '',
        mileage_max: filters.mileage_max || 300000,
        location: filters.location || '',
        sort_by: filters.sort_by || 'created_at',
        sort_order: filters.sort_order || 'desc',
    });

    const handleFilterChange = (
        key: string,
        value: string | number | undefined,
    ) => {
        // Convert numeric strings to numbers for specific fields
        let processedValue: string | number | undefined = value;

        if (
            [
                'min_price',
                'max_price',
                'year_min',
                'year_max',
                'mileage_max',
            ].includes(key)
        ) {
            if (typeof value === 'string') {
                processedValue = parseFloat(value) || 0;
            } else if (value === undefined) {
                processedValue = 0;
            }
        }

        setLocalFilters((prev) => ({ ...prev, [key]: processedValue }));
    };

    const applyFilters = () => {
        const cleanFilters: Record<string, string | number> =
            Object.fromEntries(
                Object.entries(localFilters)
                    .filter(
                        ([key, value]) =>
                            value !== '' &&
                            value !== null &&
                            value !== undefined &&
                            value !== 'all' &&
                            key !== 'search', // Remove search from URL since we're using header search
                    )
                    .map(([key, value]) => {
                        // Ensure numeric fields are numbers
                        if (
                            [
                                'min_price',
                                'max_price',
                                'year_min',
                                'year_max',
                                'mileage_max',
                            ].includes(key)
                        ) {
                            const numValue =
                                typeof value === 'string'
                                    ? parseFloat(value)
                                    : value;
                            return [key, numValue] as [string, number];
                        }
                        return [key, value as string];
                    }),
            );

        router.get('/pesquisar', cleanFilters, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    const clearFilters = () => {
        const newFilters = {
            search: '',
            category_id: '',
            brand_id: '',
            min_price: priceRange?.min_price || 0,
            max_price: priceRange?.max_price || 1000000,
            year_min: yearRange?.min_year || 1990,
            year_max: yearRange?.max_year || new Date().getFullYear(),
            fuel_type: '',
            transmission: '',
            condition: '',
            mileage_max: 300000,
            location: '',
            sort_by: 'created_at',
            sort_order: 'desc',
        };

        setLocalFilters(newFilters);

        // Create clean filters object with only non-empty values
        const cleanFilters: Record<string, string | number> =
            Object.fromEntries(
                Object.entries(newFilters)
                    .filter(
                        ([key, value]) =>
                            value !== '' &&
                            value !== null &&
                            value !== undefined &&
                            key !== 'search',
                    )
                    .map(([key, value]) => {
                        if (
                            [
                                'min_price',
                                'max_price',
                                'year_min',
                                'year_max',
                                'mileage_max',
                            ].includes(key)
                        ) {
                            return [key, Number(value)] as [string, number];
                        }
                        return [key, String(value)] as [string, string];
                    }),
            );

        router.get('/pesquisar', cleanFilters, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    const formatPrice = (price: number | string | undefined): string => {
        if (price === undefined || price === '') return 'R$ 0,00';
        const priceNumber =
            typeof price === 'string' ? parseFloat(price) : price;
        return new Intl.NumberFormat('pt-BR', {
            style: 'currency',
            currency: 'BRL',
        }).format(priceNumber);
    };

    const formatMileage = (mileage: number | string | undefined): string => {
        if (mileage === undefined || mileage === '') return '0 km';
        const mileageNumber =
            typeof mileage === 'string' ? parseFloat(mileage) : mileage;
        return new Intl.NumberFormat('pt-BR').format(mileageNumber) + ' km';
    };
    return (
        <MainLayout categories={categories}>
            <Head title="Pesquisar Veículos" />

            <div className="min-h-screen bg-gray-50">
                <div className="container mx-auto px-4 py-6">
                    <div className="flex flex-col gap-6 lg:flex-row">
                        {/* Sidebar de filtros */}
                        <div
                            className={`lg:w-80 ${showFilters ? 'block' : 'hidden lg:block'}`}
                        >
                            <Card className="sticky top-6">
                                <CardHeader className="flex flex-row items-center justify-between">
                                    <CardTitle>Filtros</CardTitle>
                                    <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={clearFilters}
                                        className="text-sm"
                                    >
                                        Limpar
                                    </Button>
                                </CardHeader>
                                <CardContent className="space-y-6">
                                    {/* Categoria */}
                                    <div>
                                        <Label>Categoria</Label>
                                        <Select
                                            value={String(
                                                localFilters.category_id || '',
                                            )}
                                            onValueChange={(value) =>
                                                handleFilterChange(
                                                    'category_id',
                                                    value,
                                                )
                                            }
                                        >
                                            <SelectTrigger>
                                                <SelectValue placeholder="Todas as categorias" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="all">
                                                    Todas as categorias
                                                </SelectItem>
                                                {categories.map((category) => (
                                                    <SelectItem
                                                        key={category.id}
                                                        value={category.id.toString()}
                                                    >
                                                        {category.name}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                    </div>

                                    {/* Marca */}
                                    <div>
                                        <Label>Marca</Label>
                                        <Select
                                            value={String(
                                                localFilters.brand_id || '',
                                            )}
                                            onValueChange={(value) =>
                                                handleFilterChange(
                                                    'brand_id',
                                                    value,
                                                )
                                            }
                                        >
                                            <SelectTrigger>
                                                <SelectValue placeholder="Todas as marcas" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="all">
                                                    Todas as marcas
                                                </SelectItem>
                                                {brands.map((brand) => (
                                                    <SelectItem
                                                        key={brand.id}
                                                        value={brand.id.toString()}
                                                    >
                                                        {brand.name}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                    </div>

                                    {/* Faixa de preço */}
                                    <div>
                                        <Label>Preço</Label>
                                        <div className="space-y-3">
                                            <Slider
                                                value={[
                                                    Number(
                                                        localFilters.min_price,
                                                    ) || 0,
                                                    Number(
                                                        localFilters.max_price,
                                                    ) || 1000000,
                                                ]}
                                                onValueChange={([min, max]) => {
                                                    handleFilterChange(
                                                        'min_price',
                                                        min,
                                                    );
                                                    handleFilterChange(
                                                        'max_price',
                                                        max,
                                                    );
                                                }}
                                                max={
                                                    priceRange?.max_price ||
                                                    1000000
                                                }
                                                min={priceRange?.min_price || 0}
                                                step={1000}
                                                className="w-full"
                                            />
                                            <div className="flex justify-between text-sm text-gray-600">
                                                <span>
                                                    {formatPrice(
                                                        localFilters.min_price,
                                                    )}
                                                </span>
                                                <span>
                                                    {formatPrice(
                                                        localFilters.max_price,
                                                    )}
                                                </span>
                                            </div>
                                        </div>
                                    </div>

                                    {/* Ano */}
                                    <div>
                                        <Label>Ano</Label>
                                        <div className="grid grid-cols-2 gap-2">
                                            <div>
                                                <Label className="text-xs">
                                                    De
                                                </Label>
                                                <Input
                                                    type="number"
                                                    value={localFilters.year_min.toString()}
                                                    onChange={(e) =>
                                                        handleFilterChange(
                                                            'year_min',
                                                            e.target.value
                                                                ? parseInt(
                                                                      e.target
                                                                          .value,
                                                                      10,
                                                                  )
                                                                : 0,
                                                        )
                                                    }
                                                    min={
                                                        yearRange?.min_year ||
                                                        1990
                                                    }
                                                    max={
                                                        yearRange?.max_year ||
                                                        new Date().getFullYear()
                                                    }
                                                />
                                            </div>
                                            <div>
                                                <Label className="text-xs">
                                                    Até
                                                </Label>
                                                <Input
                                                    type="number"
                                                    value={localFilters.year_max.toString()}
                                                    onChange={(e) =>
                                                        handleFilterChange(
                                                            'year_max',
                                                            e.target.value
                                                                ? parseInt(
                                                                      e.target
                                                                          .value,
                                                                      10,
                                                                  )
                                                                : 0,
                                                        )
                                                    }
                                                    min={
                                                        yearRange?.min_year ||
                                                        1990
                                                    }
                                                    max={
                                                        yearRange?.max_year ||
                                                        new Date().getFullYear()
                                                    }
                                                />
                                            </div>
                                        </div>
                                    </div>

                                    {/* Botão Aplicar Filtros */}
                                    <div className="border-t pt-4">
                                        <Button
                                            onClick={applyFilters}
                                            className="w-full"
                                        >
                                            Aplicar Filtros
                                        </Button>
                                    </div>
                                </CardContent>
                            </Card>
                        </div>

                        {/* Conteúdo principal */}
                        <div className="flex-1">
                            {/* Barra de resultados e ordenação */}
                            <div className="mb-6 flex justify-end">
                                <div className="flex items-center gap-2">
                                    {/* Ordenação */}
                                    <Select
                                        value={`${localFilters.sort_by}-${localFilters.sort_order}`}
                                        onValueChange={(value) => {
                                            const [sortBy, sortOrder] =
                                                value.split('-');

                                            const newFilters = {
                                                ...localFilters,
                                                sort_by: sortBy,
                                                sort_order: sortOrder,
                                            };

                                            setLocalFilters(newFilters);

                                            // Apply filters immediately with the new values
                                            const cleanFilters =
                                                Object.fromEntries(
                                                    Object.entries(
                                                        newFilters,
                                                    ).filter(
                                                        ([_, value]) =>
                                                            value !== '' &&
                                                            value !== null &&
                                                            value !==
                                                                undefined &&
                                                            value !== 'all',
                                                    ),
                                                );

                                            router.get(
                                                '/pesquisar',
                                                cleanFilters,
                                                {
                                                    preserveState: true,
                                                    preserveScroll: true,
                                                },
                                            );
                                        }}
                                    >
                                        <SelectTrigger className="w-48">
                                            <SelectValue />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="created_at-desc">
                                                Mais recentes
                                            </SelectItem>
                                            <SelectItem value="created_at-asc">
                                                Mais antigos
                                            </SelectItem>
                                            <SelectItem value="price-asc">
                                                Menor preço
                                            </SelectItem>
                                            <SelectItem value="price-desc">
                                                Maior preço
                                            </SelectItem>
                                            <SelectItem value="title-asc">
                                                A-Z
                                            </SelectItem>
                                            <SelectItem value="title-desc">
                                                Z-A
                                            </SelectItem>
                                        </SelectContent>
                                    </Select>

                                    {/* Modo de visualização */}
                                    <div className="flex rounded-md border">
                                        <Button
                                            variant={
                                                viewMode === 'grid'
                                                    ? 'default'
                                                    : 'ghost'
                                            }
                                            size="sm"
                                            onClick={() => setViewMode('grid')}
                                            className="rounded-r-none"
                                        >
                                            <Grid className="h-4 w-4" />
                                        </Button>
                                        <Button
                                            variant={
                                                viewMode === 'list'
                                                    ? 'default'
                                                    : 'ghost'
                                            }
                                            size="sm"
                                            onClick={() => setViewMode('list')}
                                            className="rounded-l-none"
                                        >
                                            <List className="h-4 w-4" />
                                        </Button>
                                    </div>
                                </div>
                            </div>

                            {/* Lista de anúncios */}
                            {advertisements.data.length > 0 ? (
                                <div
                                    className={
                                        viewMode === 'grid'
                                            ? 'grid grid-cols-1 gap-6 md:grid-cols-2 xl:grid-cols-3'
                                            : 'space-y-4'
                                    }
                                >
                                    {ads.map((ad) => (
                                        <Card
                                            key={ad.id}
                                            className="overflow-hidden transition-shadow hover:shadow-lg"
                                        >
                                            <div
                                                className={
                                                    viewMode === 'grid'
                                                        ? ''
                                                        : 'flex'
                                                }
                                            >
                                                {/* Imagem */}
                                                <div
                                                    className={
                                                        viewMode === 'grid'
                                                            ? 'aspect-video'
                                                            : 'h-32 w-48 flex-shrink-0'
                                                    }
                                                >
                                                    {ad.featured_image ? (
                                                        <img
                                                            src={
                                                                ad
                                                                    .featured_image
                                                                    .url
                                                            }
                                                            alt={
                                                                ad
                                                                    .featured_image
                                                                    .alt
                                                            }
                                                            className="h-full w-full object-cover"
                                                        />
                                                    ) : (
                                                        <div className="flex h-full w-full items-center justify-center bg-gray-200">
                                                            <span className="text-gray-400">
                                                                Sem imagem
                                                            </span>
                                                        </div>
                                                    )}
                                                </div>

                                                {/* Conteúdo */}
                                                <CardContent
                                                    className={`p-4 ${viewMode === 'list' ? 'flex-1' : ''}`}
                                                >
                                                    <div className="space-y-2">
                                                        <h3 className="line-clamp-2 text-lg font-semibold">
                                                            <Link
                                                                href={`/anuncios/${ad.id}`}
                                                                className="hover:text-blue-600"
                                                            >
                                                                {ad.title}
                                                            </Link>
                                                        </h3>

                                                        <div className="flex flex-wrap gap-2 text-sm text-gray-600">
                                                            <span>
                                                                {
                                                                    ad.vehicle
                                                                        .brand
                                                                        .name
                                                                }
                                                            </span>
                                                            <span>•</span>
                                                            <span>
                                                                {
                                                                    ad.vehicle
                                                                        .year
                                                                }
                                                            </span>
                                                            <span>•</span>
                                                            <span>
                                                                {formatMileage(
                                                                    ad.vehicle
                                                                        .mileage,
                                                                )}
                                                            </span>
                                                        </div>

                                                        <div className="flex flex-wrap gap-1">
                                                            <Badge variant="secondary">
                                                                {fuelTypes[
                                                                    ad.vehicle
                                                                        .fuel_type
                                                                ] ||
                                                                    ad.vehicle
                                                                        .fuel_type}
                                                            </Badge>
                                                            <Badge variant="secondary">
                                                                {transmissions[
                                                                    ad.vehicle
                                                                        .transmission
                                                                ] ||
                                                                    ad.vehicle
                                                                        .transmission}
                                                            </Badge>
                                                        </div>

                                                        <div className="flex items-center justify-between">
                                                            <span className="text-2xl font-bold text-green-600">
                                                                {formatPrice(
                                                                    ad.price,
                                                                )}
                                                            </span>
                                                            <span className="text-sm text-gray-500">
                                                                {ad.location}
                                                            </span>
                                                        </div>
                                                    </div>
                                                </CardContent>
                                            </div>
                                        </Card>
                                    ))}
                                </div>
                            ) : (
                                <div className="py-12 text-center">
                                    <h3 className="mb-2 text-xl font-semibold">
                                        Nenhum veículo encontrado
                                    </h3>
                                    <p className="mb-4 text-gray-600">
                                        Tente ajustar os filtros ou fazer uma
                                        nova pesquisa
                                    </p>
                                    <Button onClick={clearFilters}>
                                        Limpar filtros
                                    </Button>
                                </div>
                            )}

                            {/* Paginação */}
                            {links && links.length > 3 && (
                                <div className="mt-8 flex justify-center">
                                    <div className="flex gap-2">
                                        {links.map((link, _index) => (
                                            <Button
                                                key={_index}
                                                variant={
                                                    link.active
                                                        ? 'default'
                                                        : 'outline'
                                                }
                                                size="sm"
                                                onClick={() => {
                                                    if (link.url) {
                                                        router.get(link.url);
                                                    }
                                                }}
                                                disabled={!link.url}
                                                dangerouslySetInnerHTML={{
                                                    __html: link.label,
                                                }}
                                            />
                                        ))}
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            </div>
        </MainLayout>
    );
}
