<?php

use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

use App\Http\Controllers\Public\CategoryController;

Route::get('/', [\App\Http\Controllers\Public\WelcomeController::class, 'index'])->name('home');

// Rotas de categorias
Route::get('/categorias', [CategoryController::class, 'index'])->name('categories.index');
Route::get('/categorias/{category:slug}', [CategoryController::class, 'show'])->name('categories.show');

// Rotas públicas
Route::get('/veiculos', [\App\Http\Controllers\Public\VehicleController::class, 'index'])->name('vehicles.index');
Route::get('/veiculos/{vehicle:slug}', [\App\Http\Controllers\Public\VehicleController::class, 'show'])->name('vehicles.show');

// Rotas de peças
Route::get('/pecas', [\App\Http\Controllers\Public\PartController::class, 'index'])->name('parts.index');
Route::get('/pecas/{part:slug}', [\App\Http\Controllers\Public\PartController::class, 'show'])->name('parts.show');
Route::get('/pecas/veiculo/compativel', [\App\Http\Controllers\Public\PartController::class, 'searchByVehicle'])->name('parts.vehicle-compatible');

// Rotas de pesquisa
Route::get('/pesquisar', [\App\Http\Controllers\Public\SearchController::class, 'index'])->name('search.index');
Route::get('/api/search/suggestions', [\App\Http\Controllers\Public\SearchController::class, 'suggestions'])->name('search.suggestions');

// Rotas de anúncios
Route::get('/anuncios', [\App\Http\Controllers\Public\AdvertisementController::class, 'index'])->name('advertisements.index');
Route::get('/anuncios/{advertisement}', [\App\Http\Controllers\Public\AdvertisementController::class, 'show'])->name('advertisements.show');
Route::post('/anuncios/{advertisement}/contato', [\App\Http\Controllers\Public\AdvertisementController::class, 'contact'])->name('advertisements.contact');
Route::post('/anuncios/{advertisement}/favoritar', [\App\Http\Controllers\Public\AdvertisementController::class, 'favorite'])->name('advertisements.favorite')->middleware('auth');
Route::post('/anuncios/{advertisement}/denunciar', [\App\Http\Controllers\Public\AdvertisementController::class, 'report'])->name('advertisements.report');

// Rota para anunciar (redireciona para criação de anúncio ou login)
Route::get('/anunciar', function () {
    if (\Illuminate\Support\Facades\Auth::check()) {
        return redirect()->route('user.advertisements.create');
    }
    return redirect()->route('login')->with('message', 'Faça login para anunciar seu veículo');
})->name('advertise');

// Rotas de usuários
Route::get('/usuario/{user}', [\App\Http\Controllers\Public\UserController::class, 'show'])->name('users.show');
Route::get('/usuario/{user}/anuncios', [\App\Http\Controllers\Public\UserController::class, 'advertisements'])->name('users.advertisements');
Route::post('/usuario/{user}/contato', [\App\Http\Controllers\Public\UserController::class, 'contact'])->name('users.contact');
Route::post('/usuario/{user}/denunciar', [\App\Http\Controllers\Public\UserController::class, 'report'])->name('users.report');

// Rotas de vendedores
Route::get('/vendedor/{seller}', [\App\Http\Controllers\Public\SellerController::class, 'show'])->name('sellers.show');
Route::get('/vendedor/{seller}/anuncios', [\App\Http\Controllers\Public\SellerController::class, 'advertisements'])->name('sellers.advertisements');
Route::post('/vendedor/{seller}/contato', [\App\Http\Controllers\Public\SellerController::class, 'contact'])->name('sellers.contact');
Route::post('/vendedor/{seller}/denunciar', [\App\Http\Controllers\Public\SellerController::class, 'report'])->name('sellers.report');
Route::post('/vendedor/{seller}/seguir', [\App\Http\Controllers\Public\SellerController::class, 'follow'])->name('sellers.follow')->middleware('auth');

// Comparison routes
Route::prefix('comparacao')->name('comparison.')->group(function () {
    Route::get('/', [\App\Http\Controllers\Public\ComparisonController::class, 'index'])->name('index');
    Route::post('/adicionar/{advertisement}', [\App\Http\Controllers\Public\ComparisonController::class, 'add'])->name('add');
    Route::delete('/remover/{advertisement}', [\App\Http\Controllers\Public\ComparisonController::class, 'remove'])->name('remove');
    Route::delete('/limpar', [\App\Http\Controllers\Public\ComparisonController::class, 'clear'])->name('clear');
    Route::get('/contar', [\App\Http\Controllers\Public\ComparisonController::class, 'count'])->name('count');
    Route::get('/buscar', [\App\Http\Controllers\Public\ComparisonController::class, 'search'])->name('search');
});

// Rotas de usuário autenticado
Route::middleware('auth')->prefix('minha-conta')->name('user.')->group(function () {
    // Anúncios do usuário
    Route::resource('anuncios', \App\Http\Controllers\User\AdvertisementController::class)->names([
        'index' => 'advertisements.index',
        'create' => 'advertisements.create',
        'store' => 'advertisements.store',
        'show' => 'advertisements.show',
        'edit' => 'advertisements.edit',
        'update' => 'advertisements.update',
        'destroy' => 'advertisements.destroy',
    ]);

    // Ações específicas de anúncios
    Route::post('/anuncios/{advertisement}/publicar', [\App\Http\Controllers\User\AdvertisementController::class, 'publish'])->name('advertisements.publish');
    Route::post('/anuncios/{advertisement}/despublicar', [\App\Http\Controllers\User\AdvertisementController::class, 'unpublish'])->name('advertisements.unpublish');

    // Chat system
    Route::prefix('chat')->name('chat.')->group(function () {
        Route::get('/', [\App\Http\Controllers\User\ChatController::class, 'index'])->name('index');
        Route::get('/{chat}', [\App\Http\Controllers\User\ChatController::class, 'show'])->name('show');
        Route::post('/start/{advertisement}', [\App\Http\Controllers\User\ChatController::class, 'start'])->name('start');
        Route::post('/{chat}/message', [\App\Http\Controllers\User\ChatController::class, 'sendMessage'])->name('send-message');
        Route::patch('/{chat}/read', [\App\Http\Controllers\User\ChatController::class, 'markAsRead'])->name('mark-read');
        Route::delete('/{chat}', [\App\Http\Controllers\User\ChatController::class, 'destroy'])->name('destroy');
        Route::get('/unread/count', [\App\Http\Controllers\User\ChatController::class, 'unreadCount'])->name('unread-count');
    });

    // Favorites system
    Route::prefix('favoritos')->name('favorites.')->group(function () {
        Route::get('/', [\App\Http\Controllers\User\FavoriteController::class, 'index'])->name('index');
        Route::post('/{advertisement}', [\App\Http\Controllers\User\FavoriteController::class, 'store'])->name('store');
        Route::delete('/{advertisement}', [\App\Http\Controllers\User\FavoriteController::class, 'destroy'])->name('destroy');
        Route::delete('/', [\App\Http\Controllers\User\FavoriteController::class, 'destroyMultiple'])->name('destroy-multiple');
        Route::get('/check/{advertisement}', [\App\Http\Controllers\User\FavoriteController::class, 'check'])->name('check');
        Route::get('/count', [\App\Http\Controllers\User\FavoriteController::class, 'count'])->name('count');
    });

    // Profile routes
    Route::prefix('perfil')->name('profile.')->group(function () {
        Route::get('/', [\App\Http\Controllers\User\ProfileController::class, 'show'])->name('show');
        Route::get('/editar', [\App\Http\Controllers\User\ProfileController::class, 'edit'])->name('edit');
        Route::put('/', [\App\Http\Controllers\User\ProfileController::class, 'update'])->name('update');
        Route::put('/senha', [\App\Http\Controllers\User\ProfileController::class, 'updatePassword'])->name('update-password');
        Route::post('/avatar', [\App\Http\Controllers\User\ProfileController::class, 'updateAvatar'])->name('update-avatar');
        Route::put('/notificacoes', [\App\Http\Controllers\User\ProfileController::class, 'updateNotifications'])->name('update-notifications');
        Route::delete('/', [\App\Http\Controllers\User\ProfileController::class, 'destroy'])->name('destroy');
    });

    // Notifications routes
    Route::prefix('notificacoes')->name('notifications.')->group(function () {
        Route::get('/', [\App\Http\Controllers\User\NotificationController::class, 'index'])->name('index');
        Route::post('/{id}/marcar-lida', [\App\Http\Controllers\User\NotificationController::class, 'markAsRead'])->name('mark-read');
        Route::post('/marcar-todas-lidas', [\App\Http\Controllers\User\NotificationController::class, 'markAllAsRead'])->name('mark-all-read');
        Route::delete('/{id}', [\App\Http\Controllers\User\NotificationController::class, 'destroy'])->name('destroy');
        Route::delete('/excluir-lidas', [\App\Http\Controllers\User\NotificationController::class, 'deleteAllRead'])->name('delete-read');
        Route::get('/count', [\App\Http\Controllers\User\NotificationController::class, 'getCount'])->name('count');
        Route::get('/recent', [\App\Http\Controllers\User\NotificationController::class, 'getRecent'])->name('recent');
        Route::put('/preferencias', [\App\Http\Controllers\User\NotificationController::class, 'updatePreferences'])->name('preferences');
    });

    // Offers routes
    Route::prefix('ofertas')->name('offers.')->group(function () {
        Route::get('/', [\App\Http\Controllers\User\OfferController::class, 'index'])->name('index');
        Route::get('/criar/{advertisement}', [\App\Http\Controllers\User\OfferController::class, 'create'])->name('create');
        Route::post('/{advertisement}', [\App\Http\Controllers\User\OfferController::class, 'store'])->name('store');
        Route::get('/{offer}', [\App\Http\Controllers\User\OfferController::class, 'show'])->name('show');
        Route::post('/{offer}/aceitar', [\App\Http\Controllers\User\OfferController::class, 'accept'])->name('accept');
        Route::post('/{offer}/rejeitar', [\App\Http\Controllers\User\OfferController::class, 'reject'])->name('reject');
        Route::post('/{offer}/cancelar', [\App\Http\Controllers\User\OfferController::class, 'cancel'])->name('cancel');
        Route::post('/{offer}/contra-oferta', [\App\Http\Controllers\User\OfferController::class, 'counter'])->name('counter');
        Route::get('/stats', [\App\Http\Controllers\User\OfferController::class, 'getStats'])->name('stats');
    });
});

// Public profile route
Route::get('/perfil/{user}', [\App\Http\Controllers\User\ProfileController::class, 'publicProfile'])->name('profile.public');

// Páginas estáticas
Route::get('/ajuda', function () {
    return Inertia::render('Static/Help');
})->name('help');

Route::get('/contato', function () {
    return Inertia::render('Static/Contact');
})->name('contact');

Route::get('/seguranca', function () {
    return Inertia::render('Static/Security');
})->name('security');

Route::get('/termos', function () {
    return Inertia::render('Static/Terms');
})->name('terms');

Route::get('/privacidade', function () {
    return Inertia::render('Static/Privacy');
})->name('privacy');

// Media upload routes (authenticated)
Route::middleware('auth')->prefix('media')->name('media.')->group(function () {
    Route::post('/upload-images', [\App\Http\Controllers\MediaController::class, 'uploadImages'])->name('upload-images');
    Route::post('/upload-image', [\App\Http\Controllers\MediaController::class, 'uploadImage'])->name('upload-image');
    Route::delete('/delete-image', [\App\Http\Controllers\MediaController::class, 'deleteImage'])->name('delete-image');
    Route::get('/image-info', [\App\Http\Controllers\MediaController::class, 'getImageInfo'])->name('image-info');
    Route::post('/optimize-image', [\App\Http\Controllers\MediaController::class, 'optimizeImage'])->name('optimize-image');
});

// Rotas autenticadas
Route::middleware(['auth', 'verified'])->group(function () {
    // Dashboard genérico (fallback)
    Route::get('dashboard', function () {
        return Inertia::render('dashboard');
    })->name('dashboard');

    // Dashboard do usuário
    Route::get('minha-conta/dashboard', [\App\Http\Controllers\User\DashboardController::class, 'index'])->name('user.dashboard');

    // Rota de teste para verificar permissões
    Route::get('/test-admin', function () {
        $user = request()->user();
        return response()->json([
            'user' => $user->name,
            'roles' => $user->roles->pluck('name'),
            'permissions' => $user->getAllPermissions()->pluck('name'),
            'can_admin_access' => $user->can('admin.access'),
            'has_permission_admin_access' => $user->hasPermissionTo('admin.access'),
        ]);
    });

    // Admin Dashboard
    Route::get('/admin', [\App\Http\Controllers\Admin\DashboardController::class, 'index'])->name('admin.dashboard')->middleware('admin');

    // Admin - Usuários
    Route::prefix('admin/usuarios')->name('admin.users.')->middleware('admin')->group(function () {
        Route::get('/', [\App\Http\Controllers\Admin\UserController::class, 'index'])->name('index');
        Route::get('/{user}', [\App\Http\Controllers\Admin\UserController::class, 'show'])->name('show');
        Route::get('/{user}/editar', [\App\Http\Controllers\Admin\UserController::class, 'edit'])->name('edit');
        Route::put('/{user}', [\App\Http\Controllers\Admin\UserController::class, 'update'])->name('update');
        Route::delete('/{user}', [\App\Http\Controllers\Admin\UserController::class, 'destroy'])->name('destroy');
        Route::post('/{user}/ativar', [\App\Http\Controllers\Admin\UserController::class, 'activate'])->name('activate');
        Route::post('/{user}/suspender', [\App\Http\Controllers\Admin\UserController::class, 'suspend'])->name('suspend');
        Route::post('/{user}/verificar-email', [\App\Http\Controllers\Admin\UserController::class, 'verifyEmail'])->name('verify-email');
    });

    // Admin - Veículos
    Route::prefix('admin/veiculos')->name('admin.vehicles.')->middleware('admin')->group(function () {
        Route::get('/', [\App\Http\Controllers\Admin\VehicleController::class, 'index'])->name('index');
        Route::get('/novo', [\App\Http\Controllers\Admin\VehicleController::class, 'create'])->name('create');
        Route::post('/', [\App\Http\Controllers\Admin\VehicleController::class, 'store'])->name('store');
        Route::get('/{vehicle}/editar', [\App\Http\Controllers\Admin\VehicleController::class, 'edit'])->name('edit');
        Route::put('/{vehicle}', [\App\Http\Controllers\Admin\VehicleController::class, 'update'])->name('update');
        Route::delete('/{vehicle}', [\App\Http\Controllers\Admin\VehicleController::class, 'destroy'])->name('destroy');
    });

        // Admin - Peças
    Route::prefix('admin/pecas')->name('admin.parts.')->middleware('admin')->group(function () {
        Route::get('/', [\App\Http\Controllers\Admin\PartController::class, 'index'])->name('index');
        Route::get('/nova', [\App\Http\Controllers\Admin\PartController::class, 'create'])->name('create');
        Route::post('/', [\App\Http\Controllers\Admin\PartController::class, 'store'])->name('store');
        Route::get('/{part}', [\App\Http\Controllers\Admin\PartController::class, 'show'])->name('show');
        Route::get('/{part}/editar', [\App\Http\Controllers\Admin\PartController::class, 'edit'])->name('edit');
        Route::put('/{part}', [\App\Http\Controllers\Admin\PartController::class, 'update'])->name('update');
        Route::delete('/{part}', [\App\Http\Controllers\Admin\PartController::class, 'destroy'])->name('destroy');
    });

    // Admin - Anúncios
    Route::prefix('admin/anuncios')->name('admin.advertisements.')->middleware('admin')->group(function () {
        Route::get('/', [\App\Http\Controllers\Admin\AdvertisementController::class, 'index'])->name('index');
        Route::get('/novo', [\App\Http\Controllers\Admin\AdvertisementController::class, 'create'])->name('create');
        Route::post('/', [\App\Http\Controllers\Admin\AdvertisementController::class, 'store'])->name('store');
        Route::get('/{advertisement}', [\App\Http\Controllers\Admin\AdvertisementController::class, 'show'])->name('show');
        Route::get('/{advertisement}/editar', [\App\Http\Controllers\Admin\AdvertisementController::class, 'edit'])->name('edit');
        Route::put('/{advertisement}', [\App\Http\Controllers\Admin\AdvertisementController::class, 'update'])->name('update');
        Route::delete('/{advertisement}', [\App\Http\Controllers\Admin\AdvertisementController::class, 'destroy'])->name('destroy');
        
        // Ações de moderação
        Route::post('/{advertisement}/aprovar', [\App\Http\Controllers\Admin\AdvertisementController::class, 'approve'])
            ->name('approve')
            ->middleware('can:approve_advertisement');
            
        Route::post('/{advertisement}/rejeitar', [\App\Http\Controllers\Admin\AdvertisementController::class, 'reject'])
            ->name('reject')
            ->middleware('can:reject_advertisement');
            
        Route::post('/{advertisement}/publicar', [\App\Http\Controllers\Admin\AdvertisementController::class, 'publish'])
            ->name('publish')
            ->middleware('can:publish_advertisement');
            
        Route::post('/{advertisement}/vender', [\App\Http\Controllers\Admin\AdvertisementController::class, 'markAsSold'])
            ->name('markAsSold')
            ->middleware('can:mark_sold_advertisement');
    });
});

require __DIR__.'/settings.php';
require __DIR__.'/auth.php';
