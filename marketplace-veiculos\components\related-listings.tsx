import Link from "next/link"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { MapPin, Calendar, Fuel } from "lucide-react"

interface RelatedListingsProps {
  currentVehicle: {
    brand: string
    type: string
    price: string
  }
}

const relatedListings = [
  {
    id: 2,
    title: "Honda Civic 2021 LX",
    price: "R$ 82.500",
    location: "São Paulo, SP",
    year: "2021",
    fuel: "Flex",
    mileage: "35.000 km",
    image: "/honda-civic-2021-black-car.jpg",
    type: "Carro",
  },
  {
    id: 3,
    title: "Honda Civic 2020 Sport",
    price: "R$ 75.900",
    location: "Campinas, SP",
    year: "2020",
    fuel: "Flex",
    mileage: "42.000 km",
    image: "/honda-civic-2020-blue-car.jpg",
    type: "Carro",
  },
  {
    id: 4,
    title: "Honda Civic 2023 Touring",
    price: "R$ 135.000",
    location: "Santos, SP",
    year: "2023",
    fuel: "Flex",
    mileage: "12.000 km",
    image: "/honda-civic-2023-white-car.jpg",
    type: "Carro",
  },
  {
    id: 5,
    title: "Honda Civic 2019 EX",
    price: "R$ 68.900",
    location: "São Bernardo, SP",
    year: "2019",
    fuel: "Flex",
    mileage: "58.000 km",
    image: "/honda-civic-2019-red-car.jpg",
    type: "Carro",
  },
]

export function RelatedListings({ currentVehicle }: RelatedListingsProps) {
  return (
    <section>
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-2xl font-bold">Anúncios similares</h2>
        <Button variant="outline" asChild>
          <Link href={`/anuncios?brand=${currentVehicle.brand}&type=${currentVehicle.type}`}>Ver todos</Link>
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {relatedListings.map((listing) => (
          <Link key={listing.id} href={`/anuncio/${listing.id}`}>
            <Card className="group hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
              <div className="relative">
                <img
                  src={listing.image || "/placeholder.svg"}
                  alt={listing.title}
                  className="w-full h-48 object-cover rounded-t-lg"
                />
                <Badge variant="secondary" className="absolute top-3 right-3 bg-background/90">
                  {listing.type}
                </Badge>
              </div>

              <CardContent className="p-4">
                <h3 className="font-semibold text-lg mb-2 group-hover:text-accent transition-colors line-clamp-2">
                  {listing.title}
                </h3>

                <div className="text-2xl font-bold text-primary mb-3">{listing.price}</div>

                <div className="space-y-2 text-sm text-muted-foreground mb-4">
                  <div className="flex items-center">
                    <MapPin className="h-4 w-4 mr-2 flex-shrink-0" />
                    <span className="truncate">{listing.location}</span>
                  </div>
                  <div className="flex items-center">
                    <Calendar className="h-4 w-4 mr-2 flex-shrink-0" />
                    <span>
                      {listing.year} • {listing.mileage}
                    </span>
                  </div>
                  <div className="flex items-center">
                    <Fuel className="h-4 w-4 mr-2 flex-shrink-0" />
                    <span>{listing.fuel}</span>
                  </div>
                </div>

                <Button className="w-full bg-transparent" variant="outline">
                  Ver detalhes
                </Button>
              </CardContent>
            </Card>
          </Link>
        ))}
      </div>
    </section>
  )
}
