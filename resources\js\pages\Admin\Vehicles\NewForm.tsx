import { Head, Link, useForm, usePage } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { ArrowLeft, Save, AlertCircle } from 'lucide-react';
import { useEffect, useState } from 'react';
import { FormImageUpload } from '@/components/FormImageUpload';
// Importando tipos específicos para evitar conflitos
import type { 
    Category, 
    CategoryOption, 
    Vehicle as VehicleType, 
    User, 
    Brand, 
    VehicleFeature,
    MediaFile as MediaFileType
} from '@/types';

// Definindo tipos locais para evitar conflitos
type LocalMediaFile = MediaFileType & { original_url: string };

// Extend the Vehicle type to include additional fields
interface ExtendedVehicle extends Omit<VehicleType, 'media' | 'model_year' | 'license_plate' | 'chassis_number' | 'status' | 'seo_title' | 'seo_description' | 'seo_keywords'> {
    media?: LocalMediaFile[];
    model_year?: number | string;
    license_plate: string;
    chassis_number: string;
    status: string;
    seo_title: string;
    seo_description: string;
    seo_keywords: string;
}

// Interface for image files in the form
interface ImageFile {
    id: string;
    file: File;
    preview: string;
    name: string;
    size: number;
    type: string;
}

// Extend the Inertia PageProps to include our custom props
declare module '@inertiajs/core' {
    interface PageProps {
        auth: {
            user: User;
        };
        errors: Record<string, string>;
        vehicle?: ExtendedVehicle;
        brands: Brand[];
        categories: CategoryOption[];
        features: VehicleFeature[];
        fuelTypes: Record<string, string>;
        transmissions: Record<string, string>;
        [key: string]: unknown;
    }
}

interface MediaFile {
    id: number;
    file_name: string;
    original_url: string;
    size: number;
    mime_type: string;
    collection_name: string;
}

interface Vehicle {
    id: number;
    brand_id: number;
    category_id: number;
    model: string;
    year_manufacture: number;
    model_year: number;
    license_plate: string;
    chassis_number: string;
    color: string;
    fuel_type: string;
    transmission: string;
    mileage: number;
    description: string;
    price: number;
    promotional_price: number | null;
    is_negotiable: boolean;
    is_featured: boolean;
    status: string;
    seo_title: string;
    seo_description: string;
    seo_keywords: string;
    features: Array<{ id: number }>;
    media: MediaFile[];
}

interface PageProps {
    auth: {
        user: User;
    };
    errors: Record<string, string>;
    vehicle?: Vehicle;
    brands: Array<{ id: number; name: string }>;
    categories: CategoryOption[];
    fuelTypes: Record<string, string>;
    transmissions: Record<string, string>;
    features: Array<{ id: number; name: string }>;
    [key: string]: any; // Para outras propriedades que possam vir do Inertia
}

interface ImageFile {
    id: string;
    file: File;
    preview: string;
    name: string;
    size: number;
    type: string;
}

export default function VehicleForm() {
    const page = usePage();
    const { 
        errors = {}, 
        vehicle, 
        brands = [], 
        categories = [],
        fuelTypes = {},
        transmissions = {},
        features: allFeatures = [] 
    } = page.props as {
        errors: Record<string, string>;
        vehicle?: ExtendedVehicle;
        brands: Brand[];
        categories: CategoryOption[];
        fuelTypes: Record<string, string>;
        transmissions: Record<string, string>;
        features: VehicleFeature[];
        [key: string]: unknown;
    };

    const isEdit = !!vehicle;
    
    const { data, setData, post, put, processing } = useForm({
        brand_id: vehicle?.brand_id?.toString() || '',
        category_id: vehicle?.category_id?.toString() || '',
        model: vehicle?.model || '',
        year_manufacture: vehicle?.year_manufacture?.toString() || '',
        model_year: vehicle?.model_year?.toString() || '',
        license_plate: vehicle?.license_plate || '',
        chassis_number: vehicle?.chassis_number || '',
        color: vehicle?.color || '',
        fuel_type: vehicle?.fuel_type || '',
        transmission: vehicle?.transmission || '',
        mileage: vehicle?.mileage?.toString() || '',
        description: vehicle?.description || '',
        price: vehicle?.price?.toString() || '',
        promotional_price: vehicle?.promotional_price?.toString() || '',
        is_negotiable: vehicle?.is_negotiable || false,
        is_featured: vehicle?.is_featured || false,
        status: vehicle?.status || 'draft',
        seo_title: vehicle?.seo_title || '',
        seo_description: vehicle?.seo_description || '',
        seo_keywords: vehicle?.seo_keywords || '',
        features: vehicle?.features?.map((f: { id: number }) => f.id) || [],
        images: [] as Array<File | string>,
        deleted_image_ids: [] as number[],
    });

    const [selectedBrand, setSelectedBrand] = useState<string>(vehicle?.brand_id?.toString() || '');
    // Removido setSelectedCategory pois não está sendo usado
    const [selectedFuelType, setSelectedFuelType] = useState<string>(vehicle?.fuel_type || 'gasoline');
    const [selectedTransmission, setSelectedTransmission] = useState<string>(vehicle?.transmission || 'manual');
    const [selectedStatus, setSelectedStatus] = useState<string>(vehicle?.status || 'draft');
    const [images, setImages] = useState<ImageFile[]>([]);
    const [featuredImage, setFeaturedImage] = useState<ImageFile | null>(null);
    const [selectedFeatures, setSelectedFeatures] = useState<number[]>(
        isEdit ? vehicle?.features?.map(f => f.id) || [] : []
    );

    const handleFeatureToggle = (featureId: number) => {
        setSelectedFeatures(prev => 
            prev.includes(featureId)
                ? prev.filter(id => id !== featureId)
                : [...prev, featureId]
        );
    };

    // Função para remover imagem (comentada pois não está sendo usada no momento)
    // const handleRemoveImage = (id: string) => {
    //     setImages(images.filter(img => img.id !== id));
        
    //     // Se a imagem já foi enviada para o servidor, adiciona o ID à lista de imagens a serem removidas
    //     if (!id.startsWith('local-')) {
    //         const imageId = parseInt(id, 10);
    //         if (!isNaN(imageId)) {
    //             setData('deleted_image_ids', [...data.deleted_image_ids, imageId]);
    //         }
    //     }
    // };

    // Carregar imagens existentes ao editar
    useEffect(() => {
        if (isEdit && vehicle?.media) {
            // Carregar imagens comuns
            const mediaFiles = vehicle.media
                .filter((media) => media.collection_name === 'images')
                .map((media) => {
                    const file = new File([], media.file_name, { type: media.mime_type });
                    return {
                        id: media.id.toString(),
                        file,
                        preview: media.original_url,
                        name: media.file_name,
                        size: media.size,
                        type: media.mime_type,
                    };
                });

            setImages(mediaFiles);

            // Definir imagem em destaque
            const featured = vehicle.media.find(media => media.collection_name === 'featured');
            if (featured) {
                const file = new File([], featured.file_name, { type: featured.mime_type });
                setFeaturedImage({
                    id: featured.id.toString(),
                    file,
                    preview: featured.original_url,
                    name: featured.file_name,
                    size: featured.size,
                    type: featured.mime_type,
                });
            }
        }
    }, [isEdit, vehicle?.media]);

    // Atualizar dados do formulário quando os estados mudarem
    useEffect(() => {
        setData('brand_id', selectedBrand);
        setData('fuel_type', selectedFuelType);
        setData('transmission', selectedTransmission);
        setData('status', selectedStatus);
        setData('features', selectedFeatures);
    }, [
        selectedBrand, 
        selectedFuelType, 
        selectedTransmission, 
        selectedStatus, 
        selectedFeatures,
        setData
    ]);

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        
        const formData = new FormData();
        
        // Adiciona os campos do formulário
        Object.entries(data).forEach(([key, value]) => {
            if (key === 'images') {
                // Para imagens, adiciona cada arquivo individualmente
                (value as File[]).forEach((file, index) => {
                    if (file instanceof File) {
                        formData.append(`images[${index}]`, file);
                    }
                });
            } else if (key === 'deleted_image_ids') {
                // Para IDs de imagens excluídas, adiciona como array
                (value as number[]).forEach((id, index) => {
                    formData.append(`deleted_image_ids[${index}]`, id.toString());
                });
            } else if (Array.isArray(value)) {
                // Para arrays (como features), adiciona cada item individualmente
                value.forEach((item, index) => {
                    formData.append(`${key}[${index}]`, item.toString());
                });
            } else if (value !== null && value !== undefined) {
                // Para valores simples
                formData.append(key, value.toString());
            }
        });
        
        const options = {
            forceFormData: true,
            onSuccess: () => {
                // Lógica após o sucesso
            },
        };

        if (isEdit && vehicle?.id) {
            put(route('admin.vehicles.update', vehicle.id), formData, options);
        } else {
            post(route('admin.vehicles.store'), formData, options);
        }
    };

    // Categories and other props are already destructured from page props

    return (
        <AppLayout categories={categories as unknown as Category[]}>
            <Head title={isEdit ? 'Editar Veículo' : 'Adicionar Veículo'} />
            
            <div className="container mx-auto py-6">
                <div className="flex justify-between items-center mb-6">
                    <h1 className="text-2xl font-bold">
                        {isEdit ? 'Editar Veículo' : 'Adicionar Veículo'}
                    </h1>
                    <Button variant="outline" asChild>
                        <Link href={route('admin.vehicles.index')}>
                            <ArrowLeft className="w-4 h-4 mr-2" />
                            Voltar
                        </Link>
                    </Button>
                </div>

                <form onSubmit={handleSubmit} className="space-y-6">
                    <div className="grid gap-6 md:grid-cols-3">
                        {/* Informações Básicas */}
                        <Card className="md:col-span-2">
                            <CardHeader>
                                <CardTitle>Informações do Veículo</CardTitle>
                                <CardDescription>
                                    Preencha as informações básicas do veículo
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div className="space-y-2">
                                        <Label htmlFor="brand_id">Marca</Label>
                                        <Select 
                                            value={selectedBrand} 
                                            onValueChange={setSelectedBrand}
                                        >
                                            <SelectTrigger>
                                                <SelectValue placeholder="Selecione uma marca" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                {brands.map((brand) => (
                                                    <SelectItem key={brand.id} value={brand.id.toString()}>
                                                        {brand.name}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                        {errors.brand_id && (
                                            <p className="text-sm text-red-500 flex items-center gap-1">
                                                <AlertCircle className="w-4 h-4" />
                                                {errors.brand_id}
                                            </p>
                                        )}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="model">Modelo</Label>
                                        <Input
                                            id="model"
                                            value={data.model}
                                            onChange={(e) => setData('model', e.target.value)}
                                            placeholder="Ex: Onix"
                                        />
                                        {errors.model && (
                                            <p className="text-sm text-red-500 flex items-center gap-1">
                                                <AlertCircle className="w-4 h-4" />
                                                {errors.model}
                                            </p>
                                        )}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="year_manufacture">Ano de Fabricação</Label>
                                        <Input
                                            id="year_manufacture"
                                            type="number"
                                            value={data.year_manufacture}
                                            onChange={(e) => setData('year_manufacture', e.target.value)}
                                            min="1900"
                                            max={new Date().getFullYear() + 1}
                                        />
                                        {errors.year_manufacture && (
                                            <p className="text-sm text-red-500 flex items-center gap-1">
                                                <AlertCircle className="w-4 h-4" />
                                                {errors.year_manufacture}
                                            </p>
                                        )}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="model_year">Ano do Modelo</Label>
                                        <Input
                                            id="model_year"
                                            type="number"
                                            value={data.model_year}
                                            onChange={(e) => setData('model_year', e.target.value)}
                                            min="1900"
                                            max={new Date().getFullYear() + 1}
                                        />
                                        {errors.model_year && (
                                            <p className="text-sm text-red-500 flex items-center gap-1">
                                                <AlertCircle className="w-4 h-4" />
                                                {errors.model_year}
                                            </p>
                                        )}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="license_plate">Placa</Label>
                                        <Input
                                            id="license_plate"
                                            value={data.license_plate}
                                            onChange={(e) => setData('license_plate', e.target.value)}
                                            placeholder="AAA-0000"
                                        />
                                        {errors.license_plate && (
                                            <p className="text-sm text-red-500 flex items-center gap-1">
                                                <AlertCircle className="w-4 h-4" />
                                                {errors.license_plate}
                                            </p>
                                        )}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="chassis_number">Número do Chassi</Label>
                                        <Input
                                            id="chassis_number"
                                            value={data.chassis_number}
                                            onChange={(e) => setData('chassis_number', e.target.value)}
                                            placeholder="Número do chassi"
                                        />
                                        {errors.chassis_number && (
                                            <p className="text-sm text-red-500 flex items-center gap-1">
                                                <AlertCircle className="w-4 h-4" />
                                                {errors.chassis_number}
                                            </p>
                                        )}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="color">Cor</Label>
                                        <Input
                                            id="color"
                                            value={data.color}
                                            onChange={(e) => setData('color', e.target.value)}
                                            placeholder="Ex: Preto"
                                        />
                                        {errors.color && (
                                            <p className="text-sm text-red-500 flex items-center gap-1">
                                                <AlertCircle className="w-4 h-4" />
                                                {errors.color}
                                            </p>
                                        )}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="fuel_type">Combustível</Label>
                                        <Select 
                                            value={selectedFuelType} 
                                            onValueChange={setSelectedFuelType}
                                        >
                                            <SelectTrigger>
                                                <SelectValue placeholder="Selecione o combustível" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                {Object.entries(fuelTypes).map(([value, label]) => (
                                                    <SelectItem key={value} value={value}>
                                                        {label}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                        {errors.fuel_type && (
                                            <p className="text-sm text-red-500 flex items-center gap-1">
                                                <AlertCircle className="w-4 h-4" />
                                                {errors.fuel_type}
                                            </p>
                                        )}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="transmission">Câmbio</Label>
                                        <Select 
                                            value={selectedTransmission} 
                                            onValueChange={setSelectedTransmission}
                                        >
                                            <SelectTrigger>
                                                <SelectValue placeholder="Selecione o câmbio" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                {Object.entries(transmissions).map(([value, label]) => (
                                                    <SelectItem key={value} value={value}>
                                                        {label}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                        {errors.transmission && (
                                            <p className="text-sm text-red-500 flex items-center gap-1">
                                                <AlertCircle className="w-4 h-4" />
                                                {errors.transmission}
                                            </p>
                                        )}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="mileage">Quilometragem</Label>
                                        <Input
                                            id="mileage"
                                            type="number"
                                            value={data.mileage}
                                            onChange={(e) => setData('mileage', e.target.value)}
                                            min="0"
                                            step="1"
                                        />
                                        {errors.mileage && (
                                            <p className="text-sm text-red-500 flex items-center gap-1">
                                                <AlertCircle className="w-4 h-4" />
                                                {errors.mileage}
                                            </p>
                                        )}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="price">Preço</Label>
                                        <div className="relative">
                                            <span className="absolute left-3 top-2.5 text-muted-foreground">R$</span>
                                            <Input
                                                id="price"
                                                type="number"
                                                value={data.price}
                                                onChange={(e) => setData('price', e.target.value)}
                                                min="0"
                                                step="0.01"
                                                className="pl-8"
                                            />
                                        </div>
                                        {errors.price && (
                                            <p className="text-sm text-red-500 flex items-center gap-1">
                                                <AlertCircle className="w-4 h-4" />
                                                {errors.price}
                                            </p>
                                        )}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="promotional_price">Preço Promocional (opcional)</Label>
                                        <div className="relative">
                                            <span className="absolute left-3 top-2.5 text-muted-foreground">R$</span>
                                            <Input
                                                id="promotional_price"
                                                type="number"
                                                value={data.promotional_price || ''}
                                                onChange={(e) => setData('promotional_price', e.target.value || null)}
                                                min="0"
                                                step="0.01"
                                                className="pl-8"
                                            />
                                        </div>
                                        {errors.promotional_price && (
                                            <p className="text-sm text-red-500 flex items-center gap-1">
                                                <AlertCircle className="w-4 h-4" />
                                                {errors.promotional_price}
                                            </p>
                                        )}
                                    </div>
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="description">Descrição</Label>
                                    <Textarea
                                        id="description"
                                        value={data.description}
                                        onChange={(e) => setData('description', e.target.value)}
                                        rows={4}
                                        placeholder="Descreva o veículo com detalhes..."
                                    />
                                    {errors.description && (
                                        <p className="text-sm text-red-500 flex items-center gap-1">
                                            <AlertCircle className="w-4 h-4" />
                                            {errors.description}
                                        </p>
                                    )}
                                </div>
                            </CardContent>
                        </Card>

                        {/* Imagens */}
                        <div className="space-y-6">
                            <Card>
                                <CardHeader>
                                    <CardTitle>Imagens do Veículo</CardTitle>
                                    <CardDescription>
                                        Adicione até 10 imagens do veículo. A primeira imagem será a principal.
                                    </CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <FormImageUpload
                                        value={images}
                                        onChange={setImages}
                                        maxFiles={10}
                                        disabled={processing}
                                    />
                                </CardContent>
                            </Card>

                            <Card>
                                <CardHeader>
                                    <CardTitle>Imagem Destacada</CardTitle>
                                    <CardDescription>
                                        Selecione uma imagem para ser destacada na listagem.
                                    </CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <FormImageUpload
                                        value={featuredImage ? [featuredImage] : []}
                                        onChange={(files) => setFeaturedImage(files[0] || null)}
                                        maxFiles={1}
                                        disabled={processing}
                                    />
                                </CardContent>
                            </Card>

                            <Card>
                                <CardHeader>
                                    <CardTitle>Configurações</CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="flex items-center justify-between">
                                        <div className="space-y-0.5">
                                            <Label htmlFor="is_negotiable">Aceita Troca?</Label>
                                            <p className="text-sm text-muted-foreground">
                                                Marque se o veículo aceita troca
                                            </p>
                                        </div>
                                        <Switch
                                            id="is_negotiable"
                                            checked={data.is_negotiable}
                                            onCheckedChange={(checked) => setData('is_negotiable', checked)}
                                        />
                                    </div>

                                    <div className="flex items-center justify-between">
                                        <div className="space-y-0.5">
                                            <Label htmlFor="is_featured">Destaque</Label>
                                            <p className="text-sm text-muted-foreground">
                                                Exibir este veículo em destaque
                                            </p>
                                        </div>
                                        <Switch
                                            id="is_featured"
                                            checked={data.is_featured}
                                            onCheckedChange={(checked) => setData('is_featured', checked)}
                                        />
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="status">Status</Label>
                                        <Select 
                                            value={selectedStatus} 
                                            onValueChange={setSelectedStatus}
                                        >
                                            <SelectTrigger>
                                                <SelectValue placeholder="Selecione o status" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="draft">Rascunho</SelectItem>
                                                <SelectItem value="published">Publicado</SelectItem>
                                                <SelectItem value="sold">Vendido</SelectItem>
                                                <SelectItem value="unavailable">Indisponível</SelectItem>
                                            </SelectContent>
                                        </Select>
                                    </div>
                                </CardContent>
                            </Card>

                            <Card>
                                <CardHeader>
                                    <CardTitle>Características</CardTitle>
                                    <CardDescription>
                                        Selecione as características do veículo
                                    </CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <div className="grid grid-cols-2 gap-2">
                                        {allFeatures.map((feature) => (
                                            <div key={feature.id} className="flex items-center space-x-2">
                                                <input
                                                    type="checkbox"
                                                    id={`feature-${feature.id}`}
                                                    checked={selectedFeatures.includes(feature.id)}
                                                    onChange={() => handleFeatureToggle(feature.id)}
                                                    className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                                                />
                                                <Label htmlFor={`feature-${feature.id}`} className="text-sm">
                                                    {feature.name}
                                                </Label>
                                            </div>
                                        ))}
                                    </div>
                                </CardContent>
                            </Card>
                        </div>
                    </div>

                    <div className="flex justify-end gap-4 pt-6 border-t">
                        <Button type="button" variant="outline" asChild>
                            <Link href={route('admin.vehicles.index')}>
                                Cancelar
                            </Link>
                        </Button>
                        <Button type="submit" disabled={processing}>
                            {processing ? 'Salvando...' : 'Salvar Veículo'}
                            <Save className="w-4 h-4 ml-2" />
                        </Button>
                    </div>
                </form>
            </div>
        </AppLayout>
    );
}
