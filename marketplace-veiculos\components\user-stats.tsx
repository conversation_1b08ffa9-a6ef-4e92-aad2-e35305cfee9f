import { Card, CardContent } from "@/components/ui/card"
import { Eye, Heart, MessageSquare, Car } from "lucide-react"

const stats = [
  {
    title: "Anúncios Ativos",
    value: "3",
    change: "+1 este mês",
    icon: Car,
    color: "text-primary",
  },
  {
    title: "Visualizações",
    value: "1.247",
    change: "+15% vs mês anterior",
    icon: Eye,
    color: "text-accent",
  },
  {
    title: "Favoritos",
    value: "89",
    change: "+23 esta semana",
    icon: Heart,
    color: "text-destructive",
  },
  {
    title: "Contatos",
    value: "34",
    change: "+8 esta semana",
    icon: MessageSquare,
    color: "text-primary",
  },
]

export function UserStats() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {stats.map((stat, index) => {
        const Icon = stat.icon
        return (
          <Card key={index}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">{stat.title}</p>
                  <p className="text-2xl font-bold">{stat.value}</p>
                  <p className="text-xs text-muted-foreground mt-1">{stat.change}</p>
                </div>
                <Icon className={`h-8 w-8 ${stat.color}`} />
              </div>
            </CardContent>
          </Card>
        )
      })}
    </div>
  )
}
