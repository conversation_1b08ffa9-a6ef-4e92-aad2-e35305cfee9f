<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Intervention\Image\ImageManager;
use Intervention\Image\Drivers\Gd\Driver;

class MediaController extends Controller
{
    /**
     * Upload multiple images.
     */
    public function uploadImages(Request $request)
    {
        $request->validate([
            'files' => 'required|array|max:10',
            'files.*' => 'image|mimes:jpeg,png,jpg,webp|max:5120', // 5MB max
            'type' => 'required|string|in:vehicle,part,avatar,general',
        ]);

        $uploadedFiles = [];
        $type = $request->input('type', 'general');

        foreach ($request->file('files') as $file) {
            try {
                $uploadedFile = $this->processImage($file, $type);
                $uploadedFiles[] = $uploadedFile;
            } catch (\Exception $e) {
                return response()->json([
                    'error' => 'Erro ao processar imagem: ' . $e->getMessage()
                ], 422);
            }
        }

        return response()->json([
            'message' => 'Imagens enviadas com sucesso!',
            'files' => $uploadedFiles,
        ]);
    }

    /**
     * Upload single image.
     */
    public function uploadImage(Request $request)
    {
        $request->validate([
            'file' => 'required|image|mimes:jpeg,png,jpg,webp|max:5120',
            'type' => 'required|string|in:vehicle,part,avatar,general',
        ]);

        try {
            $uploadedFile = $this->processImage($request->file('file'), $request->input('type'));

            return response()->json([
                'message' => 'Imagem enviada com sucesso!',
                'file' => $uploadedFile,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Erro ao processar imagem: ' . $e->getMessage()
            ], 422);
        }
    }

    /**
     * Process and store image with different sizes.
     */
    private function processImage($file, $type = 'general')
    {
        $filename = Str::uuid() . '.' . $file->getClientOriginalExtension();
        $path = "uploads/{$type}/" . date('Y/m');

        // Configurações por tipo
        $configs = [
            'vehicle' => [
                'original' => ['width' => 1920, 'height' => 1080, 'quality' => 90],
                'large' => ['width' => 1200, 'height' => 800, 'quality' => 85],
                'medium' => ['width' => 800, 'height' => 600, 'quality' => 80],
                'thumb' => ['width' => 300, 'height' => 200, 'quality' => 75],
            ],
            'part' => [
                'original' => ['width' => 1200, 'height' => 1200, 'quality' => 90],
                'large' => ['width' => 800, 'height' => 800, 'quality' => 85],
                'medium' => ['width' => 400, 'height' => 400, 'quality' => 80],
                'thumb' => ['width' => 150, 'height' => 150, 'quality' => 75],
            ],
            'avatar' => [
                'original' => ['width' => 400, 'height' => 400, 'quality' => 90],
                'medium' => ['width' => 200, 'height' => 200, 'quality' => 85],
                'thumb' => ['width' => 100, 'height' => 100, 'quality' => 80],
            ],
            'general' => [
                'original' => ['width' => 1920, 'height' => 1080, 'quality' => 90],
                'medium' => ['width' => 800, 'height' => 600, 'quality' => 80],
                'thumb' => ['width' => 300, 'height' => 200, 'quality' => 75],
            ],
        ];

        $config = $configs[$type] ?? $configs['general'];
        $sizes = [];

        // Criar diretório se não existir
        Storage::disk('public')->makeDirectory($path);

        // Criar manager de imagem
        $manager = new ImageManager(new Driver());

        foreach ($config as $sizeName => $sizeConfig) {
            $sizeFilename = $sizeName === 'original' ? $filename :
                pathinfo($filename, PATHINFO_FILENAME) . "_{$sizeName}." . pathinfo($filename, PATHINFO_EXTENSION);

            $fullPath = $path . '/' . $sizeFilename;

            // Processar imagem
            $image = $manager->read($file);

            // Redimensionar mantendo proporção
            $image->scaleDown($sizeConfig['width'], $sizeConfig['height']);

            // Salvar
            $encodedImage = $image->encode();
            Storage::disk('public')->put($fullPath, $encodedImage);

            $sizes[$sizeName] = [
                'url' => asset('storage/' . $fullPath),
                'path' => $fullPath,
                'width' => $image->width(),
                'height' => $image->height(),
                'size' => Storage::disk('public')->size($fullPath),
            ];
        }

        return [
            'id' => Str::uuid(),
            'name' => $file->getClientOriginalName(),
            'filename' => $filename,
            'type' => $file->getClientMimeType(),
            'sizes' => $sizes,
            'original_size' => $file->getSize(),
            'created_at' => now()->toISOString(),
        ];
    }

    /**
     * Delete image and all its sizes.
     */
    public function deleteImage(Request $request)
    {
        $request->validate([
            'path' => 'required|string',
        ]);

        $path = $request->input('path');
        
        try {
            // Remover arquivo original
            if (Storage::disk('public')->exists($path)) {
                Storage::disk('public')->delete($path);
            }

            // Remover variações de tamanho
            $pathInfo = pathinfo($path);
            $directory = $pathInfo['dirname'];
            $filename = $pathInfo['filename'];
            $extension = $pathInfo['extension'];

            $sizes = ['large', 'medium', 'thumb'];
            foreach ($sizes as $size) {
                $sizePath = $directory . '/' . $filename . "_{$size}." . $extension;
                if (Storage::disk('public')->exists($sizePath)) {
                    Storage::disk('public')->delete($sizePath);
                }
            }

            return response()->json([
                'message' => 'Imagem removida com sucesso!',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Erro ao remover imagem: ' . $e->getMessage()
            ], 422);
        }
    }

    /**
     * Get image info.
     */
    public function getImageInfo(Request $request)
    {
        $request->validate([
            'path' => 'required|string',
        ]);

        $path = $request->input('path');

        if (!Storage::disk('public')->exists($path)) {
            return response()->json([
                'error' => 'Imagem não encontrada.'
            ], 404);
        }

        $size = Storage::disk('public')->size($path);
        $url = asset('storage/' . $path);

        return response()->json([
            'path' => $path,
            'url' => $url,
            'size' => $size,
            'exists' => true,
        ]);
    }

    /**
     * Optimize existing image.
     */
    public function optimizeImage(Request $request)
    {
        $request->validate([
            'path' => 'required|string',
            'quality' => 'integer|min:10|max:100',
            'width' => 'integer|min:50',
            'height' => 'integer|min:50',
        ]);

        $path = $request->input('path');
        $width = $request->input('width');
        $height = $request->input('height');

        if (!Storage::disk('public')->exists($path)) {
            return response()->json([
                'error' => 'Imagem não encontrada.'
            ], 404);
        }

        try {
            $fullPath = Storage::disk('public')->path($path);
            $manager = new ImageManager(new Driver());
            $image = $manager->read($fullPath);

            if ($width && $height) {
                $image->scaleDown($width, $height);
            }

            $encodedImage = $image->encode();
            Storage::disk('public')->put($path, $encodedImage);

            return response()->json([
                'message' => 'Imagem otimizada com sucesso!',
                'url' => asset('storage/' . $path),
                'size' => Storage::disk('public')->size($path),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Erro ao otimizar imagem: ' . $e->getMessage()
            ], 422);
        }
    }
}
