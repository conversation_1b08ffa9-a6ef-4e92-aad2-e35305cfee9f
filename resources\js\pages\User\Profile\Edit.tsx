import { But<PERSON> } from '@/components/ui/button';
import {
    Card,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle,
} from '@/components/ui/card';
import { ImageUpload } from '@/components/ui/image-upload';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { AvatarImage } from '@/components/ui/responsive-image';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Textarea } from '@/components/ui/textarea';
import { Head, useForm } from '@inertiajs/react';
import { Eye, EyeOff, Save, Upload } from 'lucide-react';
import { useState } from 'react';

interface User {
    id: number;
    name: string;
    email: string;
    phone?: string;
    cpf_cnpj?: string;
    type: 'individual' | 'company';
    birth_date?: string;
    company_name?: string;
    trading_name?: string;
    state_registration?: string;
    corporate_document?: string;
    website?: string;
    bio?: string;
    avatar?: string;
    notification_preferences?: {
        email_notifications: boolean;
        sms_notifications: boolean;
        marketing_emails: boolean;
        new_message_email: boolean;
        new_offer_email: boolean;
    };
}

interface Props {
    user: User;
}

export default function ProfileEdit({ user }: Props) {
    const [showPassword, setShowPassword] = useState(false);
    const [avatarFiles, setAvatarFiles] = useState<any[]>([]);

    const { data, setData, put, processing, errors } = useForm({
        name: user.name || '',
        email: user.email || '',
        phone: user.phone || '',
        cpf_cnpj: user.cpf_cnpj || '',
        type: user.type || 'individual',
        birth_date: user.birth_date || '',
        company_name: user.company_name || '',
        trading_name: user.trading_name || '',
        state_registration: user.state_registration || '',
        corporate_document: user.corporate_document || '',
        website: user.website || '',
        bio: user.bio || '',
    });

    const {
        data: passwordData,
        setData: setPasswordData,
        put: updatePassword,
        processing: passwordProcessing,
        errors: passwordErrors,
    } = useForm({
        current_password: '',
        password: '',
        password_confirmation: '',
    });

    const {
        data: notificationData,
        setData: setNotificationData,
        put: updateNotifications,
        processing: notificationProcessing,
    } = useForm({
        email_notifications:
            user.notification_preferences?.email_notifications ?? true,
        sms_notifications:
            user.notification_preferences?.sms_notifications ?? false,
        marketing_emails:
            user.notification_preferences?.marketing_emails ?? false,
        new_message_email:
            user.notification_preferences?.new_message_email ?? true,
        new_offer_email: user.notification_preferences?.new_offer_email ?? true,
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        put('/minha-conta/perfil');
    };

    const handlePasswordSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        updatePassword('/minha-conta/perfil/senha', {
            onSuccess: () => {
                setPasswordData({
                    current_password: '',
                    password: '',
                    password_confirmation: '',
                });
            },
        });
    };

    const handleNotificationSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        updateNotifications('/minha-conta/perfil/notificacoes');
    };

    const handleAvatarUpload = async () => {
        if (avatarFiles.length === 0) return;

        const formData = new FormData();
        formData.append('avatar', avatarFiles[0].file);

        try {
            await fetch('/minha-conta/perfil/avatar', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRF-TOKEN':
                        document
                            .querySelector('meta[name="csrf-token"]')
                            ?.getAttribute('content') || '',
                },
            });

            // Reload page to show new avatar
            window.location.reload();
        } catch (error) {
            console.error('Erro ao fazer upload do avatar:', error);
        }
    };

    return (
        <>
            <Head title="Editar Perfil" />

            <div className="space-y-6">
                {/* Header */}
                <div>
                    <h1 className="text-3xl font-bold tracking-tight">
                        Editar Perfil
                    </h1>
                    <p className="text-muted-foreground">
                        Atualize suas informações pessoais e configurações
                    </p>
                </div>

                <Tabs defaultValue="profile" className="space-y-6">
                    <TabsList>
                        <TabsTrigger value="profile">Perfil</TabsTrigger>
                        <TabsTrigger value="password">Senha</TabsTrigger>
                        <TabsTrigger value="notifications">
                            Notificações
                        </TabsTrigger>
                        <TabsTrigger value="avatar">Avatar</TabsTrigger>
                    </TabsList>

                    {/* Profile Tab */}
                    <TabsContent value="profile">
                        <Card>
                            <CardHeader>
                                <CardTitle>Informações Pessoais</CardTitle>
                                <CardDescription>
                                    Atualize suas informações básicas e de
                                    contato
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                <form
                                    onSubmit={handleSubmit}
                                    className="space-y-6"
                                >
                                    {/* Basic Information */}
                                    <div className="grid gap-4 md:grid-cols-2">
                                        <div className="space-y-2">
                                            <Label htmlFor="name">
                                                Nome Completo *
                                            </Label>
                                            <Input
                                                id="name"
                                                value={data.name}
                                                onChange={(e) =>
                                                    setData(
                                                        'name',
                                                        e.target.value,
                                                    )
                                                }
                                                required
                                            />
                                            {errors.name && (
                                                <p className="text-sm text-red-600">
                                                    {errors.name}
                                                </p>
                                            )}
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="email">
                                                Email *
                                            </Label>
                                            <Input
                                                id="email"
                                                type="email"
                                                value={data.email}
                                                onChange={(e) =>
                                                    setData(
                                                        'email',
                                                        e.target.value,
                                                    )
                                                }
                                                required
                                            />
                                            {errors.email && (
                                                <p className="text-sm text-red-600">
                                                    {errors.email}
                                                </p>
                                            )}
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="phone">
                                                Telefone
                                            </Label>
                                            <Input
                                                id="phone"
                                                value={data.phone}
                                                onChange={(e) =>
                                                    setData(
                                                        'phone',
                                                        e.target.value,
                                                    )
                                                }
                                                placeholder="(11) 99999-9999"
                                            />
                                            {errors.phone && (
                                                <p className="text-sm text-red-600">
                                                    {errors.phone}
                                                </p>
                                            )}
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="type">
                                                Tipo de Conta *
                                            </Label>
                                            <Select
                                                value={data.type}
                                                onValueChange={(value) =>
                                                    setData(
                                                        'type',
                                                        value as
                                                            | 'individual'
                                                            | 'company',
                                                    )
                                                }
                                            >
                                                <SelectTrigger>
                                                    <SelectValue />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem value="individual">
                                                        Pessoa Física
                                                    </SelectItem>
                                                    <SelectItem value="company">
                                                        Empresa
                                                    </SelectItem>
                                                </SelectContent>
                                            </Select>
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="cpf_cnpj">
                                                {data.type === 'company'
                                                    ? 'CNPJ'
                                                    : 'CPF'}
                                            </Label>
                                            <Input
                                                id="cpf_cnpj"
                                                value={data.cpf_cnpj}
                                                onChange={(e) =>
                                                    setData(
                                                        'cpf_cnpj',
                                                        e.target.value,
                                                    )
                                                }
                                                placeholder={
                                                    data.type === 'company'
                                                        ? '00.000.000/0000-00'
                                                        : '000.000.000-00'
                                                }
                                            />
                                            {errors.cpf_cnpj && (
                                                <p className="text-sm text-red-600">
                                                    {errors.cpf_cnpj}
                                                </p>
                                            )}
                                        </div>

                                        {data.type === 'individual' && (
                                            <div className="space-y-2">
                                                <Label htmlFor="birth_date">
                                                    Data de Nascimento
                                                </Label>
                                                <Input
                                                    id="birth_date"
                                                    type="date"
                                                    value={data.birth_date}
                                                    onChange={(e) =>
                                                        setData(
                                                            'birth_date',
                                                            e.target.value,
                                                        )
                                                    }
                                                />
                                                {errors.birth_date && (
                                                    <p className="text-sm text-red-600">
                                                        {errors.birth_date}
                                                    </p>
                                                )}
                                            </div>
                                        )}
                                    </div>

                                    {/* Company Information */}
                                    {data.type === 'company' && (
                                        <>
                                            <Separator />
                                            <div className="space-y-4">
                                                <h3 className="text-lg font-medium">
                                                    Informações da Empresa
                                                </h3>
                                                <div className="grid gap-4 md:grid-cols-2">
                                                    <div className="space-y-2">
                                                        <Label htmlFor="company_name">
                                                            Razão Social
                                                        </Label>
                                                        <Input
                                                            id="company_name"
                                                            value={
                                                                data.company_name
                                                            }
                                                            onChange={(e) =>
                                                                setData(
                                                                    'company_name',
                                                                    e.target
                                                                        .value,
                                                                )
                                                            }
                                                        />
                                                        {errors.company_name && (
                                                            <p className="text-sm text-red-600">
                                                                {
                                                                    errors.company_name
                                                                }
                                                            </p>
                                                        )}
                                                    </div>

                                                    <div className="space-y-2">
                                                        <Label htmlFor="trading_name">
                                                            Nome Fantasia
                                                        </Label>
                                                        <Input
                                                            id="trading_name"
                                                            value={
                                                                data.trading_name
                                                            }
                                                            onChange={(e) =>
                                                                setData(
                                                                    'trading_name',
                                                                    e.target
                                                                        .value,
                                                                )
                                                            }
                                                        />
                                                        {errors.trading_name && (
                                                            <p className="text-sm text-red-600">
                                                                {
                                                                    errors.trading_name
                                                                }
                                                            </p>
                                                        )}
                                                    </div>

                                                    <div className="space-y-2 md:col-span-2">
                                                        <Label htmlFor="website">
                                                            Website
                                                        </Label>
                                                        <Input
                                                            id="website"
                                                            type="url"
                                                            value={data.website}
                                                            onChange={(e) =>
                                                                setData(
                                                                    'website',
                                                                    e.target
                                                                        .value,
                                                                )
                                                            }
                                                            placeholder="https://exemplo.com"
                                                        />
                                                        {errors.website && (
                                                            <p className="text-sm text-red-600">
                                                                {errors.website}
                                                            </p>
                                                        )}
                                                    </div>
                                                </div>
                                            </div>
                                        </>
                                    )}

                                    {/* Bio */}
                                    <div className="space-y-2">
                                        <Label htmlFor="bio">Biografia</Label>
                                        <Textarea
                                            id="bio"
                                            value={data.bio}
                                            onChange={(e) =>
                                                setData('bio', e.target.value)
                                            }
                                            placeholder="Conte um pouco sobre você..."
                                            rows={4}
                                        />
                                        {errors.bio && (
                                            <p className="text-sm text-red-600">
                                                {errors.bio}
                                            </p>
                                        )}
                                    </div>

                                    <Button type="submit" disabled={processing}>
                                        <Save className="mr-2 h-4 w-4" />
                                        {processing
                                            ? 'Salvando...'
                                            : 'Salvar Alterações'}
                                    </Button>
                                </form>
                            </CardContent>
                        </Card>
                    </TabsContent>

                    {/* Password Tab */}
                    <TabsContent value="password">
                        <Card>
                            <CardHeader>
                                <CardTitle>Alterar Senha</CardTitle>
                                <CardDescription>
                                    Atualize sua senha para manter sua conta
                                    segura
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                <form
                                    onSubmit={handlePasswordSubmit}
                                    className="space-y-4"
                                >
                                    <div className="space-y-2">
                                        <Label htmlFor="current_password">
                                            Senha Atual *
                                        </Label>
                                        <div className="relative">
                                            <Input
                                                id="current_password"
                                                type={
                                                    showPassword
                                                        ? 'text'
                                                        : 'password'
                                                }
                                                value={
                                                    passwordData.current_password
                                                }
                                                onChange={(e) =>
                                                    setPasswordData(
                                                        'current_password',
                                                        e.target.value,
                                                    )
                                                }
                                                required
                                            />
                                            <Button
                                                type="button"
                                                variant="ghost"
                                                size="sm"
                                                className="absolute top-0 right-0 h-full px-3"
                                                onClick={() =>
                                                    setShowPassword(
                                                        !showPassword,
                                                    )
                                                }
                                            >
                                                {showPassword ? (
                                                    <EyeOff className="h-4 w-4" />
                                                ) : (
                                                    <Eye className="h-4 w-4" />
                                                )}
                                            </Button>
                                        </div>
                                        {passwordErrors.current_password && (
                                            <p className="text-sm text-red-600">
                                                {
                                                    passwordErrors.current_password
                                                }
                                            </p>
                                        )}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="password">
                                            Nova Senha *
                                        </Label>
                                        <Input
                                            id="password"
                                            type="password"
                                            value={passwordData.password}
                                            onChange={(e) =>
                                                setPasswordData(
                                                    'password',
                                                    e.target.value,
                                                )
                                            }
                                            required
                                        />
                                        {passwordErrors.password && (
                                            <p className="text-sm text-red-600">
                                                {passwordErrors.password}
                                            </p>
                                        )}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="password_confirmation">
                                            Confirmar Nova Senha *
                                        </Label>
                                        <Input
                                            id="password_confirmation"
                                            type="password"
                                            value={
                                                passwordData.password_confirmation
                                            }
                                            onChange={(e) =>
                                                setPasswordData(
                                                    'password_confirmation',
                                                    e.target.value,
                                                )
                                            }
                                            required
                                        />
                                        {passwordErrors.password_confirmation && (
                                            <p className="text-sm text-red-600">
                                                {
                                                    passwordErrors.password_confirmation
                                                }
                                            </p>
                                        )}
                                    </div>

                                    <Button
                                        type="submit"
                                        disabled={passwordProcessing}
                                    >
                                        <Save className="mr-2 h-4 w-4" />
                                        {passwordProcessing
                                            ? 'Atualizando...'
                                            : 'Atualizar Senha'}
                                    </Button>
                                </form>
                            </CardContent>
                        </Card>
                    </TabsContent>

                    {/* Avatar Tab */}
                    <TabsContent value="avatar">
                        <Card>
                            <CardHeader>
                                <CardTitle>Avatar</CardTitle>
                                <CardDescription>
                                    Atualize sua foto de perfil
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-6">
                                {/* Current Avatar */}
                                <div className="flex items-center gap-4">
                                    <AvatarImage
                                        src={user.avatar}
                                        alt={user.name}
                                        size="large"
                                    />
                                    <div>
                                        <p className="text-sm font-medium">
                                            Avatar Atual
                                        </p>
                                        <p className="text-xs text-muted-foreground">
                                            Recomendamos uma imagem quadrada de
                                            pelo menos 200x200 pixels
                                        </p>
                                    </div>
                                </div>

                                {/* Upload New Avatar */}
                                <div className="space-y-4">
                                    <Label>Nova Foto</Label>
                                    <ImageUpload
                                        value={avatarFiles}
                                        onChange={setAvatarFiles}
                                        maxFiles={1}
                                        maxSize={2}
                                        accept={[
                                            'image/jpeg',
                                            'image/png',
                                            'image/jpg',
                                        ]}
                                        showPreview={true}
                                        allowReorder={false}
                                    />
                                    {avatarFiles.length > 0 && (
                                        <Button onClick={handleAvatarUpload}>
                                            <Upload className="mr-2 h-4 w-4" />
                                            Atualizar Avatar
                                        </Button>
                                    )}
                                </div>
                            </CardContent>
                        </Card>
                    </TabsContent>
                </Tabs>
            </div>
        </>
    );
}
