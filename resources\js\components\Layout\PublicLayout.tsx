import { ReactNode } from 'react';
import HeaderOLX from '@/components/HeaderOLX';
import Footer from '@/components/Footer';
import { Category } from '@/types';

interface PublicLayoutProps {
    children: ReactNode;
    categories?: Category[];
}

export default function PublicLayout({ 
    children, 
    categories = [] 
}: PublicLayoutProps) {
    return (
        <div className="min-h-screen flex flex-col bg-background">
            <HeaderOLX categories={categories} />
            <main className="flex-1">
                {children}
            </main>
            <Footer />
        </div>
    );
}
