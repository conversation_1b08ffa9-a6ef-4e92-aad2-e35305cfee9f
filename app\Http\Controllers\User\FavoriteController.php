<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Models\Advertisement;
use App\Models\Favorite;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class FavoriteController extends Controller
{

    /**
     * Display a listing of user's favorites
     */
    public function index(Request $request): Response
    {
        $query = Favorite::with([
            'advertisement.vehicle.brand',
            'advertisement.user'
        ])
        ->where('user_id', auth()->id())
        ->whereHas('advertisement', function($q) {
            $q->where('status', 'published');
        });

        // Search filter
        if ($request->filled('search')) {
            $search = $request->input('search');
            $query->whereHas('advertisement', function($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhereHas('vehicle', function($vq) use ($search) {
                      $vq->where('model', 'like', "%{$search}%")
                         ->orWhereHas('brand', function($bq) use ($search) {
                             $bq->where('name', 'like', "%{$search}%");
                         });
                  });
            });
        }

        // Price filter
        if ($request->filled('min_price')) {
            $query->whereHas('advertisement', function($q) use ($request) {
                $q->where('price', '>=', $request->input('min_price'));
            });
        }

        if ($request->filled('max_price')) {
            $query->whereHas('advertisement', function($q) use ($request) {
                $q->where('price', '<=', $request->input('max_price'));
            });
        }

        // Brand filter
        if ($request->filled('brand_id')) {
            $query->whereHas('advertisement.vehicle', function($q) use ($request) {
                $q->where('brand_id', $request->input('brand_id'));
            });
        }

        // Sort
        $sortBy = $request->input('sort', 'created_at');
        $sortOrder = $request->input('order', 'desc');

        switch ($sortBy) {
            case 'price':
                $query->join('advertisements', 'favorites.advertisement_id', '=', 'advertisements.id')
                      ->orderBy('advertisements.price', $sortOrder)
                      ->select('favorites.*');
                break;
            case 'title':
                $query->join('advertisements', 'favorites.advertisement_id', '=', 'advertisements.id')
                      ->orderBy('advertisements.title', $sortOrder)
                      ->select('favorites.*');
                break;
            default:
                $query->orderBy('favorites.created_at', $sortOrder);
        }

        $favorites = $query->paginate(12)->withQueryString();

        // Get brands for filter
        $brands = \App\Models\Brand::orderBy('name')->get();

        return Inertia::render('User/Favorites/IndexSimple', [
            'favorites' => $favorites,
            'brands' => $brands,
            'filters' => $request->only(['search', 'min_price', 'max_price', 'brand_id', 'sort', 'order']),
        ]);
    }

    /**
     * Add advertisement to favorites
     */
    public function store(Request $request, Advertisement $advertisement)
    {
        // Check if advertisement is published
        if ($advertisement->status !== 'published') {
            return response()->json(['error' => 'Anúncio não está disponível.'], 400);
        }

        // Check if user is not the owner
        if ($advertisement->user_id === auth()->id()) {
            return response()->json(['error' => 'Você não pode favoritar seu próprio anúncio.'], 400);
        }

        // Check if already favorited
        $existingFavorite = Favorite::where('user_id', auth()->id())
            ->where('advertisement_id', $advertisement->id)
            ->first();

        if ($existingFavorite) {
            return response()->json(['error' => 'Anúncio já está nos favoritos.'], 400);
        }

        Favorite::create([
            'user_id' => auth()->id(),
            'advertisement_id' => $advertisement->id,
        ]);

        return response()->json(['message' => 'Anúncio adicionado aos favoritos!']);
    }

    /**
     * Remove advertisement from favorites
     */
    public function destroy(Advertisement $advertisement)
    {
        $favorite = Favorite::where('user_id', auth()->id())
            ->where('advertisement_id', $advertisement->id)
            ->first();

        if (!$favorite) {
            return response()->json(['error' => 'Anúncio não está nos favoritos.'], 404);
        }

        $favorite->delete();

        return response()->json(['message' => 'Anúncio removido dos favoritos!']);
    }

    /**
     * Remove multiple favorites
     */
    public function destroyMultiple(Request $request)
    {
        $request->validate([
            'advertisement_ids' => 'required|array',
            'advertisement_ids.*' => 'integer|exists:advertisements,id',
        ]);

        $deleted = Favorite::where('user_id', auth()->id())
            ->whereIn('advertisement_id', $request->input('advertisement_ids'))
            ->delete();

        return response()->json([
            'message' => "Removidos {$deleted} anúncios dos favoritos!",
            'deleted_count' => $deleted,
        ]);
    }

    /**
     * Check if advertisement is favorited by user
     */
    public function check(Advertisement $advertisement)
    {
        $isFavorited = Favorite::where('user_id', auth()->id())
            ->where('advertisement_id', $advertisement->id)
            ->exists();

        return response()->json(['is_favorited' => $isFavorited]);
    }

    /**
     * Get user's favorites count
     */
    public function count()
    {
        $count = Favorite::where('user_id', auth()->id())
            ->whereHas('advertisement', function($q) {
                $q->where('status', 'published');
            })
            ->count();

        return response()->json(['count' => $count]);
    }
}
