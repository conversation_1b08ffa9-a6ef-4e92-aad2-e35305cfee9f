<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('vehicles', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('category_id')->constrained()->onDelete('cascade');
            $table->foreignId('brand_id')->constrained()->onDelete('cascade');
            $table->string('model');
            $table->string('slug')->unique();
            $table->integer('year_manufacture');
            $table->integer('model_year');
            $table->string('license_plate', 10)->unique()->nullable();
            $table->string('chassis_number', 17)->unique()->nullable();
            $table->string('color', 50)->nullable();
            $table->string('fuel_type', 20);
            $table->string('transmission', 20);
            $table->integer('mileage');
            $table->text('description')->nullable();
            $table->decimal('price', 15, 2);
            $table->decimal('promotional_price', 15, 2)->nullable();
            $table->boolean('is_negotiable')->default(false);
            $table->boolean('is_featured')->default(false);
            $table->enum('status', ['draft', 'pending', 'published', 'sold', 'reserved', 'inactive'])->default('draft');
            $table->integer('views')->default(0);
            $table->string('seo_title')->nullable();
            $table->text('seo_description')->nullable();
            $table->string('seo_keywords')->nullable();
            $table->timestamps();
            $table->softDeletes();

            // Índices
            $table->index('slug');
            $table->index('status');
            $table->index('is_featured');
            $table->index('price');
            $table->index('year_manufacture');
            $table->index('model_year');
            $table->index('fuel_type');
            $table->index('transmission');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('vehicles');
    }
};
