<?php

namespace App\Policies;

use App\Models\User;
use App\Models\Vehicle;
use Illuminate\Auth\Access\HandlesAuthorization;

class VehiclePolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any vehicles.
     */
    public function viewAny(User $user): bool
    {
        return true;
    }

    /**
     * Determine whether the user can view the vehicle.
     */
    public function view(User $user, Vehicle $vehicle): bool
    {
        return $vehicle->status === 'published' || 
               $user->id === $vehicle->user_id || 
               $user->hasPermissionTo('vehicles.view');
    }

    /**
     * Determine whether the user can create vehicles.
     */
    public function create(User $user): bool
    {
        return $user->hasPermissionTo('vehicles.create') && 
               $user->hasVerifiedEmail() && 
               $user->status === 'active';
    }

    /**
     * Determine whether the user can update the vehicle.
     */
    public function update(User $user, Vehicle $vehicle): bool
    {
        return ($user->id === $vehicle->user_id && $user->hasPermissionTo('vehicles.update.own')) ||
               $user->hasPermissionTo('vehicles.update.any');
    }

    /**
     * Determine whether the user can delete the vehicle.
     */
    public function delete(User $user, Vehicle $vehicle): bool
    {
        return ($user->id === $vehicle->user_id && $user->hasPermissionTo('vehicles.delete.own')) ||
               $user->hasPermissionTo('vehicles.delete.any');
    }

    /**
     * Determine whether the user can restore the vehicle.
     */
    public function restore(User $user, Vehicle $vehicle): bool
    {
        return ($user->id === $vehicle->user_id && $user->hasPermissionTo('vehicles.restore.own')) ||
               $user->hasPermissionTo('vehicles.restore.any');
    }

    /**
     * Determine whether the user can permanently delete the vehicle.
     */
    public function forceDelete(User $user, Vehicle $vehicle): bool
    {
        return $user->hasPermissionTo('vehicles.force.delete');
    }

    /**
     * Determine whether the user can publish vehicles.
     */
    public function publish(User $user, Vehicle $vehicle): bool
    {
        return ($user->id === $vehicle->user_id && $user->hasPermissionTo('vehicles.publish.own')) ||
               $user->hasPermissionTo('vehicles.publish.any');
    }

    /**
     * Determine whether the user can feature vehicles.
     */
    public function feature(User $user): bool
    {
        return $user->hasPermissionTo('vehicles.feature');
    }

    /**
     * Determine whether the user can bulk edit vehicles.
     */
    public function bulkEdit(User $user): bool
    {
        return $user->hasPermissionTo('vehicles.bulk.edit');
    }

    /**
     * Determine whether the user can bulk delete vehicles.
     */
    public function bulkDelete(User $user): bool
    {
        return $user->hasPermissionTo('vehicles.bulk.delete');
    }
}
