import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Separator } from "@/components/ui/separator"
import { Phone, MessageCircle, Mail, MapPin, Star, Shield, Calendar } from "lucide-react"

interface SellerContactProps {
  seller: {
    name: string
    phone: string
    whatsapp: string
    email: string
    location: string
    memberSince: string
    rating: number
    totalSales: number
    verified: boolean
  }
  vehicle: {
    title: string
    price: string
  }
}

export function SellerContact({ seller, vehicle }: SellerContactProps) {
  return (
    <div className="space-y-6">
      {/* Price Card */}
      <Card>
        <CardContent className="p-6">
          <div className="text-center">
            <div className="text-3xl font-bold text-primary mb-2">{vehicle.price}</div>
            <p className="text-muted-foreground text-sm mb-4">Preço à vista</p>
            <Button className="w-full mb-3" size="lg">
              <MessageCircle className="h-4 w-4 mr-2" />
              Conversar no WhatsApp
            </Button>
            <Button variant="outline" className="w-full bg-transparent">
              <Phone className="h-4 w-4 mr-2" />
              Ver telefone
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Seller Info */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            Vendedor
            {seller.verified && (
              <Badge variant="secondary" className="flex items-center gap-1">
                <Shield className="h-3 w-3" />
                Verificado
              </Badge>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center space-x-3">
            <Avatar>
              <AvatarImage src="/placeholder.svg" />
              <AvatarFallback>
                {seller.name
                  .split(" ")
                  .map((n) => n[0])
                  .join("")}
              </AvatarFallback>
            </Avatar>
            <div>
              <div className="font-semibold">{seller.name}</div>
              <div className="flex items-center text-sm text-muted-foreground">
                {/* using token semântico em vez de yellow-400 */}
                <Star className="h-4 w-4 mr-1 fill-accent text-accent" />
                {seller.rating} • {seller.totalSales} vendas
              </div>
            </div>
          </div>

          <Separator />

          <div className="space-y-3 text-sm">
            <div className="flex items-center text-muted-foreground">
              <MapPin className="h-4 w-4 mr-2" />
              {seller.location}
            </div>
            <div className="flex items-center text-muted-foreground">
              <Calendar className="h-4 w-4 mr-2" />
              Membro desde {seller.memberSince}
            </div>
          </div>

          <Separator />

          <div className="space-y-2">
            <Button variant="outline" className="w-full justify-start bg-transparent">
              <Mail className="h-4 w-4 mr-2" />
              Enviar email
            </Button>
            <Button variant="outline" className="w-full justify-start bg-transparent">
              <MessageCircle className="h-4 w-4 mr-2" />
              Ver outros anúncios
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Safety Tips */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Dicas de Segurança
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ul className="text-sm space-y-2 text-muted-foreground">
            <li>• Prefira encontros em locais públicos</li>
            <li>• Verifique a documentação do veículo</li>
            <li>• Faça uma vistoria completa</li>
            <li>• Desconfie de preços muito baixos</li>
            <li>• Nunca faça pagamentos antecipados</li>
          </ul>
        </CardContent>
      </Card>
    </div>
  )
}
