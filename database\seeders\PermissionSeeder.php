<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;
use App\Models\User;

class PermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Reset cached roles and permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Create permissions for vehicles
        $permissions = [
            // Vehicle permissions
            'vehicles.view',
            'vehicles.create',
            'vehicles.update.own',
            'vehicles.update.any',
            'vehicles.delete.own',
            'vehicles.delete.any',
            'vehicles.restore.own',
            'vehicles.restore.any',
            'vehicles.force.delete',
            'vehicles.publish.own',
            'vehicles.publish.any',
            'vehicles.feature',
            'vehicles.bulk.edit',
            'vehicles.bulk.delete',

            // Admin permissions
            'admin.access',
            'admin.users.view',
            'admin.users.create',
            'admin.users.update',
            'admin.users.delete',
            'admin.settings.manage',
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['name' => $permission, 'guard_name' => 'web']);
        }

        // Create roles
        $adminRole = Role::firstOrCreate(['name' => 'admin', 'guard_name' => 'web']);
        $managerRole = Role::firstOrCreate(['name' => 'manager', 'guard_name' => 'web']);
        $userRole = Role::firstOrCreate(['name' => 'user', 'guard_name' => 'web']);

        // Assign permissions to admin role
        $adminRole->givePermissionTo($permissions);

        // Assign permissions to manager role
        $managerRole->givePermissionTo([
            'vehicles.view',
            'vehicles.create',
            'vehicles.update.own',
            'vehicles.delete.own',
            'vehicles.publish.own',
        ]);

        // Assign permissions to user role
        $userRole->givePermissionTo([
            'vehicles.view',
            'vehicles.create',
            'vehicles.update.own',
            'vehicles.delete.own',
            'vehicles.publish.own',
        ]);

        // Create admin user and assign admin role
        $adminUser = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Admin User',
                'password' => bcrypt('password123'),
                'status' => 'active',
                'email_verified_at' => now(),
            ]
        );
        $adminUser->assignRole($adminRole);

        // Create manager user and assign manager role
        $managerUser = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Manager User',
                'password' => bcrypt('password123'),
                'status' => 'active',
                'email_verified_at' => now(),
            ]
        );
        $managerUser->assignRole($managerRole);

        $this->command->info('Permissions and roles seeded successfully!');
    }
}
