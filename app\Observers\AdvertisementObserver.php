<?php

namespace App\Observers;

use App\Models\Advertisement;
use App\Notifications\AdvertisementApprovedNotification;
use App\Notifications\AdvertisementRejectedNotification;
use App\Notifications\AdvertisementPublishedNotification;
use App\Notifications\AdvertisementExpiredNotification;
use Carbon\Carbon;

class AdvertisementObserver
{
    /**
     * Handle the Advertisement "created" event.
     */
    public function created(Advertisement $advertisement): void
    {
        // When an ad is created, set the default status to draft
        if (!$advertisement->status) {
            $advertisement->status = Advertisement::STATUS_DRAFT;
            $advertisement->saveQuietly();
        }
        
        // If the ad is created by an admin and is already approved, set the publication date
        if ($advertisement->status === Advertisement::STATUS_APPROVED && !$advertisement->published_at) {
            $advertisement->published_at = now();
            $advertisement->expires_at = now()->addDays(30); // 30 days of publication
            $advertisement->saveQuietly();
        }
    }

    /**
     * Handle the Advertisement "updated" event.
     */
    public function updated(Advertisement $advertisement): void
    {
        $originalStatus = $advertisement->getOriginal('status');
        $currentStatus = $advertisement->status;
        
        // If status changed to approved
        if ($originalStatus !== $currentStatus && $currentStatus === Advertisement::STATUS_APPROVED) {
            // Notify the user that the ad was approved
            if ($advertisement->user) {
                $advertisement->user->notify(new AdvertisementApprovedNotification($advertisement));
            }
            
            // If there's no publication date, set it to now
            if (!$advertisement->published_at) {
                $advertisement->published_at = now();
                $advertisement->expires_at = now()->addDays(30); // 30 days of publication
                $advertisement->saveQuietly();
            }
        }
        
        // If status changed to rejected
        if ($originalStatus !== $currentStatus && $currentStatus === Advertisement::STATUS_REJECTED) {
            // Notify the user that the ad was rejected
            if ($advertisement->user) {
                $advertisement->user->notify(new AdvertisementRejectedNotification($advertisement));
            }
        }
        
        // If status changed to published
        if ($originalStatus !== $currentStatus && $currentStatus === Advertisement::STATUS_PUBLISHED) {
            // Set publication/expiration dates if not already set
            $updates = [];
            
            if (!$advertisement->published_at) {
                $updates['published_at'] = now();
            }
            
            if (!$advertisement->expires_at) {
                $updates['expires_at'] = now()->addDays(30); // 30 days of publication
            }
            
            if (!empty($updates)) {
                $advertisement->update($updates);
            }
            
            // Notify the user that the ad was published
            if ($advertisement->user) {
                $advertisement->user->notify(new AdvertisementPublishedNotification($advertisement));
            }
        }
        
        // If the ad has expired
        if ($advertisement->expires_at && $advertisement->expires_at->isPast() && 
            $currentStatus !== Advertisement::STATUS_EXPIRED) {
            $advertisement->status = Advertisement::STATUS_EXPIRED;
            $advertisement->saveQuietly();
            
            // Notify the user that the ad has expired
            if ($advertisement->user) {
                $advertisement->user->notify(new AdvertisementExpiredNotification($advertisement));
            }
        }
    }

    /**
     * Handle the Advertisement "deleted" event.
     */
    public function deleted(Advertisement $advertisement): void
    {
        // Clear ad images
        $advertisement->clearMediaCollection('images');
        $advertisement->clearMediaCollection('featured_image');
    }

    /**
     * Handle the Advertisement "restored" event.
     */
    public function restored(Advertisement $advertisement): void
    {
        //
    }

    /**
     * Handle the Advertisement "force deleted" event.
     */
    public function forceDeleted(Advertisement $advertisement): void
    {
        //
    }
}
