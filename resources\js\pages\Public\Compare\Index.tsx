import { Head, Link, router } from '@inertiajs/react';
import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
    X, 
    Plus, 
    Car,
    Calendar,
    Gauge,
    Fuel,
    Settings,
    MapPin,
    Eye,
    Heart,
    Share2,
    Download
} from 'lucide-react';
import { ProductImage } from '@/components/ui/responsive-image';

interface Vehicle {
    id: number;
    title: string;
    price: number;
    slug: string;
    images: any[];
    user: {
        id: number;
        name: string;
        city?: string;
        state?: string;
    };
    vehicle: {
        brand: {
            name: string;
        };
        model: {
            name: string;
        };
        year: number;
        mileage?: number;
        fuel_type: string;
        transmission: string;
        condition: string;
        doors?: number;
        color?: string;
        engine_size?: string;
    };
    views: number;
    created_at: string;
}

interface Props {
    vehicles: Vehicle[];
    maxComparisons: number;
}

export default function CompareIndex({ vehicles, maxComparisons }: Props) {
    const [comparisonItems, setComparisonItems] = useState<Vehicle[]>(vehicles);

    const removeFromComparison = (vehicleId: number) => {
        router.delete(`/comparacao/remover/${vehicleId}`, {
            preserveScroll: true,
            onSuccess: () => {
                setComparisonItems(prev => prev.filter(v => v.id !== vehicleId));
            },
        });
    };

    const clearComparison = () => {
        router.delete('/comparacao/limpar', {
            preserveScroll: true,
            onSuccess: () => {
                setComparisonItems([]);
            },
        });
    };

    const formatPrice = (price: number) => {
        return new Intl.NumberFormat('pt-BR', {
            style: 'currency',
            currency: 'BRL',
        }).format(price);
    };

    const formatMileage = (mileage?: number) => {
        if (!mileage) return 'N/A';
        return new Intl.NumberFormat('pt-BR').format(mileage) + ' km';
    };

    const getConditionBadge = (condition: string) => {
        const conditionConfig = {
            new: { label: 'Novo', variant: 'default' as const },
            used: { label: 'Usado', variant: 'secondary' as const },
            certified: { label: 'Seminovo', variant: 'outline' as const },
        };

        const config = conditionConfig[condition as keyof typeof conditionConfig] || { 
            label: condition, 
            variant: 'secondary' as const 
        };
        return <Badge variant={config.variant}>{config.label}</Badge>;
    };

    const getFuelTypeLabel = (fuelType: string) => {
        const fuelTypes = {
            gasoline: 'Gasolina',
            ethanol: 'Etanol',
            flex: 'Flex',
            diesel: 'Diesel',
            electric: 'Elétrico',
            hybrid: 'Híbrido',
        };
        return fuelTypes[fuelType as keyof typeof fuelTypes] || fuelType;
    };

    const getTransmissionLabel = (transmission: string) => {
        const transmissions = {
            manual: 'Manual',
            automatic: 'Automático',
            cvt: 'CVT',
            automated: 'Automatizada',
        };
        return transmissions[transmission as keyof typeof transmissions] || transmission;
    };

    if (comparisonItems.length === 0) {
        return (
            <>
                <Head title="Comparar Veículos" />
                
                <div className="container mx-auto px-4 py-8">
                    <div className="text-center">
                        <Car className="h-16 w-16 mx-auto mb-4 text-muted-foreground" />
                        <h1 className="text-2xl font-bold mb-2">Nenhum veículo para comparar</h1>
                        <p className="text-muted-foreground mb-6">
                            Adicione veículos à sua lista de comparação para ver as diferenças lado a lado.
                        </p>
                        <Button asChild>
                            <Link href="/veiculos">
                                Buscar Veículos
                            </Link>
                        </Button>
                    </div>
                </div>
            </>
        );
    }

    return (
        <>
            <Head title="Comparar Veículos" />
            
            <div className="container mx-auto px-4 py-8">
                {/* Header */}
                <div className="flex items-center justify-between mb-6">
                    <div>
                        <h1 className="text-3xl font-bold tracking-tight">Comparar Veículos</h1>
                        <p className="text-muted-foreground">
                            Compare até {maxComparisons} veículos lado a lado
                        </p>
                    </div>
                    <div className="flex items-center gap-2">
                        <Button variant="outline">
                            <Share2 className="h-4 w-4 mr-2" />
                            Compartilhar
                        </Button>
                        <Button variant="outline">
                            <Download className="h-4 w-4 mr-2" />
                            Exportar PDF
                        </Button>
                        <Button variant="outline" onClick={clearComparison}>
                            <X className="h-4 w-4 mr-2" />
                            Limpar Tudo
                        </Button>
                    </div>
                </div>

                {/* Comparison Grid */}
                <div className="grid gap-6" style={{ gridTemplateColumns: `repeat(${Math.min(comparisonItems.length, 3)}, 1fr)` }}>
                    {comparisonItems.map((vehicle) => (
                        <Card key={vehicle.id} className="relative">
                            <Button
                                variant="outline"
                                size="sm"
                                className="absolute top-2 right-2 z-10"
                                onClick={() => removeFromComparison(vehicle.id)}
                            >
                                <X className="h-4 w-4" />
                            </Button>

                            <CardHeader className="pb-4">
                                <ProductImage
                                    src={vehicle.images[0]?.url}
                                    alt={vehicle.title}
                                    className="w-full h-48 rounded-lg object-cover"
                                    featured={true}
                                />
                                <div className="space-y-2">
                                    <CardTitle className="text-lg">{vehicle.title}</CardTitle>
                                    <div className="flex items-center justify-between">
                                        <p className="text-2xl font-bold text-primary">
                                            {formatPrice(vehicle.price)}
                                        </p>
                                        {getConditionBadge(vehicle.vehicle.condition)}
                                    </div>
                                </div>
                            </CardHeader>

                            <CardContent className="space-y-4">
                                {/* Basic Info */}
                                <div className="space-y-3">
                                    <div className="flex items-center gap-2">
                                        <Car className="h-4 w-4 text-muted-foreground" />
                                        <span className="text-sm">
                                            {vehicle.vehicle.brand.name} {vehicle.vehicle.model.name}
                                        </span>
                                    </div>
                                    <div className="flex items-center gap-2">
                                        <Calendar className="h-4 w-4 text-muted-foreground" />
                                        <span className="text-sm">{vehicle.vehicle.year}</span>
                                    </div>
                                    <div className="flex items-center gap-2">
                                        <Gauge className="h-4 w-4 text-muted-foreground" />
                                        <span className="text-sm">{formatMileage(vehicle.vehicle.mileage)}</span>
                                    </div>
                                    <div className="flex items-center gap-2">
                                        <Fuel className="h-4 w-4 text-muted-foreground" />
                                        <span className="text-sm">{getFuelTypeLabel(vehicle.vehicle.fuel_type)}</span>
                                    </div>
                                    <div className="flex items-center gap-2">
                                        <Settings className="h-4 w-4 text-muted-foreground" />
                                        <span className="text-sm">{getTransmissionLabel(vehicle.vehicle.transmission)}</span>
                                    </div>
                                    <div className="flex items-center gap-2">
                                        <MapPin className="h-4 w-4 text-muted-foreground" />
                                        <span className="text-sm">
                                            {vehicle.user.city}, {vehicle.user.state}
                                        </span>
                                    </div>
                                </div>

                                <Separator />

                                {/* Detailed Specs */}
                                <div className="space-y-2">
                                    <h4 className="font-medium text-sm">Especificações</h4>
                                    <div className="grid grid-cols-2 gap-2 text-xs">
                                        {vehicle.vehicle.doors && (
                                            <div>
                                                <span className="text-muted-foreground">Portas:</span>
                                                <span className="ml-1">{vehicle.vehicle.doors}</span>
                                            </div>
                                        )}
                                        {vehicle.vehicle.color && (
                                            <div>
                                                <span className="text-muted-foreground">Cor:</span>
                                                <span className="ml-1">{vehicle.vehicle.color}</span>
                                            </div>
                                        )}
                                        {vehicle.vehicle.engine_size && (
                                            <div>
                                                <span className="text-muted-foreground">Motor:</span>
                                                <span className="ml-1">{vehicle.vehicle.engine_size}</span>
                                            </div>
                                        )}
                                        <div>
                                            <span className="text-muted-foreground">Visualizações:</span>
                                            <span className="ml-1">{vehicle.views}</span>
                                        </div>
                                    </div>
                                </div>

                                <Separator />

                                {/* Seller Info */}
                                <div className="space-y-2">
                                    <h4 className="font-medium text-sm">Vendedor</h4>
                                    <p className="text-sm text-muted-foreground">{vehicle.user.name}</p>
                                    <p className="text-xs text-muted-foreground">
                                        Anunciado em {new Date(vehicle.created_at).toLocaleDateString()}
                                    </p>
                                </div>

                                <Separator />

                                {/* Actions */}
                                <div className="flex flex-col gap-2">
                                    <Button asChild className="w-full">
                                        <Link href={`/veiculos/${vehicle.slug}`}>
                                            <Eye className="h-4 w-4 mr-2" />
                                            Ver Detalhes
                                        </Link>
                                    </Button>
                                    <div className="flex gap-2">
                                        <Button variant="outline" size="sm" className="flex-1">
                                            <Heart className="h-4 w-4 mr-1" />
                                            Favoritar
                                        </Button>
                                        <Button variant="outline" size="sm" className="flex-1">
                                            <Share2 className="h-4 w-4 mr-1" />
                                            Compartilhar
                                        </Button>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    ))}

                    {/* Add More Button */}
                    {comparisonItems.length < maxComparisons && (
                        <Card className="border-dashed border-2 flex items-center justify-center min-h-[600px]">
                            <div className="text-center">
                                <Plus className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                                <h3 className="text-lg font-medium mb-2">Adicionar Veículo</h3>
                                <p className="text-muted-foreground mb-4">
                                    Compare até {maxComparisons} veículos
                                </p>
                                <Button asChild>
                                    <Link href="/veiculos">
                                        Buscar Veículos
                                    </Link>
                                </Button>
                            </div>
                        </Card>
                    )}
                </div>

                {/* Comparison Table for Mobile */}
                <div className="mt-8 lg:hidden">
                    <Card>
                        <CardHeader>
                            <CardTitle>Comparação Detalhada</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                {comparisonItems.map((vehicle, index) => (
                                    <div key={vehicle.id} className="border rounded-lg p-4">
                                        <h4 className="font-medium mb-2">{vehicle.title}</h4>
                                        <div className="grid grid-cols-2 gap-2 text-sm">
                                            <div>Preço: {formatPrice(vehicle.price)}</div>
                                            <div>Ano: {vehicle.vehicle.year}</div>
                                            <div>Quilometragem: {formatMileage(vehicle.vehicle.mileage)}</div>
                                            <div>Combustível: {getFuelTypeLabel(vehicle.vehicle.fuel_type)}</div>
                                            <div>Câmbio: {getTransmissionLabel(vehicle.vehicle.transmission)}</div>
                                            <div>Condição: {vehicle.vehicle.condition}</div>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </>
    );
}
