<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use App\Models\Advertisement;

class UpdateAdvertisementRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return $this->user()->can('update', $this->route('advertisement'));
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $advertisement = $this->route('advertisement');
        
        return [
            'title' => ['required', 'string', 'max:255'],
            'description' => ['required', 'string', 'max:5000'],
            'vehicle_id' => ['required', 'exists:vehicles,id'],
            'status' => ['required', 'string', Rule::in([
                Advertisement::STATUS_DRAFT,
                Advertisement::STATUS_PENDING_REVIEW,
                Advertisement::STATUS_APPROVED,
                Advertisement::STATUS_REJECTED,
                Advertisement::STATUS_PUBLISHED,
                Advertisement::STATUS_EXPIRED,
                Advertisement::STATUS_SOLD,
            ])],
            'rejection_reason' => ['nullable', 'required_if:status,'.Advertisement::STATUS_REJECTED, 'string', 'max:1000'],
            'price' => ['required', 'numeric', 'min:0', 'max:99999999.99'],
            'is_negotiable' => ['boolean'],
            'is_featured' => ['boolean'],
            'contact_phone' => ['required', 'string', 'max:20'],
            'contact_email' => ['required', 'email', 'max:255'],
            'location' => ['required', 'string', 'max:255'],
            'latitude' => ['nullable', 'numeric', 'between:-90,90'],
            'longitude' => ['nullable', 'numeric', 'between:-180,180'],
            'published_at' => ['nullable', 'date'],
            'expires_at' => ['nullable', 'date', 'after:published_at'],
            'images' => ['sometimes', 'array', 'max:10'],
            'images.*' => ['image', 'mimes:jpeg,png,jpg,gif,webp', 'max:5120'], // 5MB max
            'featured_image' => ['sometimes', 'image', 'mimes:jpeg,png,jpg,gif,webp', 'max:5120'],
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'vehicle_id.required' => 'Selecione um veículo para o anúncio.',
            'rejection_reason.required_if' => 'O motivo da rejeição é obrigatório quando o status for Rejeitado.',
            'images.max' => 'Você pode enviar no máximo 10 imagens.',
            'images.*.image' => 'Cada arquivo deve ser uma imagem válida.',
            'images.*.mimes' => 'Apenas imagens nos formatos jpeg, png, jpg, gif ou webp são permitidas.',
            'images.*.max' => 'Cada imagem não pode ser maior que 5MB.',
            'featured_image.image' => 'A imagem destacada deve ser um arquivo de imagem válido.',
            'featured_image.mimes' => 'A imagem destacada deve estar no formato jpeg, png, jpg, gif ou webp.',
            'featured_image.max' => 'A imagem destacada não pode ser maior que 5MB.',
            'expires_at.after' => 'A data de expiração deve ser posterior à data de publicação.',
        ];
    }
}
