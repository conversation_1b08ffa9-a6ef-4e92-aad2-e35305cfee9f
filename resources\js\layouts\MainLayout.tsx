import Footer from '@/components/Footer';
import HeaderOLX from '@/components/HeaderOLX';
import { Category } from '@/types';
import { ReactNode } from 'react';

interface User {
    id: number;
    name: string;
    email: string;
}

interface MainLayoutProps {
    categories?: Category[];
    children: ReactNode;
    auth?: {
        user: User | null;
    };
    errors?: Record<string, string>;
    title?: string;
}

export default function MainLayout({
    categories = [],
    children,
}: MainLayoutProps) {
    return (
        <div className="min-h-screen bg-background">
            <HeaderOLX categories={categories} />
            <main>{children}</main>
            <Footer />
        </div>
    );
}
