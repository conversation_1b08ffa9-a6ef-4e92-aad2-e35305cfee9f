import Link from "next/link"
import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { MapPin, Calendar, Eye, Heart, MoreVertical, Edit, Trash2, Pause, Play } from "lucide-react"

const userListings = [
  {
    id: 1,
    title: "Honda Civic 2022 EXL",
    price: "R$ 89.900",
    location: "São Paulo, SP",
    year: "2022",
    mileage: "25.000 km",
    image: "/honda-civic-2022-silver-car.jpg",
    status: "active",
    views: 247,
    favorites: 12,
    contacts: 8,
    createdAt: "2024-01-15",
  },
  {
    id: 2,
    title: "Yamaha MT-07 2021",
    price: "R$ 32.500",
    location: "São Paulo, SP",
    year: "2021",
    mileage: "15.000 km",
    image: "/yamaha-mt-07-2021-blue-motorcycle.jpg",
    status: "paused",
    views: 156,
    favorites: 8,
    contacts: 3,
    createdAt: "2024-01-10",
  },
  {
    id: 3,
    title: "Kit Suspensão Civic Completo",
    price: "R$ 1.250",
    location: "São Paulo, SP",
    year: "2023",
    mileage: "Novo",
    image: "/car-suspension-kit-parts.jpg",
    status: "sold",
    views: 89,
    favorites: 5,
    contacts: 12,
    createdAt: "2024-01-05",
  },
]

const statusConfig = {
  active: { label: "Ativo", variant: "default" as const, color: "bg-accent" },
  paused: { label: "Pausado", variant: "secondary" as const, color: "bg-muted" },
  sold: { label: "Vendido", variant: "outline" as const, color: "bg-primary" },
}

export function UserListings() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold">Meus Anúncios</h2>
        <Button asChild>
          <Link href="/anunciar">Criar novo anúncio</Link>
        </Button>
      </div>

      <div className="grid grid-cols-1 gap-6">
        {userListings.map((listing) => {
          const status = statusConfig[listing.status as keyof typeof statusConfig]

          return (
            <Card key={listing.id} className="overflow-hidden">
              <CardContent className="p-0">
                <div className="flex flex-col sm:flex-row">
                  {/* Image */}
                  <div className="relative sm:w-48 h-48 sm:h-auto">
                    <img
                      src={listing.image || "/placeholder.svg"}
                      alt={listing.title}
                      className="w-full h-full object-cover"
                    />
                    <div className="absolute top-3 left-3">
                      <Badge variant={status.variant} className="flex items-center gap-1">
                        <div className={`w-2 h-2 rounded-full ${status.color}`}></div>
                        {status.label}
                      </Badge>
                    </div>
                  </div>

                  {/* Content */}
                  <div className="flex-1 p-6">
                    <div className="flex items-start justify-between mb-4">
                      <div>
                        <h3 className="text-lg font-semibold mb-2">{listing.title}</h3>
                        <div className="text-2xl font-bold text-primary mb-2">{listing.price}</div>
                        <div className="flex items-center text-sm text-muted-foreground space-x-4">
                          <div className="flex items-center">
                            <MapPin className="h-4 w-4 mr-1" />
                            {listing.location}
                          </div>
                          <div className="flex items-center">
                            <Calendar className="h-4 w-4 mr-1" />
                            {listing.year} • {listing.mileage}
                          </div>
                        </div>
                      </div>

                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <MoreVertical className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem>
                            <Edit className="h-4 w-4 mr-2" />
                            Editar
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            {listing.status === "active" ? (
                              <>
                                <Pause className="h-4 w-4 mr-2" />
                                Pausar
                              </>
                            ) : (
                              <>
                                <Play className="h-4 w-4 mr-2" />
                                Reativar
                              </>
                            )}
                          </DropdownMenuItem>
                          <DropdownMenuItem className="text-destructive">
                            <Trash2 className="h-4 w-4 mr-2" />
                            Excluir
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>

                    {/* Stats */}
                    <div className="grid grid-cols-3 gap-4 mb-4">
                      <div className="text-center">
                        <div className="flex items-center justify-center text-muted-foreground mb-1">
                          <Eye className="h-4 w-4 mr-1" />
                        </div>
                        <div className="text-lg font-semibold">{listing.views}</div>
                        <div className="text-xs text-muted-foreground">Visualizações</div>
                      </div>
                      <div className="text-center">
                        <div className="flex items-center justify-center text-muted-foreground mb-1">
                          <Heart className="h-4 w-4 mr-1" />
                        </div>
                        <div className="text-lg font-semibold">{listing.favorites}</div>
                        <div className="text-xs text-muted-foreground">Favoritos</div>
                      </div>
                      <div className="text-center">
                        <div className="flex items-center justify-center text-muted-foreground mb-1">
                          <Calendar className="h-4 w-4 mr-1" />
                        </div>
                        <div className="text-lg font-semibold">{listing.contacts}</div>
                        <div className="text-xs text-muted-foreground">Contatos</div>
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="flex space-x-2">
                      <Button variant="outline" size="sm" asChild className="bg-transparent">
                        <Link href={`/anuncio/${listing.id}`}>Ver anúncio</Link>
                      </Button>
                      <Button variant="outline" size="sm" className="bg-transparent">
                        Editar
                      </Button>
                      {listing.status === "active" && (
                        <Button variant="outline" size="sm" className="bg-transparent">
                          Pausar
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>
    </div>
  )
}
