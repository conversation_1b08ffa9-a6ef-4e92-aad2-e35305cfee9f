import { <PERSON>, <PERSON>, router } from '@inertiajs/react';
import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { 
    Table, 
    TableBody, 
    TableCell, 
    TableHead, 
    TableHeader, 
    TableRow 
} from '@/components/ui/table';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { 
    Search, 
    MoreHorizontal, 
    Eye, 
    Edit, 
    Trash2, 
    UserCheck, 
    UserX,
    Mail,
    Filter
} from 'lucide-react';
import { AvatarImage } from '@/components/ui/responsive-image';

interface User {
    id: number;
    name: string;
    email: string;
    type: 'individual' | 'company';
    status: 'active' | 'inactive' | 'suspended';
    email_verified_at: string | null;
    created_at: string;
    avatar?: string;
    vehicles_count: number;
    parts_count: number;
    advertisements_count: number;
}

interface PaginatedUsers {
    data: User[];
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
    from: number;
    to: number;
}

interface Props {
    users: PaginatedUsers;
    filters: {
        search?: string;
        status?: string;
        type?: string;
    };
}

export default function UsersIndex({ users, filters }: Props) {
    const [search, setSearch] = useState(filters.search || '');

    const handleSearch = (e: React.FormEvent) => {
        e.preventDefault();
        router.get('/admin/usuarios', { search }, { preserveState: true });
    };

    const handleAction = (action: string, userId: number) => {
        const actions = {
            activate: () => router.post(`/admin/usuarios/${userId}/ativar`),
            suspend: () => router.post(`/admin/usuarios/${userId}/suspender`),
            verifyEmail: () => router.post(`/admin/usuarios/${userId}/verificar-email`),
            delete: () => {
                if (confirm('Tem certeza que deseja excluir este usuário?')) {
                    router.delete(`/admin/usuarios/${userId}`);
                }
            },
        };

        actions[action as keyof typeof actions]?.();
    };

    const getStatusBadge = (status: string) => {
        const statusConfig = {
            active: { label: 'Ativo', variant: 'default' as const },
            inactive: { label: 'Inativo', variant: 'secondary' as const },
            suspended: { label: 'Suspenso', variant: 'destructive' as const },
        };

        const config = statusConfig[status as keyof typeof statusConfig] || { label: status, variant: 'secondary' as const };
        return <Badge variant={config.variant}>{config.label}</Badge>;
    };

    const getTypeBadge = (type: string) => {
        const typeConfig = {
            individual: { label: 'Pessoa Física', variant: 'outline' as const },
            company: { label: 'Empresa', variant: 'secondary' as const },
        };

        const config = typeConfig[type as keyof typeof typeConfig] || { label: type, variant: 'outline' as const };
        return <Badge variant={config.variant}>{config.label}</Badge>;
    };

    return (
        <>
            <Head title="Gerenciar Usuários" />
            
            <div className="space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold tracking-tight">Usuários</h1>
                        <p className="text-muted-foreground">
                            Gerencie todos os usuários da plataforma
                        </p>
                    </div>
                </div>

                {/* Filters */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <Filter className="h-5 w-5" />
                            Filtros
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <form onSubmit={handleSearch} className="flex gap-4">
                            <div className="flex-1">
                                <Input
                                    placeholder="Buscar por nome ou email..."
                                    value={search}
                                    onChange={(e) => setSearch(e.target.value)}
                                    className="max-w-sm"
                                />
                            </div>
                            <Button type="submit">
                                <Search className="h-4 w-4 mr-2" />
                                Buscar
                            </Button>
                        </form>
                    </CardContent>
                </Card>

                {/* Users Table */}
                <Card>
                    <CardHeader>
                        <CardTitle>
                            Lista de Usuários ({users.total})
                        </CardTitle>
                        <CardDescription>
                            Mostrando {users.from} a {users.to} de {users.total} usuários
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <Table>
                            <TableHeader>
                                <TableRow>
                                    <TableHead>Usuário</TableHead>
                                    <TableHead>Tipo</TableHead>
                                    <TableHead>Status</TableHead>
                                    <TableHead>Email Verificado</TableHead>
                                    <TableHead>Conteúdo</TableHead>
                                    <TableHead>Cadastro</TableHead>
                                    <TableHead className="text-right">Ações</TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                {users.data.map((user) => (
                                    <TableRow key={user.id}>
                                        <TableCell>
                                            <div className="flex items-center gap-3">
                                                <AvatarImage
                                                    src={user.avatar}
                                                    alt={user.name}
                                                    size="small"
                                                />
                                                <div>
                                                    <div className="font-medium">{user.name}</div>
                                                    <div className="text-sm text-muted-foreground">
                                                        {user.email}
                                                    </div>
                                                </div>
                                            </div>
                                        </TableCell>
                                        <TableCell>
                                            {getTypeBadge(user.type)}
                                        </TableCell>
                                        <TableCell>
                                            {getStatusBadge(user.status)}
                                        </TableCell>
                                        <TableCell>
                                            {user.email_verified_at ? (
                                                <Badge variant="default">Verificado</Badge>
                                            ) : (
                                                <Badge variant="destructive">Não Verificado</Badge>
                                            )}
                                        </TableCell>
                                        <TableCell>
                                            <div className="text-sm">
                                                <div>{user.vehicles_count} veículos</div>
                                                <div>{user.parts_count} peças</div>
                                                <div>{user.advertisements_count} anúncios</div>
                                            </div>
                                        </TableCell>
                                        <TableCell>
                                            <div className="text-sm">
                                                {new Date(user.created_at).toLocaleDateString()}
                                            </div>
                                        </TableCell>
                                        <TableCell className="text-right">
                                            <DropdownMenu>
                                                <DropdownMenuTrigger asChild>
                                                    <Button variant="ghost" className="h-8 w-8 p-0">
                                                        <MoreHorizontal className="h-4 w-4" />
                                                    </Button>
                                                </DropdownMenuTrigger>
                                                <DropdownMenuContent align="end">
                                                    <DropdownMenuLabel>Ações</DropdownMenuLabel>
                                                    <DropdownMenuItem asChild>
                                                        <Link href={`/admin/usuarios/${user.id}`}>
                                                            <Eye className="h-4 w-4 mr-2" />
                                                            Ver Detalhes
                                                        </Link>
                                                    </DropdownMenuItem>
                                                    <DropdownMenuItem asChild>
                                                        <Link href={`/admin/usuarios/${user.id}/editar`}>
                                                            <Edit className="h-4 w-4 mr-2" />
                                                            Editar
                                                        </Link>
                                                    </DropdownMenuItem>
                                                    <DropdownMenuSeparator />
                                                    {user.status === 'active' ? (
                                                        <DropdownMenuItem 
                                                            onClick={() => handleAction('suspend', user.id)}
                                                            className="text-orange-600"
                                                        >
                                                            <UserX className="h-4 w-4 mr-2" />
                                                            Suspender
                                                        </DropdownMenuItem>
                                                    ) : (
                                                        <DropdownMenuItem 
                                                            onClick={() => handleAction('activate', user.id)}
                                                            className="text-green-600"
                                                        >
                                                            <UserCheck className="h-4 w-4 mr-2" />
                                                            Ativar
                                                        </DropdownMenuItem>
                                                    )}
                                                    {!user.email_verified_at && (
                                                        <DropdownMenuItem 
                                                            onClick={() => handleAction('verifyEmail', user.id)}
                                                            className="text-blue-600"
                                                        >
                                                            <Mail className="h-4 w-4 mr-2" />
                                                            Verificar Email
                                                        </DropdownMenuItem>
                                                    )}
                                                    <DropdownMenuSeparator />
                                                    <DropdownMenuItem 
                                                        onClick={() => handleAction('delete', user.id)}
                                                        className="text-red-600"
                                                    >
                                                        <Trash2 className="h-4 w-4 mr-2" />
                                                        Excluir
                                                    </DropdownMenuItem>
                                                </DropdownMenuContent>
                                            </DropdownMenu>
                                        </TableCell>
                                    </TableRow>
                                ))}
                            </TableBody>
                        </Table>

                        {/* Pagination */}
                        {users.last_page > 1 && (
                            <div className="flex items-center justify-between mt-4">
                                <div className="text-sm text-muted-foreground">
                                    Página {users.current_page} de {users.last_page}
                                </div>
                                <div className="flex gap-2">
                                    {users.current_page > 1 && (
                                        <Button 
                                            variant="outline" 
                                            size="sm"
                                            onClick={() => router.get('/admin/usuarios', { 
                                                ...filters, 
                                                page: users.current_page - 1 
                                            })}
                                        >
                                            Anterior
                                        </Button>
                                    )}
                                    {users.current_page < users.last_page && (
                                        <Button 
                                            variant="outline" 
                                            size="sm"
                                            onClick={() => router.get('/admin/usuarios', { 
                                                ...filters, 
                                                page: users.current_page + 1 
                                            })}
                                        >
                                            Próxima
                                        </Button>
                                    )}
                                </div>
                            </div>
                        )}
                    </CardContent>
                </Card>
            </div>
        </>
    );
}
