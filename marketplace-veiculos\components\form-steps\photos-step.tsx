"use client"

import type React from "react"

import { useState, useRef } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Upload, X, Camera, ImageIcon } from "lucide-react"

interface PhotosStepProps {
  formData: any
  updateFormData: (data: any) => void
  onNext: () => void
  onPrev: () => void
}

export function PhotosStep({ formData, updateFormData }: PhotosStepProps) {
  const fileInputRef = useRef<HTMLInputElement>(null)
  const [dragOver, setDragOver] = useState(false)

  const handleFileSelect = (files: FileList | null) => {
    if (!files) return

    const newFiles = Array.from(files).filter(
      (file) => file.type.startsWith("image/") && file.size <= 10 * 1024 * 1024, // 10MB limit
    )

    const currentPhotos = formData.photos || []
    const updatedPhotos = [...currentPhotos, ...newFiles].slice(0, 20) // Max 20 photos

    updateFormData({ photos: updatedPhotos })
  }

  const removePhoto = (index: number) => {
    const currentPhotos = formData.photos || []
    const updatedPhotos = currentPhotos.filter((_: any, i: number) => i !== index)
    updateFormData({ photos: updatedPhotos })
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(false)
    handleFileSelect(e.dataTransfer.files)
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(true)
  }

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(false)
  }

  const photos = formData.photos || []

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h3 className="text-lg font-semibold mb-2">Adicione fotos do seu veículo</h3>
        <p className="text-muted-foreground">
          Fotos de qualidade aumentam significativamente as chances de venda. Adicione até 20 fotos.
        </p>
      </div>

      {/* Upload Area */}
      <Card
        className={`border-2 border-dashed transition-colors ${
          dragOver ? "border-primary bg-primary/5" : "border-muted-foreground/25"
        }`}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
      >
        <CardContent className="p-8">
          <div className="text-center space-y-4">
            <div className="mx-auto w-16 h-16 bg-muted rounded-full flex items-center justify-center">
              <Upload className="h-8 w-8 text-muted-foreground" />
            </div>

            <div>
              <Button variant="outline" onClick={() => fileInputRef.current?.click()} className="bg-transparent">
                <Camera className="h-4 w-4 mr-2" />
                Selecionar fotos
              </Button>
              <p className="text-sm text-muted-foreground mt-2">ou arraste e solte as imagens aqui</p>
            </div>

            <div className="text-xs text-muted-foreground">
              <p>Formatos aceitos: JPG, PNG, WebP</p>
              <p>Tamanho máximo: 10MB por foto</p>
              <p>Máximo: 20 fotos</p>
            </div>
          </div>

          <input
            ref={fileInputRef}
            type="file"
            multiple
            accept="image/*"
            className="hidden"
            onChange={(e) => handleFileSelect(e.target.files)}
          />
        </CardContent>
      </Card>

      {/* Photo Tips */}
      <Card>
        <CardContent className="p-4">
          <h4 className="font-semibold mb-3 flex items-center">
            <ImageIcon className="h-4 w-4 mr-2" />
            Dicas para boas fotos
          </h4>
          <ul className="text-sm text-muted-foreground space-y-1">
            <li>• Tire fotos em local bem iluminado, preferencialmente durante o dia</li>
            <li>• Fotografe o veículo por todos os ângulos: frente, traseira, laterais</li>
            <li>• Inclua fotos do interior, painel, bancos e porta-malas</li>
            <li>• Mostre detalhes importantes como pneus, motor e documentação</li>
            <li>• Evite fotos borradas ou com reflexos excessivos</li>
          </ul>
        </CardContent>
      </Card>

      {/* Photo Grid */}
      {photos.length > 0 && (
        <div>
          <Label className="text-base font-semibold">Fotos selecionadas ({photos.length}/20)</Label>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-4">
            {photos.map((photo: File, index: number) => (
              <div key={index} className="relative group">
                <img
                  src={URL.createObjectURL(photo) || "/placeholder.svg"}
                  alt={`Foto ${index + 1}`}
                  className="w-full h-32 object-cover rounded-lg"
                />
                <Button
                  variant="destructive"
                  size="sm"
                  className="absolute top-2 right-2 h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                  onClick={() => removePhoto(index)}
                >
                  <X className="h-3 w-3" />
                </Button>
                {index === 0 && (
                  <div className="absolute bottom-2 left-2 bg-primary text-primary-foreground text-xs px-2 py-1 rounded">
                    Principal
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}
