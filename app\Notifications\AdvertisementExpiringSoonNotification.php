<?php

namespace App\Notifications;

use App\Models\Advertisement;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class AdvertisementExpiringSoonNotification extends Notification implements ShouldQueue
{
    use Queueable;

    public $advertisement;
    public $daysUntilExpiration;

    /**
     * Create a new notification instance.
     */
    public function __construct(Advertisement $advertisement, int $daysUntilExpiration)
    {
        $this->advertisement = $advertisement;
        $this->daysUntilExpiration = $daysUntilExpiration;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable)
    {
        $expirationDate = $this->advertisement->expires_at->format('d/m/Y');
        $title = $this->advertisement->title;
        $days = $this->daysUntilExpiration;
        
        return (new MailMessage)
                    ->subject("Seu anúncio expira em {$days} dias!")
                    ->line("Seu anúncio \"{$title}\" está prestes a expirar em {$days} dias (em {$expirationDate}).")
                    ->line('Para manter seu anúncio ativo, renove-o agora e continue recebendo visualizações.')
                    ->action('Renovar Anúncio', route('advertisements.renew', $this->advertisement))
                    ->line('Não perca oportunidades de venda! Renove já!');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'message' => "Seu anúncio \"{$this->advertisement->title}\" expira em {$this->daysUntilExpiration} dias em {$this->advertisement->expires_at->format('d/m/Y')}.",
            'link' => route('advertisements.renew', $this->advertisement),
            'advertisement_id' => $this->advertisement->id,
            'advertisement_title' => $this->advertisement->title,
            'expires_at' => $this->advertisement->expires_at,
            'days_until_expiration' => $this->daysUntilExpiration,
        ];
    }
}
