import { Category } from '@/components/Header';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import MainLayout from '@/layouts/MainLayout';
import { PageProps } from '@/types';
import { Link } from '@inertiajs/react';

interface CategoriesIndexProps extends PageProps {
    categories: Category[];
}

export default function CategoriesIndex({ categories }: CategoriesIndexProps) {
    return (
        <MainLayout>
            <div className="container mx-auto px-4 py-8">
                <h1 className="mb-8 text-3xl font-bold">Todas as Categorias</h1>

                <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
                    {categories.map((category) => (
                        <Link
                            key={category.id}
                            href={`/pesquisar?category_id=${category.id}`}
                            className="block transition-opacity hover:opacity-90"
                        >
                            <Card className="h-full">
                                <CardHeader>
                                    <div className="flex items-center space-x-4">
                                        {category.icon && (
                                            <div className="rounded-lg bg-primary/10 p-3">
                                                <i
                                                    className={`${category.icon} text-2xl text-primary`}
                                                ></i>
                                            </div>
                                        )}
                                        <CardTitle className="text-xl">
                                            {category.name}
                                        </CardTitle>
                                    </div>
                                </CardHeader>
                                <CardContent>
                                    {category.children &&
                                        category.children.length > 0 && (
                                            <div className="mt-2">
                                                <h3 className="mb-2 text-sm font-medium text-muted-foreground">
                                                    Subcategorias:
                                                </h3>
                                                <div className="flex flex-wrap gap-2">
                                                    {category.children.map(
                                                        (subcategory) => (
                                                            <span
                                                                key={
                                                                    subcategory.id
                                                                }
                                                                className="inline-flex items-center rounded-full bg-primary/10 px-3 py-1 text-xs font-medium text-primary"
                                                            >
                                                                {
                                                                    subcategory.name
                                                                }
                                                            </span>
                                                        ),
                                                    )}
                                                </div>
                                            </div>
                                        )}
                                </CardContent>
                            </Card>
                        </Link>
                    ))}
                </div>
            </div>
        </MainLayout>
    );
}
