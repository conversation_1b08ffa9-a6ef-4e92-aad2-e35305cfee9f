<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Part extends Model
{
    use SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'user_id',
        'category_id',
        'brand_id',
        'name',
        'slug',
        'part_number',
        'description',
        'price',
        'promotional_price',
        'stock_quantity',
        'weight',
        'dimensions',
        'is_original',
        'is_featured',
        'status',
        'views',
        'seo_title',
        'seo_description',
        'seo_keywords',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'price' => 'decimal:2',
        'promotional_price' => 'decimal:2',
        'stock_quantity' => 'integer',
        'weight' => 'decimal:2',
        'is_original' => 'boolean',
        'is_featured' => 'boolean',
        'views' => 'integer',
    ];

    /**
     * The model's default values for attributes.
     *
     * @var array
     */
    protected $attributes = [
        'status' => 'draft',
        'is_original' => false,
        'is_featured' => false,
        'stock_quantity' => 0,
        'views' => 0,
    ];

    /**
     * The accessors to append to the model's array form.
     *
     * @var array
     */
    protected $appends = ['url', 'formatted_price', 'formatted_promotional_price'];

    /**
     * Get the user that owns the part.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the category that owns the part.
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class);
    }

    /**
     * Get the brand that owns the part.
     */
    public function brand(): BelongsTo
    {
        return $this->belongsTo(Brand::class);
    }

    /**
     * Get the images for the part.
     */
    public function images(): MorphMany
    {
        return $this->morphMany(Image::class, 'imageable')->orderBy('order');
    }

    /**
     * Get the main image of the part.
     */
    public function mainImage()
    {
        return $this->morphOne(Image::class, 'imageable')
            ->where('is_main', true);
    }

    /**
     * The features that belong to the part.
     */
    public function features(): BelongsToMany
    {
        return $this->belongsToMany(Feature::class, 'part_feature')
            ->withPivot('value')
            ->withTimestamps();
    }

    /**
     * The vehicles that are compatible with this part.
     */
    public function compatibleVehicles(): BelongsToMany
    {
        return $this->belongsToMany(Vehicle::class, 'part_vehicle')
            ->withPivot('notes')
            ->withTimestamps();
    }

    /**
     * Scope a query to only include published parts.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopePublished($query)
    {
        return $query->where('status', 'published');
    }

    /**
     * Scope a query to only include featured parts.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * Scope a query to only include parts in stock.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeInStock($query)
    {
        return $query->where('stock_quantity', '>', 0);
    }

    /**
     * Get the route key for the model.
     *
     * @return string
     */
    public function getRouteKeyName()
    {
        return 'slug';
    }

    /**
     * Get the URL for the part.
     *
     * @return string
     */
    public function getUrlAttribute()
    {
        return route('parts.show', $this->slug);
    }

    /**
     * Get the formatted price.
     *
     * @return string
     */
    public function getFormattedPriceAttribute()
    {
        return 'R$ ' . number_format($this->price, 2, ',', '.');
    }

    /**
     * Get the formatted promotional price.
     *
     * @return string|null
     */
    public function getFormattedPromotionalPriceAttribute()
    {
        return $this->promotional_price ? 'R$ ' . number_format($this->promotional_price, 2, ',', '.') : null;
    }

    /**
     * Check if the part has a promotional price.
     *
     * @return bool
     */
    public function hasPromotion(): bool
    {
        return !is_null($this->promotional_price);
    }

    /**
     * Get the final price of the part (considers promotional price if available).
     *
     * @return float
     */
    public function getFinalPriceAttribute(): float
    {
        return $this->promotional_price ?? $this->price;
    }

    /**
     * Check if the part is in stock.
     *
     * @return bool
     */
    public function inStock(): bool
    {
        return $this->stock_quantity > 0;
    }

    /**
     * Increment the view count for the part.
     *
     * @return void
     */
    public function incrementViewCount()
    {
        $this->increment('views');
    }
}
