import AdminLayout from '@/components/Layout/AdminLayout';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
    Card,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select';
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '@/components/ui/table';
import { formatCurrency } from '@/lib/utils';
import { PageProps, PaginatedData, Part, PartFilters } from '@/types';
import { Head, Link, router, usePage } from '@inertiajs/react';
import { Edit, Eye, Plus, Search, Trash2 } from 'lucide-react';
import { FormEvent, useState } from 'react';

interface PartIndexProps {
    parts: PaginatedData<Part>;
    filters: PartFilters;
}

export default function PartIndex() {
    const pageProps = usePage<PageProps<PartIndexProps>>().props;
    const { parts, filters } = pageProps as unknown as PartIndexProps;
    const { url } = usePage();
    const [search, setSearch] = useState(filters?.search ?? '');
    const [status, setStatus] = useState(filters?.status ?? '');

    const statuses = {
        draft: 'Rascunho',
        active: 'Ativo',
        inactive: 'Inativo',
        out_of_stock: 'Fora de Estoque',
    };

    const statusColors = {
        draft: 'bg-gray-100 text-gray-800',
        active: 'bg-green-100 text-green-800',
        inactive: 'bg-red-100 text-red-800',
        out_of_stock: 'bg-yellow-100 text-yellow-800',
    };

    const handleFilter = (key: string, value: string) => {
        const params = new URLSearchParams(url.split('?')[1] || '');

        if (value) {
            params.set(key, value);
        } else {
            params.delete(key);
        }

        router.get(url.split('?')[0] + '?' + params.toString());
    };

    const handleSearch = (e: FormEvent) => {
        e.preventDefault();
        handleFilter('search', search);
    };

    const handleStatusChange = (value: string) => {
        setStatus(value);
        handleFilter('status', value);
    };

    const handleDelete = (part: Part) => {
        if (
            confirm(
                'Tem certeza que deseja excluir esta peça? Esta ação não pode ser desfeita.',
            )
        ) {
            router.delete(route('admin.parts.destroy', { id: part.id }));
        }
    };

    return (
        <AdminLayout>
            <Head title="Gerenciar Peças" />

            <div className="space-y-6">
                <div className="flex items-center justify-between">
                    <div>
                        <h2 className="text-2xl font-bold tracking-tight">
                            Gerenciar Peças
                        </h2>
                        <p className="text-muted-foreground">
                            Visualize e gerencie todas as peças cadastradas no
                            sistema
                        </p>
                    </div>
                    <Button asChild>
                        <Link href={route('admin.parts.create')}>
                            <Plus className="mr-2 h-4 w-4" />
                            Nova Peça
                        </Link>
                    </Button>
                </div>

                <Card>
                    <CardHeader>
                        <div className="flex items-center justify-between">
                            <div>
                                <CardTitle>Peças</CardTitle>
                                <CardDescription>
                                    {parts.total} peça
                                    {parts.total !== 1 ? 's' : ''} encontrada
                                    {parts.total !== 1 ? 's' : ''}
                                </CardDescription>
                            </div>
                            <form
                                onSubmit={handleSearch}
                                className="flex items-center space-x-2"
                            >
                                <div className="relative">
                                    <Search className="absolute top-2.5 left-2.5 h-4 w-4 text-muted-foreground" />
                                    <Input
                                        type="search"
                                        placeholder="Buscar peças..."
                                        className="w-[200px] pl-8 lg:w-[300px]"
                                        value={search}
                                        onChange={(e) =>
                                            setSearch(e.target.value)
                                        }
                                    />
                                </div>
                                <Select
                                    value={status}
                                    onValueChange={handleStatusChange}
                                >
                                    <SelectTrigger className="w-[180px]">
                                        <SelectValue placeholder="Status" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="all">
                                            Todos os status
                                        </SelectItem>
                                        {Object.entries(statuses).map(
                                            ([key, label]) => (
                                                <SelectItem
                                                    key={key}
                                                    value={key}
                                                >
                                                    {label}
                                                </SelectItem>
                                            ),
                                        )}
                                    </SelectContent>
                                </Select>
                                <Button type="submit" variant="outline">
                                    <Search className="mr-2 h-4 w-4" />
                                    Filtrar
                                </Button>
                            </form>
                        </div>
                    </CardHeader>
                    <CardContent>
                        <div className="rounded-md border">
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead>Imagem</TableHead>
                                        <TableHead>Nome</TableHead>
                                        <TableHead>Número da Peça</TableHead>
                                        <TableHead>Marca</TableHead>
                                        <TableHead>Preço</TableHead>
                                        <TableHead>Estoque</TableHead>
                                        <TableHead>Compatíveis</TableHead>
                                        <TableHead>Status</TableHead>
                                        <TableHead className="text-right">
                                            Ações
                                        </TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {parts.data.length === 0 ? (
                                        <TableRow>
                                            <TableCell
                                                colSpan={8}
                                                className="py-8 text-center text-muted-foreground"
                                            >
                                                Nenhuma peça encontrada.
                                            </TableCell>
                                        </TableRow>
                                    ) : (
                                        parts.data.map((part: Part) => (
                                            <TableRow
                                                key={part.id}
                                                className="hover:bg-gray-50"
                                            >
                                                <TableCell>
                                                    {part.media &&
                                                    part.media.length > 0 ? (
                                                        <img
                                                            src={
                                                                part.media[0]
                                                                    .original_url
                                                            }
                                                            alt={part.name}
                                                            className="h-10 w-10 rounded-md object-cover"
                                                        />
                                                    ) : (
                                                        <div className="flex h-10 w-10 items-center justify-center rounded-md bg-muted">
                                                            <span className="text-xs text-muted-foreground">
                                                                Sem imagem
                                                            </span>
                                                        </div>
                                                    )}
                                                </TableCell>
                                                <TableCell className="font-medium">
                                                    {part.name}
                                                </TableCell>
                                                <TableCell>
                                                    {part.part_number || '-'}
                                                </TableCell>
                                                <TableCell>
                                                    {part.brand?.name || '-'}
                                                </TableCell>
                                                <TableCell>
                                                    {part.promotional_price ? (
                                                        <>
                                                            <span className="text-sm text-muted-foreground line-through">
                                                                {formatCurrency(
                                                                    part.price,
                                                                )}
                                                            </span>
                                                            <span className="ml-2 font-medium text-red-600">
                                                                {formatCurrency(
                                                                    part.promotional_price,
                                                                )}
                                                            </span>
                                                        </>
                                                    ) : (
                                                        formatCurrency(
                                                            part.price,
                                                        )
                                                    )}
                                                </TableCell>
                                                <TableCell>
                                                    {part.stock_quantity}
                                                </TableCell>
                                                <TableCell>
                                                    {part.compatible_vehicles &&
                                                    part.compatible_vehicles
                                                        .length > 0 ? (
                                                        <span className="text-sm font-medium">
                                                            {
                                                                part
                                                                    .compatible_vehicles
                                                                    .length
                                                            }{' '}
                                                            veículo
                                                            {part
                                                                .compatible_vehicles
                                                                .length !== 1
                                                                ? 's'
                                                                : ''}
                                                        </span>
                                                    ) : (
                                                        <span className="text-sm text-muted-foreground">
                                                            0
                                                        </span>
                                                    )}
                                                </TableCell>
                                                <TableCell>
                                                    <Badge
                                                        className={
                                                            statusColors[
                                                                part.status as keyof typeof statusColors
                                                            ]
                                                        }
                                                    >
                                                        {statuses[
                                                            part.status as keyof typeof statuses
                                                        ] || part.status}
                                                    </Badge>
                                                </TableCell>
                                                <TableCell className="text-right">
                                                    <div className="flex justify-end space-x-2">
                                                        <Button
                                                            variant="ghost"
                                                            size="icon"
                                                            asChild
                                                        >
                                                            <Link
                                                                href={route(
                                                                    'admin.parts.show',
                                                                    {
                                                                        id: part.id,
                                                                    },
                                                                )}
                                                            >
                                                                <Eye className="h-4 w-4" />
                                                                <span className="sr-only">
                                                                    Visualizar
                                                                </span>
                                                            </Link>
                                                        </Button>
                                                        <Button
                                                            variant="ghost"
                                                            size="icon"
                                                            asChild
                                                        >
                                                            <Link
                                                                href={route(
                                                                    'admin.parts.edit',
                                                                    {
                                                                        id: part.id,
                                                                    },
                                                                )}
                                                            >
                                                                <Edit className="h-4 w-4" />
                                                                <span className="sr-only">
                                                                    Editar
                                                                </span>
                                                            </Link>
                                                        </Button>
                                                        <Button
                                                            variant="ghost"
                                                            size="icon"
                                                            className="text-red-600 hover:text-red-700"
                                                            onClick={() =>
                                                                handleDelete(
                                                                    part,
                                                                )
                                                            }
                                                        >
                                                            <Trash2 className="h-4 w-4" />
                                                            <span className="sr-only">
                                                                Excluir
                                                            </span>
                                                        </Button>
                                                    </div>
                                                </TableCell>
                                            </TableRow>
                                        ))
                                    )}
                                </TableBody>
                            </Table>
                        </div>

                        {parts.links && parts.links.length > 3 && (
                            <div className="mt-4">
                                <nav className="flex items-center justify-between">
                                    <div className="text-sm text-muted-foreground">
                                        Mostrando {parts.from} a {parts.to} de{' '}
                                        {parts.total} resultados
                                    </div>
                                    <div className="flex space-x-1">
                                        {parts.links.map(
                                            (
                                                link: {
                                                    url: string | null;
                                                    label: string;
                                                    active: boolean;
                                                },
                                                index: number,
                                            ) => (
                                                <Button
                                                    key={index}
                                                    asChild={
                                                        !link.active &&
                                                        !!link.url
                                                    }
                                                    variant={
                                                        link.active
                                                            ? 'default'
                                                            : 'outline'
                                                    }
                                                    size="sm"
                                                    disabled={
                                                        !link.url || link.active
                                                    }
                                                >
                                                    {link.url ? (
                                                        <Link
                                                            href={link.url}
                                                            dangerouslySetInnerHTML={{
                                                                __html: link.label,
                                                            }}
                                                        />
                                                    ) : (
                                                        <span
                                                            dangerouslySetInnerHTML={{
                                                                __html: link.label,
                                                            }}
                                                        />
                                                    )}
                                                </Button>
                                            ),
                                        )}
                                    </div>
                                </nav>
                            </div>
                        )}
                    </CardContent>
                </Card>
            </div>
        </AdminLayout>
    );
}
