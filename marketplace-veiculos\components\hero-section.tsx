"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { ChevronLeft, ChevronRight } from "lucide-react"

export function HeroSection() {
  const [currentSlide, setCurrentSlide] = useState(0)

  const banners = [
    {
      id: 1,
      title: "FESTIVAL DE",
      subtitle: "CARROS",
      description: "Compre seu carro em até 48x",
      offer: "Contrate o seguro Klubi e ganhe bônus de R$ 2 MIL",
      buttonText: "Ver ofertas",
      image: "/honda-civic-2022-silver-car.jpg",
      bgColor: "bg-primary",
    },
    {
      id: 2,
      title: "SUPER",
      subtitle: "MOTOS",
      description: "As melhores motos com desconto",
      offer: "Até 30% OFF em motos selecionadas",
      buttonText: "Conferir",
      image: "/yamaha-mt-07-2021-blue-motorcycle.jpg",
      bgColor: "bg-accent",
    },
  ]

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % banners.length)
  }

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + banners.length) % banners.length)
  }

  return (
    <section className="relative">
      <div className="relative h-80 md:h-96 overflow-hidden">
        <div
          className="flex transition-transform duration-500 ease-in-out h-full"
          style={{ transform: `translateX(-${currentSlide * 100}%)` }}
        >
          {banners.map((banner, index) => (
            <div key={banner.id} className={`min-w-full h-full ${banner.bgColor} relative flex items-center`}>
              <div className="container mx-auto px-4">
                <div className="flex items-center justify-between">
                  <div className="text-white max-w-lg">
                    <div className="text-sm font-medium mb-2">Parceiro</div>
                    <h1 className="text-4xl md:text-6xl font-bold mb-2">{banner.title}</h1>
                    <h2 className="text-4xl md:text-6xl font-bold mb-4">{banner.subtitle}</h2>
                    <p className="text-lg mb-2">{banner.description}</p>
                    <p className="text-sm mb-6 opacity-90">{banner.offer}</p>
                    <Button className="bg-accent hover:bg-accent/90 text-accent-foreground px-8 py-2 rounded-full">
                      {banner.buttonText}
                    </Button>
                  </div>
                  <div className="hidden md:block">
                    <img
                      src={banner.image || "/placeholder.svg"}
                      alt={banner.subtitle}
                      className="w-80 h-60 object-cover rounded-lg"
                    />
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Navigation Arrows */}
        <Button
          variant="ghost"
          size="sm"
          className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white/20 hover:bg-white/30 text-white rounded-full p-2"
          onClick={prevSlide}
        >
          <ChevronLeft className="h-6 w-6" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white/20 hover:bg-white/30 text-white rounded-full p-2"
          onClick={nextSlide}
        >
          <ChevronRight className="h-6 w-6" />
        </Button>

        {/* Dots Indicator */}
        <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
          {banners.map((_, index) => (
            <button
              key={index}
              className={`w-3 h-3 rounded-full transition-colors ${
                index === currentSlide ? "bg-white" : "bg-white/50"
              }`}
              onClick={() => setCurrentSlide(index)}
            />
          ))}
        </div>
      </div>
    </section>
  )
}
