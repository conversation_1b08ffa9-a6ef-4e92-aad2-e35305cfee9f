import { Link } from '@inertiajs/react';
import { Heart } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import { ProductVehicle, ProductPart, ProductItem } from "@/types";

export type Vehicle = ProductVehicle;
export type Part = ProductPart;
export type Product = ProductItem;

interface ProductCardProps {
  product: Product;
  onToggleFavorite?: (productId: string) => void;
  isFavorite?: boolean;
  className?: string;
}

const isVehicle = (product: Product): product is Vehicle => {
  return 'model' in product;
};

const isPart = (product: Product): product is Part => {
  return 'name' in product;
};

export default function ProductCard({ 
  product, 
  onToggleFavorite, 
  isFavorite = false,
  className 
}: ProductCardProps) {
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(price);
  };

  const getFinalPrice = () => {
    return product.promotional_price || product.price;
  };

  const getMainImage = () => {
    if (product.main_image_url) {
      return product.main_image_url;
    }
    
    // Imagens placeholder baseadas no tipo
    if (isVehicle(product)) {
      const vehicleImages = [
        '/toyota-corolla-2023-white-car.jpg',
        '/honda-civic-2022-silver-car.jpg',
        '/placeholder.jpg'
      ];
      return vehicleImages[Math.floor(Math.random() * vehicleImages.length)];
    } else {
      return '/car-suspension-kit-parts.jpg';
    }
  };

  const getTitle = () => {
    if (isVehicle(product)) {
      return `${product.brand.name} ${product.model}`;
    } else {
      return product.name;
    }
  };

  const productId = `${isVehicle(product) ? 'vehicle' : 'part'}-${product.id}`;

  return (
    <Card className={cn("group cursor-pointer hover:shadow-lg transition-shadow duration-200 border-gray-200 h-full flex flex-col max-w-xs mx-auto w-full", className)}>
      <Link href={product.url}>
        <div className="relative">
          {/* Product Image */}
          <div className="aspect-[4/3] bg-gray-100 rounded-t-lg overflow-hidden flex-shrink-0">
            <img
              src={getMainImage()}
              alt={getTitle()}
              className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
            />
          </div>

          {/* Favorite Button */}
          {onToggleFavorite && (
            <Button
              variant="ghost"
              size="icon"
              className="absolute top-2 right-2 bg-white/80 hover:bg-white shadow-sm"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                onToggleFavorite(productId);
              }}
            >
              <Heart
                className={cn(
                  "h-4 w-4",
                  isFavorite ? "fill-red-500 text-red-500" : "text-gray-600",
                )}
              />
            </Button>
          )}

          {/* Badges */}
          <div className="absolute top-2 left-2 flex flex-col gap-2">
            {product.is_featured && (
              <Badge className="bg-yellow-500 hover:bg-yellow-600 text-white">
                ⭐ Destaque
              </Badge>
            )}
            {product.promotional_price && (
              <Badge className="bg-green-500 hover:bg-green-600 text-white">
                🏷️ Oferta
              </Badge>
            )}
            {isPart(product) && product.is_original && (
              <Badge className="bg-blue-500 hover:bg-blue-600 text-white">
                ✅ Original
              </Badge>
            )}
          </div>
        </div>

        {/* Product Info */}
        <CardContent className="p-3 flex-grow flex flex-col">
          <h3 className="font-medium text-foreground text-xs mb-1 line-clamp-2 flex-grow">
            {getTitle()}
          </h3>

          {/* Price */}
          <div className="mb-2">
            <span className="text-base font-semibold text-foreground">
              {formatPrice(getFinalPrice())}
            </span>
            {product.promotional_price && (
              <span className="text-xs text-muted-foreground line-through ml-1">
                {formatPrice(product.price)}
              </span>
            )}
          </div>

          {/* Details specific to type */}
          {isVehicle(product) ? (
            // Vehicle details
            <div className="text-xs text-muted-foreground space-y-0.5">
              <div className="flex justify-between">
                <span>Ano:</span>
                <span className="font-medium">{product.year_manufacture}</span>
              </div>
              <div className="flex justify-between">
                <span>Km:</span>
                <span className="font-medium">{product.mileage.toLocaleString('pt-BR')}</span>
              </div>
              <div className="flex justify-between">
                <span>Combustível:</span>
                <span className="font-medium">{product.fuel_type}</span>
              </div>
              {product.is_negotiable && (
                <div className="text-blue-600 font-medium text-xs">
                  💰 Preço negociável
                </div>
              )}
            </div>
          ) : (
            // Part details
            <div className="text-xs text-muted-foreground space-y-0.5">
              <div className="flex justify-between">
                <span>Marca:</span>
                <span className="font-medium">{product.brand.name}</span>
              </div>
              <div className="flex justify-between">
                <span>Estoque:</span>
                <span className={`font-medium ${product.stock_quantity > 10 ? 'text-green-600' : 'text-orange-600'}`}>
                  {product.stock_quantity} unidades
                </span>
              </div>
              <div className="text-xs">
                {product.is_original ? 'Peça original' : 'Peça compatível'}
              </div>
            </div>
          )}
        </CardContent>
      </Link>
    </Card>
  );
}
