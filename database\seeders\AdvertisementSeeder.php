<?php

namespace Database\Seeders;

use App\Models\Advertisement;
use App\Models\Vehicle;
use App\Models\User;
use App\Models\Image;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class AdvertisementSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Limpar a tabela de anúncios
        Advertisement::query()->delete();

        // Obter veículos e usuários
        $vehicles = Vehicle::all();
        $users = User::all();
        
        if ($vehicles->isEmpty() || $users->isEmpty()) {
            $this->command->error('É necessário executar os seeders de Vehicle e User primeiro!');
            return;
        }

        $advertisementData = [
            [
                'title' => 'Volkswagen Golf GTI 2022 - Impecável',
                'description' => 'Golf GTI 2022 em perfeito estado de conservação. Único dono, revisões em dia, pneus novos. Carro para quem busca performance e conforto.',
                'price' => 175000.00,
                'original_price' => 185000.00,
                'location' => 'São Paulo, SP',
                'status' => 'published',
                'is_featured' => true,
                'accepts_trade' => false,
                'contact_phone' => '(11) 99999-1111',
                'contact_email' => '<EMAIL>',
            ],
            [
                'title' => 'Toyota Corolla XEI Híbrido 2023',
                'description' => 'Corolla XEI Híbrido 2023, baixa quilometragem, economia excepcional. Garantia de fábrica, manual e chave reserva.',
                'price' => 145000.00,
                'location' => 'Rio de Janeiro, RJ',
                'status' => 'published',
                'is_featured' => true,
                'accepts_trade' => true,
                'contact_phone' => '(21) 98888-2222',
                'contact_email' => '<EMAIL>',
            ],
            [
                'title' => 'Honda Civic EXL 2022 - Completo',
                'description' => 'Civic EXL 2022 completíssimo, central multimídia, bancos de couro, câmera de ré. Excelente estado.',
                'price' => 155000.00,
                'original_price' => 165000.00,
                'location' => 'Belo Horizonte, MG',
                'status' => 'published',
                'is_featured' => true,
                'accepts_trade' => false,
                'contact_phone' => '(31) 97777-3333',
                'contact_email' => '<EMAIL>',
            ],
            [
                'title' => 'Chevrolet Onix Premier 2023',
                'description' => 'Onix Premier 2023, zero quilômetro, todas as opcionais. Financiamento facilitado.',
                'price' => 89000.00,
                'location' => 'Porto Alegre, RS',
                'status' => 'published',
                'is_featured' => false,
                'accepts_trade' => true,
                'contact_phone' => '(51) 96666-4444',
                'contact_email' => '<EMAIL>',
            ],
            [
                'title' => 'Hyundai HB20 Diamond 2022',
                'description' => 'HB20 Diamond 2022, ar condicionado, direção elétrica, vidros elétricos. Muito econômico.',
                'price' => 89000.00,
                'original_price' => 95000.00,
                'location' => 'Salvador, BA',
                'status' => 'published',
                'is_featured' => false,
                'accepts_trade' => false,
                'contact_phone' => '(71) 95555-5555',
                'contact_email' => '<EMAIL>',
            ],
            [
                'title' => 'Jeep Renegade Trailhawk 2022',
                'description' => 'Renegade Trailhawk 2022, 4x4, diesel, ideal para aventuras. Excelente custo-benefício.',
                'price' => 195000.00,
                'location' => 'Brasília, DF',
                'status' => 'published',
                'is_featured' => true,
                'accepts_trade' => false,
                'contact_phone' => '(61) 94444-6666',
                'contact_email' => '<EMAIL>',
            ],
            [
                'title' => 'Honda CB 650R 2023 - Moto Esportiva',
                'description' => 'CB 650R 2023, moto esportiva de alta performance. Baixa quilometragem, revisões em dia.',
                'price' => 62000.00,
                'original_price' => 65000.00,
                'location' => 'São Paulo, SP',
                'status' => 'published',
                'is_featured' => true,
                'accepts_trade' => false,
                'contact_phone' => '(11) 93333-7777',
                'contact_email' => '<EMAIL>',
            ],
            [
                'title' => 'Yamaha MT-07 2022 - Naked',
                'description' => 'MT-07 2022, moto naked com excelente desempenho. Ideal para uso urbano e estrada.',
                'price' => 58000.00,
                'location' => 'Curitiba, PR',
                'status' => 'published',
                'is_featured' => true,
                'accepts_trade' => true,
                'contact_phone' => '(41) 92222-8888',
                'contact_email' => '<EMAIL>',
            ],
        ];

        foreach ($advertisementData as $index => $adData) {
            // Pegar um veículo aleatório
            $vehicle = $vehicles->random();
            $user = $users->random();

            $advertisement = Advertisement::create([
                'vehicle_id' => $vehicle->id,
                'user_id' => $user->id,
                'title' => $adData['title'],
                'description' => $adData['description'],
                'price' => $adData['price'],
                'location' => $adData['location'],
                'status' => $adData['status'],
                'is_featured' => $adData['is_featured'],
                'is_negotiable' => $adData['accepts_trade'],
                'contact_phone' => $adData['contact_phone'],
                'contact_email' => $adData['contact_email'],
                'views' => rand(10, 500),
                'published_at' => now(),
            ]);

            // Criar uma imagem principal para o anúncio
            Image::create([
                'imageable_type' => Advertisement::class,
                'imageable_id' => $advertisement->id,
                'path' => 'placeholder.jpg',
                'url' => '/placeholder.jpg',
                'mime_type' => 'image/jpeg',
                'size' => 1024,
                'width' => 800,
                'height' => 600,
                'alt_text' => $adData['title'],
                'is_main' => true,
                'order' => 1,
            ]);
        }

        $this->command->info(count($advertisementData) . ' anúncios criados com sucesso!');
    }
}
