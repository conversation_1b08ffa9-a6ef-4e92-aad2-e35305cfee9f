<?php

namespace App\Notifications;

use App\Models\Advertisement;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class AdvertisementRejectedNotification extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new notification instance.
     */
    public function __construct(public Advertisement $advertisement)
    {
        //
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable)
    {
        $mail = (new MailMessage)
                    ->subject('Seu anúncio foi rejeitado')
                    ->line('Seu anúncio foi rejeitado pelos seguintes motivos:')
                    ->line($this->advertisement->rejection_reason)
                    ->line('Você pode editar o anúncio e enviá-lo para revisão novamente.')
                    ->action('Editar <PERSON>cio', route('advertisements.edit', $this->advertisement));

        return $mail;
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'message' => 'Seu anúncio foi rejeitado. Verifique os detalhes para mais informações.',
            'reason' => $this->advertisement->rejection_reason,
            'link' => route('advertisements.edit', $this->advertisement),
            'advertisement_id' => $this->advertisement->id,
            'advertisement_title' => $this->advertisement->title,
        ];
    }
}
