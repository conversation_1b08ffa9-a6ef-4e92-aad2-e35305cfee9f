import { Head, usePage } from '@inertiajs/react';
import MainLayout from '../../layouts/MainLayout';

export default function Help() {
    const { categories = [] } = usePage<any>().props;

    return (
        <MainLayout categories={categories}>
            <Head title="Central de Ajuda" />

            <div className="container mx-auto px-4 py-8">
                <div className="mx-auto max-w-4xl">
                    <h1 className="mb-8 text-3xl font-bold">
                        Central de Ajuda
                    </h1>

                    <div className="grid gap-8 md:grid-cols-2">
                        <div className="rounded-lg bg-white p-6 shadow-md">
                            <h2 className="mb-4 text-xl font-semibold">
                                Como Comprar
                            </h2>
                            <ul className="space-y-2 text-gray-600">
                                <li>• Navegue pelas categorias</li>
                                <li>
                                    • Use os filtros para encontrar o que
                                    precisa
                                </li>
                                <li>• Entre em contato com o vendedor</li>
                                <li>• Negocie o preço e condições</li>
                                <li>• Finalize a compra com segurança</li>
                            </ul>
                        </div>

                        <div className="rounded-lg bg-white p-6 shadow-md">
                            <h2 className="mb-4 text-xl font-semibold">
                                Como Vender
                            </h2>
                            <ul className="space-y-2 text-gray-600">
                                <li>• Crie sua conta gratuitamente</li>
                                <li>• Cadastre seu veículo ou peça</li>
                                <li>• Adicione fotos de qualidade</li>
                                <li>• Defina um preço competitivo</li>
                                <li>• Publique seu anúncio</li>
                            </ul>
                        </div>

                        <div className="rounded-lg bg-white p-6 shadow-md">
                            <h2 className="mb-4 text-xl font-semibold">
                                Dúvidas Frequentes
                            </h2>
                            <div className="space-y-4">
                                <div>
                                    <h3 className="font-medium">
                                        Como funciona a garantia?
                                    </h3>
                                    <p className="text-sm text-gray-600">
                                        Todos os veículos possuem garantia
                                        conforme legislação vigente.
                                    </p>
                                </div>
                                <div>
                                    <h3 className="font-medium">
                                        Posso parcelar a compra?
                                    </h3>
                                    <p className="text-sm text-gray-600">
                                        Sim, oferecemos diversas opções de
                                        financiamento.
                                    </p>
                                </div>
                            </div>
                        </div>

                        <div className="rounded-lg bg-white p-6 shadow-md">
                            <h2 className="mb-4 text-xl font-semibold">
                                Contato
                            </h2>
                            <div className="space-y-2 text-gray-600">
                                <p>📞 (11) 1234-5678</p>
                                <p>📧 <EMAIL></p>
                                <p>💬 Chat online disponível</p>
                                <p>🕒 Seg-Sex: 8h às 18h</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </MainLayout>
    );
}
