<?php

namespace App\Console\Commands;

use App\Models\Advertisement;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class FixAdvertisementDates extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'advertisements:fix-dates
                            {--days=30 : Número de dias para a data de expiração a partir de hoje}
                            {--dry-run : Executar em modo de teste, sem fazer alterações reais}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Corrige as datas de publicação e expiração dos anúncios';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $days = (int) $this->option('days');
        $dryRun = $this->option('dry-run');
        $now = now();
        
        if ($days < 1) {
            $this->error('O número de dias deve ser maior que zero.');
            return 1;
        }
        
        if ($dryRun) {
            $this->info('🚧 MODO DE TESTE - Nenhuma alteração será feita no banco de dados.');
        } else {
            $this->warn('⚠️  ATENÇÃO: Esta operação irá modificar as datas dos anúncios.');
            if (!$this->confirm('Deseja continuar?', false)) {
                $this->info('Operação cancelada pelo usuário.');
                return 0;
            }
        }
        
        // 1. Anúncios publicados sem data de publicação
        $publishedWithoutDate = Advertisement::where('status', 'published')
            ->whereNull('published_at')
            ->get();
            
        $this->processPublishedWithoutDate($publishedWithoutDate, $now, $days, $dryRun);
        
        // 2. Anúncios com data de expiração nula ou no passado para status ativos
        $activeStatuses = ['published', 'approved'];
        $expiredOrNull = Advertisement::whereIn('status', $activeStatuses)
            ->where(function($query) use ($now) {
                $query->whereNull('expires_at')
                      ->orWhere('expires_at', '<=', $now);
            })
            ->get();
            
        $this->processExpiredOrNull($expiredOrNull, $now, $days, $dryRun);
        
        // 3. Anúncios com data de publicação futura
        $futurePublish = Advertisement::where('published_at', '>', $now)
            ->get();
            
        $this->processFuturePublish($futurePublish, $now, $dryRun);
        
        // 4. Anúncios com data de expiração anterior à data de publicação
        $invalidDateRange = Advertisement::whereNotNull('published_at')
            ->whereNotNull('expires_at')
            ->where('expires_at', '<=', DB::raw('published_at'))
            ->get();
            
        $this->processInvalidDateRange($invalidDateRange, $now, $days, $dryRun);
        
        $this->newLine();
        $this->info('✅ Operação concluída com sucesso!');
        
        if ($dryRun) {
            $this->info('🔍 Lembre-se de executar novamente sem --dry-run para aplicar as alterações.');
        }
        
        return 0;
    }
    
    /**
     * Processa anúncios publicados sem data de publicação
     */
    private function processPublishedWithoutDate($ads, $now, $days, $dryRun)
    {
        $count = $ads->count();
        
        if ($count === 0) {
            $this->info('✅ Nenhum anúncio publicado sem data de publicação encontrado.');
            return;
        }
        
        $this->warn("⚠️  Encontrados {$count} anúncios publicados sem data de publicação.");
        
        $bar = $this->output->createProgressBar($count);
        $bar->start();
        
        $updated = 0;
        
        foreach ($ads as $ad) {
            try {
                $publishedAt = $now->copy()->subDays(rand(1, $days));
                $expiresAt = $publishedAt->copy()->addDays($days);
                
                if (!$dryRun) {
                    $ad->published_at = $publishedAt;
                    $ad->expires_at = $expiresAt;
                    $ad->save();
                }
                
                $updated++;
                $bar->advance();
            } catch (\Exception $e) {
                $this->error("\nErro ao atualizar anúncio #{$ad->id}: " . $e->getMessage());
                Log::error("Erro ao atualizar datas do anúncio #{$ad->id}", [
                    'error' => $e->getMessage(),
                    'ad' => $ad->toArray(),
                ]);
            }
        }
        
        $bar->finish();
        $this->newLine();
        
        $action = $dryRun ? 'Seriam atualizados' : 'Foram atualizados';
        $this->info("✓ {$action} {$updated}/{$count} anúncios com data de publicação ausente.");
        
        if (!$dryRun) {
            Log::info("Anúncios publicados sem data atualizados", [
                'total' => $count,
                'updated' => $updated,
                'days' => $days,
            ]);
        }
    }
    
    /**
     * Processa anúncios com data de expiração nula ou no passado
     */
    private function processExpiredOrNull($ads, $now, $days, $dryRun)
    {
        $count = $ads->count();
        
        if ($count === 0) {
            $this->info('✅ Nenhum anúncio ativo com data de expiração inválida encontrado.');
            return;
        }
        
        $this->warn("⚠️  Encontrados {$count} anúncios ativos com data de expiração inválida.");
        
        $bar = $this->output->createProgressBar($count);
        $bar->start();
        
        $updated = 0;
        $expired = 0;
        
        foreach ($ads as $ad) {
            try {
                $shouldExpire = false;
                
                // Se não tem data de publicação, define como agora
                if (!$ad->published_at) {
                    $ad->published_at = $now;
                }
                
                // Se a data de expiração for nula ou no passado
                if (!$ad->expires_at || $ad->expires_at <= $now) {
                    // Se foi publicado há mais de 30 dias, marca como expirado
                    if ($ad->published_at <= $now->copy()->subDays($days)) {
                        if (!$dryRun) {
                            $ad->status = 'expired';
                        }
                        $expired++;
                        $shouldExpire = true;
                    } else {
                        // Caso contrário, estende a data de expiração
                        $ad->expires_at = $now->copy()->addDays($days);
                    }
                }
                
                if (!$dryRun) {
                    $ad->save();
                }
                
                $updated++;
                $bar->advance();
            } catch (\Exception $e) {
                $this->error("\nErro ao processar anúncio #{$ad->id}: " . $e->getMessage());
                Log::error("Erro ao processar datas do anúncio #{$ad->id}", [
                    'error' => $e->getMessage(),
                    'ad' => $ad->toArray(),
                ]);
            }
        }
        
        $bar->finish();
        $this->newLine();
        
        $action = $dryRun ? 'Seriam processados' : 'Foram processados';
        $this->info("✓ {$action} {$updated}/{$count} anúncios com datas de expiração inválidas.");
        
        if ($expired > 0) {
            $action = $dryRun ? 'Seria marcado' : 'Foi marcado';
            $this->info("  - {$action} {$expired} anúncios como expirados.");
        }
        
        if (!$dryRun) {
            Log::info("Anúncios com datas de expiração inválidas processados", [
                'total' => $count,
                'updated' => $updated,
                'expired' => $expired,
                'days' => $days,
            ]);
        }
    }
    
    /**
     * Processa anúncios com data de publicação no futuro
     */
    private function processFuturePublish($ads, $now, $dryRun)
    {
        $count = $ads->count();
        
        if ($count === 0) {
            $this->info('✅ Nenhum anúncio com data de publicação futura encontrado.');
            return;
        }
        
        $this->warn("⚠️  Encontrados {$count} anúncios com data de publicação no futuro.");
        
        $bar = $this->output->createProgressBar($count);
        $bar->start();
        
        $updated = 0;
        
        foreach ($ads as $ad) {
            try {
                if (!$dryRun) {
                    // Se a data de publicação for no futuro, define como agora
                    $ad->published_at = $now;
                    
                    // Se a data de expiração for nula ou anterior à data de publicação, atualiza
                    if (!$ad->expires_at || $ad->expires_at <= $ad->published_at) {
                        $ad->expires_at = $now->copy()->addDays(30);
                    }
                    
                    $ad->save();
                }
                
                $updated++;
                $bar->advance();
            } catch (\Exception $e) {
                $this->error("\nErro ao atualizar anúncio #{$ad->id}: " . $e->getMessage());
                Log::error("Erro ao corrigir data de publicação futura do anúncio #{$ad->id}", [
                    'error' => $e->getMessage(),
                    'ad' => $ad->toArray(),
                ]);
            }
        }
        
        $bar->finish();
        $this->newLine();
        
        $action = $dryRun ? 'Seriam atualizados' : 'Foram atualizados';
        $this->info("✓ {$action} {$updated}/{$count} anúncios com data de publicação futura.");
        
        if (!$dryRun) {
            Log::info("Anúncios com data de publicação futura corrigidos", [
                'total' => $count,
                'updated' => $updated,
            ]);
        }
    }
    
    /**
     * Processa anúncios com data de expiração anterior à data de publicação
     */
    private function processInvalidDateRange($ads, $now, $days, $dryRun)
    {
        $count = $ads->count();
        
        if ($count === 0) {
            $this->info('✅ Nenhum anúncio com intervalo de datas inválido encontrado.');
            return;
        }
        
        $this->warn("⚠️  Encontrados {$count} anúncios com data de expiração anterior à data de publicação.");
        
        $bar = $this->output->createProgressBar($count);
        $bar->start();
        
        $updated = 0;
        
        foreach ($ads as $ad) {
            try {
                if (!$dryRun) {
                    // Se a data de publicação for no futuro, define como agora
                    if ($ad->published_at > $now) {
                        $ad->published_at = $now;
                    }
                    
                    // Define a data de expiração como 30 dias após a data de publicação
                    $ad->expires_at = $ad->published_at->copy()->addDays($days);
                    
                    $ad->save();
                }
                
                $updated++;
                $bar->advance();
            } catch (\Exception $e) {
                $this->error("\nErro ao atualizar anúncio #{$ad->id}: " . $e->getMessage());
                Log::error("Erro ao corrigir intervalo de datas do anúncio #{$ad->id}", [
                    'error' => $e->getMessage(),
                    'ad' => $ad->toArray(),
                ]);
            }
        }
        
        $bar->finish();
        $this->newLine();
        
        $action = $dryRun ? 'Seriam corrigidos' : 'Foram corrigidos';
        $this->info("✓ {$action} {$updated}/{$count} anúncios com intervalo de datas inválido.");
        
        if (!$dryRun) {
            Log::info("Anúncios com intervalo de datas inválido corrigidos", [
                'total' => $count,
                'updated' => $updated,
                'days' => $days,
            ]);
        }
    }
}
