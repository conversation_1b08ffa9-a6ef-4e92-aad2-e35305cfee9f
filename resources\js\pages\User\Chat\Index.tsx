import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import MainLayout from '@/layouts/MainLayout';
import { PageProps } from '@/types';
import { Head, Link } from '@inertiajs/react';
import { MessageCircle, Search, Trash2, User } from 'lucide-react';
import { useState } from 'react';

interface Chat {
    id: number;
    advertisement: {
        id: number;
        title: string;
        price: number;
        vehicle: {
            brand: {
                name: string;
            };
        };
        featured_image?: {
            url: string;
        };
    };
    buyer: {
        id: number;
        name: string;
        avatar?: string;
    };
    seller: {
        id: number;
        name: string;
        avatar?: string;
    };
    last_message?: {
        id: number;
        message: string;
        user_id: number;
        created_at: string;
        read_at?: string;
    };
    unread_messages_count: number;
    updated_at: string;
}

interface ChatIndexProps extends PageProps {
    chats: {
        data: Chat[];
        links: any;
        meta: any;
    };
}

export default function ChatIndex({ chats, auth }: ChatIndexProps) {
    const [searchQuery, setSearchQuery] = useState('');

    const filteredChats = chats.data.filter(
        (chat) =>
            chat.advertisement.title
                .toLowerCase()
                .includes(searchQuery.toLowerCase()) ||
            chat.buyer.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
            chat.seller.name.toLowerCase().includes(searchQuery.toLowerCase()),
    );

    const formatTime = (dateString: string) => {
        const date = new Date(dateString);
        const now = new Date();
        const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

        if (diffInHours < 24) {
            return date.toLocaleTimeString('pt-BR', {
                hour: '2-digit',
                minute: '2-digit',
            });
        } else if (diffInHours < 168) {
            // 7 days
            return date.toLocaleDateString('pt-BR', {
                weekday: 'short',
            });
        } else {
            return date.toLocaleDateString('pt-BR', {
                day: '2-digit',
                month: '2-digit',
            });
        }
    };

    const getOtherParticipant = (chat: Chat, currentUserId: number) => {
        return chat.buyer.id === currentUserId ? chat.seller : chat.buyer;
    };

    return (
        <MainLayout>
            <Head title="Mensagens" />

            <div className="min-h-screen bg-gray-50">
                <div className="container mx-auto px-4 py-6">
                    {/* Header */}
                    <div className="mb-6">
                        <h1 className="text-3xl font-bold">Mensagens</h1>
                        <p className="text-gray-600">
                            Gerencie suas conversas sobre anúncios
                        </p>
                    </div>

                    <div className="grid grid-cols-1 gap-6 lg:grid-cols-4">
                        {/* Sidebar com filtros */}
                        <div className="lg:col-span-1">
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <Search className="h-5 w-5" />
                                        Buscar conversas
                                    </CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <Input
                                        placeholder="Buscar por anúncio ou pessoa..."
                                        value={searchQuery}
                                        onChange={(e) =>
                                            setSearchQuery(e.target.value)
                                        }
                                    />
                                </CardContent>
                            </Card>

                            <Card className="mt-4">
                                <CardHeader>
                                    <CardTitle>Estatísticas</CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-2">
                                    <div className="flex justify-between">
                                        <span className="text-sm text-gray-600">
                                            Total de conversas:
                                        </span>
                                        <span className="font-medium">
                                            {chats.data.length}
                                        </span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span className="text-sm text-gray-600">
                                            Não lidas:
                                        </span>
                                        <span className="font-medium text-orange-600">
                                            {
                                                chats.data.filter(
                                                    (chat) =>
                                                        chat.unread_messages_count >
                                                        0,
                                                ).length
                                            }
                                        </span>
                                    </div>
                                </CardContent>
                            </Card>
                        </div>

                        {/* Lista de chats */}
                        <div className="lg:col-span-3">
                            {filteredChats.length === 0 ? (
                                <Card>
                                    <CardContent className="py-12 text-center">
                                        <MessageCircle className="mx-auto mb-4 h-12 w-12 text-gray-400" />
                                        <h3 className="mb-2 text-lg font-medium text-gray-900">
                                            {searchQuery
                                                ? 'Nenhuma conversa encontrada'
                                                : 'Nenhuma conversa ainda'}
                                        </h3>
                                        <p className="mb-4 text-gray-600">
                                            {searchQuery
                                                ? 'Tente ajustar os termos de busca.'
                                                : 'Quando você entrar em contato com vendedores, suas conversas aparecerão aqui.'}
                                        </p>
                                        {!searchQuery && (
                                            <Link href="/anuncios">
                                                <Button>
                                                    Explorar anúncios
                                                </Button>
                                            </Link>
                                        )}
                                    </CardContent>
                                </Card>
                            ) : (
                                <div className="space-y-4">
                                    {filteredChats.map((chat) => {
                                        const otherParticipant =
                                            getOtherParticipant(
                                                chat,
                                                auth.user?.id || 0,
                                            );
                                        const hasUnread =
                                            chat.unread_messages_count > 0;

                                        return (
                                            <Card
                                                key={chat.id}
                                                className={`transition-shadow hover:shadow-md ${hasUnread ? 'border-orange-200 bg-orange-50' : ''}`}
                                            >
                                                <CardContent className="p-4">
                                                    <div className="flex items-start gap-4">
                                                        {/* Imagem do anúncio */}
                                                        <div className="flex-shrink-0">
                                                            <img
                                                                src={
                                                                    chat
                                                                        .advertisement
                                                                        .featured_image
                                                                        ?.url ||
                                                                    '/placeholder-car.jpg'
                                                                }
                                                                alt={
                                                                    chat
                                                                        .advertisement
                                                                        .title
                                                                }
                                                                className="h-16 w-16 rounded-lg object-cover"
                                                            />
                                                        </div>

                                                        {/* Conteúdo principal */}
                                                        <div className="min-w-0 flex-1">
                                                            <div className="flex items-start justify-between">
                                                                <div className="flex-1">
                                                                    <h3 className="truncate font-medium text-gray-900">
                                                                        {
                                                                            chat
                                                                                .advertisement
                                                                                .title
                                                                        }
                                                                    </h3>
                                                                    <p className="text-sm text-gray-600">
                                                                        {
                                                                            chat
                                                                                .advertisement
                                                                                .vehicle
                                                                                .brand
                                                                                .name
                                                                        }{' '}
                                                                        • R${' '}
                                                                        {chat.advertisement.price.toLocaleString(
                                                                            'pt-BR',
                                                                        )}
                                                                    </p>

                                                                    <div className="mt-2 flex items-center gap-2">
                                                                        <div className="flex items-center gap-1">
                                                                            {otherParticipant.avatar ? (
                                                                                <img
                                                                                    src={
                                                                                        otherParticipant.avatar
                                                                                    }
                                                                                    alt={
                                                                                        otherParticipant.name
                                                                                    }
                                                                                    className="h-5 w-5 rounded-full"
                                                                                />
                                                                            ) : (
                                                                                <User className="h-5 w-5 text-gray-400" />
                                                                            )}
                                                                            <span className="text-sm text-gray-600">
                                                                                {
                                                                                    otherParticipant.name
                                                                                }
                                                                            </span>
                                                                        </div>

                                                                        {hasUnread && (
                                                                            <span className="rounded-full bg-orange-500 px-2 py-1 text-xs text-white">
                                                                                {
                                                                                    chat.unread_messages_count
                                                                                }
                                                                            </span>
                                                                        )}
                                                                    </div>

                                                                    {chat.last_message && (
                                                                        <p
                                                                            className={`mt-2 truncate text-sm ${hasUnread ? 'font-medium text-gray-900' : 'text-gray-600'}`}
                                                                        >
                                                                            {
                                                                                chat
                                                                                    .last_message
                                                                                    .message
                                                                            }
                                                                        </p>
                                                                    )}
                                                                </div>

                                                                <div className="ml-4 flex flex-col items-end gap-2">
                                                                    <span className="text-xs text-gray-500">
                                                                        {formatTime(
                                                                            chat.updated_at,
                                                                        )}
                                                                    </span>

                                                                    <div className="flex gap-1">
                                                                        <Link
                                                                            href={`/minha-conta/chat/${chat.id}`}
                                                                        >
                                                                            <Button
                                                                                size="sm"
                                                                                variant="outline"
                                                                            >
                                                                                <MessageCircle className="h-4 w-4" />
                                                                            </Button>
                                                                        </Link>

                                                                        <Button
                                                                            size="sm"
                                                                            variant="outline"
                                                                            className="text-red-600 hover:text-red-700"
                                                                        >
                                                                            <Trash2 className="h-4 w-4" />
                                                                        </Button>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </CardContent>
                                            </Card>
                                        );
                                    })}
                                </div>
                            )}

                            {/* Paginação */}
                            {chats.links && (
                                <div className="mt-6 flex justify-center">
                                    {/* Implementar paginação aqui se necessário */}
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            </div>
        </MainLayout>
    );
}
