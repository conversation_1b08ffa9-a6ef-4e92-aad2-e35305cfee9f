import { cn } from '@/lib/utils';
import { useState } from 'react';
import { ImageIcon } from 'lucide-react';

interface ImageSize {
    url: string;
    width: number;
    height: number;
    size: number;
}

interface ImageSizes {
    original?: ImageSize;
    large?: ImageSize;
    medium?: ImageSize;
    thumb?: ImageSize;
}

interface ResponsiveImageProps {
    src?: string;
    sizes?: ImageSizes;
    alt: string;
    className?: string;
    fallback?: string;
    loading?: 'lazy' | 'eager';
    priority?: 'thumb' | 'medium' | 'large' | 'original';
    aspectRatio?: 'square' | 'video' | 'auto';
    objectFit?: 'cover' | 'contain' | 'fill' | 'scale-down';
    placeholder?: boolean;
    onLoad?: () => void;
    onError?: () => void;
}

export function ResponsiveImage({
    src,
    sizes,
    alt,
    className,
    fallback,
    loading = 'lazy',
    priority = 'medium',
    aspectRatio = 'auto',
    objectFit = 'cover',
    placeholder = true,
    onLoad,
    onError,
}: ResponsiveImageProps) {
    const [isLoading, setIsLoading] = useState(true);
    const [hasError, setHasError] = useState(false);

    // Determinar qual imagem usar baseado na prioridade
    const getImageUrl = () => {
        if (src) return src;
        if (!sizes) return fallback;

        const priorityOrder = {
            thumb: ['thumb', 'medium', 'large', 'original'],
            medium: ['medium', 'large', 'thumb', 'original'],
            large: ['large', 'medium', 'original', 'thumb'],
            original: ['original', 'large', 'medium', 'thumb'],
        };

        const order = priorityOrder[priority];
        for (const size of order) {
            if (sizes[size as keyof ImageSizes]?.url) {
                return sizes[size as keyof ImageSizes]!.url;
            }
        }

        return fallback;
    };

    // Gerar srcSet para imagens responsivas
    const generateSrcSet = () => {
        if (!sizes) return undefined;

        const srcSetEntries: string[] = [];
        
        Object.entries(sizes).forEach(([sizeName, sizeData]) => {
            if (sizeData?.url && sizeData?.width) {
                srcSetEntries.push(`${sizeData.url} ${sizeData.width}w`);
            }
        });

        return srcSetEntries.length > 0 ? srcSetEntries.join(', ') : undefined;
    };

    // Gerar sizes attribute
    const generateSizes = () => {
        if (!sizes) return undefined;

        // Configuração padrão baseada em breakpoints comuns
        return '(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw';
    };

    const imageUrl = getImageUrl();
    const srcSet = generateSrcSet();
    const sizesAttr = generateSizes();

    const handleLoad = () => {
        setIsLoading(false);
        onLoad?.();
    };

    const handleError = () => {
        setIsLoading(false);
        setHasError(true);
        onError?.();
    };

    const aspectRatioClasses = {
        square: 'aspect-square',
        video: 'aspect-video',
        auto: '',
    };

    const objectFitClasses = {
        cover: 'object-cover',
        contain: 'object-contain',
        fill: 'object-fill',
        'scale-down': 'object-scale-down',
    };

    if (!imageUrl || hasError) {
        return (
            <div
                className={cn(
                    'flex items-center justify-center bg-muted text-muted-foreground',
                    aspectRatioClasses[aspectRatio],
                    className,
                )}
            >
                <ImageIcon className="h-8 w-8" />
                {alt && <span className="sr-only">{alt}</span>}
            </div>
        );
    }

    return (
        <div className={cn('relative overflow-hidden', aspectRatioClasses[aspectRatio], className)}>
            {/* Placeholder durante carregamento */}
            {isLoading && placeholder && (
                <div className="absolute inset-0 flex items-center justify-center bg-muted animate-pulse">
                    <ImageIcon className="h-8 w-8 text-muted-foreground" />
                </div>
            )}

            {/* Imagem principal */}
            <img
                src={imageUrl}
                srcSet={srcSet}
                sizes={sizesAttr}
                alt={alt}
                loading={loading}
                className={cn(
                    'w-full h-full transition-opacity duration-300',
                    objectFitClasses[objectFit],
                    isLoading ? 'opacity-0' : 'opacity-100',
                )}
                onLoad={handleLoad}
                onError={handleError}
            />
        </div>
    );
}

// Componente específico para avatares
export function AvatarImage({
    src,
    sizes,
    alt,
    className,
    size = 'medium',
}: {
    src?: string;
    sizes?: ImageSizes;
    alt: string;
    className?: string;
    size?: 'small' | 'medium' | 'large';
}) {
    const sizeClasses = {
        small: 'h-8 w-8',
        medium: 'h-12 w-12',
        large: 'h-16 w-16',
    };

    const priority = size === 'small' ? 'thumb' : size === 'large' ? 'large' : 'medium';

    return (
        <ResponsiveImage
            src={src}
            sizes={sizes}
            alt={alt}
            className={cn('rounded-full', sizeClasses[size], className)}
            aspectRatio="square"
            objectFit="cover"
            priority={priority}
        />
    );
}

// Componente para galeria de imagens
export function GalleryImage({
    src,
    sizes,
    alt,
    className,
    onClick,
}: {
    src?: string;
    sizes?: ImageSizes;
    alt: string;
    className?: string;
    onClick?: () => void;
}) {
    return (
        <div
            className={cn(
                'cursor-pointer transition-transform hover:scale-105',
                onClick && 'cursor-pointer',
                className,
            )}
            onClick={onClick}
        >
            <ResponsiveImage
                src={src}
                sizes={sizes}
                alt={alt}
                className="rounded-lg"
                aspectRatio="square"
                objectFit="cover"
                priority="medium"
            />
        </div>
    );
}

// Componente para imagem de produto/veículo
export function ProductImage({
    src,
    sizes,
    alt,
    className,
    featured = false,
}: {
    src?: string;
    sizes?: ImageSizes;
    alt: string;
    className?: string;
    featured?: boolean;
}) {
    return (
        <div className={cn('relative', className)}>
            <ResponsiveImage
                src={src}
                sizes={sizes}
                alt={alt}
                className="rounded-lg"
                aspectRatio="video"
                objectFit="cover"
                priority={featured ? 'large' : 'medium'}
            />
            {featured && (
                <div className="absolute top-2 left-2 bg-primary text-primary-foreground px-2 py-1 rounded text-xs font-medium">
                    Principal
                </div>
            )}
        </div>
    );
}
