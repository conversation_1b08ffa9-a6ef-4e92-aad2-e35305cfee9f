import { Head, Link, router } from '@inertiajs/react';
import AdminLayout from '@/components/Layout/AdminLayout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Search, Plus, Filter, Edit, Eye, X, Clock, AlertCircle, CheckCircle, Zap, Tag } from 'lucide-react';
import { formatCurrency } from '@/lib/utils';
import { useState } from 'react';

interface Brand {
  id: number;
  name: string;
}

interface Vehicle {
  id: number;
  brand: Brand;
  model: string;
  year_manufacture: number;
}

interface Advertisement {
  id: number;
  title: string;
  price: number;
  is_negotiable: boolean;
  status: keyof typeof statusVariant;
  status_label: string;
  featured_image_url: string;
  created_at: string;
  updated_at: string;
  user: {
    name: string;
  };
  vehicle: Vehicle;
}

interface PaginationLink {
  url: string | null;
  label: string;
  active: boolean;
}

interface PaginationData<T> {
  current_page: number;
  data: T[];
  first_page_url: string;
  from: number;
  last_page: number;
  last_page_url: string;
  links: PaginationLink[];
  next_page_url: string | null;
  path: string;
  per_page: number;
  prev_page_url: string | null;
  to: number;
  total: number;
}

interface Filters {
  search?: string;
  status?: string;
}

interface AdvertisementsIndexProps {
  advertisements: PaginationData<Advertisement>;
  filters: Filters;
  statuses: Record<string, string>;
}

import { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from '@/components/ui/pagination';

const statusVariant = {
  draft: 'bg-gray-100 text-gray-800',
  pending_review: 'bg-yellow-100 text-yellow-800',
  approved: 'bg-blue-100 text-blue-800',
  rejected: 'bg-red-100 text-red-800',
  published: 'bg-green-100 text-green-800',
  expired: 'bg-gray-100 text-gray-800',
  sold: 'bg-purple-100 text-purple-800',
};

const statusIcons = {
  draft: <Clock className="h-4 w-4" />,
  pending_review: <AlertCircle className="h-4 w-4" />,
  approved: <CheckCircle className="h-4 w-4" />,
  rejected: <X className="h-4 w-4" />,
  published: <Zap className="h-4 w-4" />,
  expired: <Clock className="h-4 w-4" />,
  sold: <Tag className="h-4 w-4" />,
};

export default function AdvertisementsIndex({ advertisements, filters, statuses }: AdvertisementsIndexProps) {
  const [search, setSearch] = useState<string>(filters.search || '');
  const [status, setStatus] = useState<string>(filters.status || '');
  const [showFilters, setShowFilters] = useState(false);

  const handleFilter = () => {
    const params: Record<string, string> = {};
    
    if (search) params.search = search;
    if (status) params.status = status;
    
    router.get(route('admin.advertisements.index'), params, {
      preserveState: true,
      preserveScroll: true,
    });
  };

  const resetFilters = () => {
    setSearch('');
    setStatus('');
    router.get(route('admin.advertisements.index'), {}, {
      preserveState: true,
      preserveScroll: true,
    });
  };

  return (
    <AdminLayout>
      <Head title="Gerenciar Anúncios" />
      
      <div className="space-y-6">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold tracking-tight">Gerenciar Anúncios</h1>
            <p className="text-muted-foreground">Visualize e gerencie todos os anúncios do sistema</p>
          </div>
          <div>
            <Link href={route('admin.advertisements.create')}>
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                Novo Anúncio
              </Button>
            </Link>
          </div>
        </div>

        <div className="space-y-4">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <Input
                placeholder="Pesquisar anúncios..."
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                onKeyDown={(e) => e.key === 'Enter' && handleFilter()}
              />
            </div>
            <div className="flex items-center gap-2">
              <Button variant="outline" onClick={handleFilter}>
                <Search className="mr-2 h-4 w-4" />
                Filtrar
              </Button>
              <Button variant="outline" onClick={() => setShowFilters(!showFilters)}>
                <Filter className="mr-2 h-4 w-4" />
                Filtros
              </Button>
              {(search || status) && (
                <Button variant="ghost" onClick={resetFilters}>
                  Limpar Filtros
                </Button>
              )}
            </div>
          </div>

          {showFilters && (
            <div className="bg-muted/50 p-4 rounded-lg space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-1">Status</label>
                  <Select value={status} onValueChange={setStatus}>
                    <SelectTrigger>
                      <SelectValue placeholder="Selecione um status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">Todos os Status</SelectItem>
                      {Object.entries(statuses).map(([key, label]) => (
                        <SelectItem key={key} value={key}>
                          {label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div className="flex justify-end">
                <Button onClick={handleFilter}>
                  Aplicar Filtros
                </Button>
              </div>
            </div>
          )}
        </div>

        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Anúncio</TableHead>
                <TableHead>Veículo</TableHead>
                <TableHead>Valor</TableHead>
                <TableHead>Status</TableHead>
                <TableHead className="text-right">Ações</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {advertisements.data.length > 0 ? (
                advertisements.data.map((advertisement: Advertisement) => (
                  <TableRow key={advertisement.id}>
                    <TableCell className="font-medium">
                      <div className="flex items-center gap-3">
                        <div className="h-10 w-10 rounded-md bg-muted overflow-hidden">
                          {advertisement.featured_image_url && (
                            <img
                              src={advertisement.featured_image_url}
                              alt={advertisement.title}
                              className="h-full w-full object-cover"
                            />
                          )}
                        </div>
                        <div>
                          <div className="font-medium">{advertisement.title}</div>
                          <div className="text-xs text-muted-foreground">
                            {new Date(advertisement.created_at).toLocaleDateString('pt-BR')}
                          </div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="font-medium">
                        {advertisement.vehicle?.brand?.name} {advertisement.vehicle?.model}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        {advertisement.vehicle?.year_manufacture}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="font-medium">{formatCurrency(advertisement.price)}</div>
                      {advertisement.is_negotiable && (
                        <div className="text-xs text-muted-foreground">Preço Negociável</div>
                      )}
                    </TableCell>
                    <TableCell>
                      <Badge className={statusVariant[advertisement.status]}>
                        {statusIcons[advertisement.status as keyof typeof statusIcons]}
                        {advertisement.status_label}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end gap-2">
                        <Link href={route('admin.advertisements.show', { advertisement: advertisement.id })}>
                          <Button variant="ghost" size="icon">
                            <Eye className="h-4 w-4" />
                            <span className="sr-only">Visualizar</span>
                          </Button>
                        </Link>
                        <Link href={route('admin.advertisements.edit', { advertisement: advertisement.id })}>
                          <Button variant="ghost" size="icon">
                            <Edit className="h-4 w-4" />
                            <span className="sr-only">Editar</span>
                          </Button>
                        </Link>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={5} className="text-center py-8 text-muted-foreground">
                    Nenhum anúncio encontrado.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>

        {advertisements.data.length > 0 && advertisements.links && (
          <Pagination>
            <PaginationContent>
              {advertisements.links.map((link: PaginationLink, index: number) => {
                if (link.url === null) return null;
                
                const isActive = link.active;
                const isPrevious = link.label.includes('Previous');
                const isNext = link.label.includes('Next');
                
                return (
                  <PaginationItem key={index}>
                    {isPrevious ? (
                      <PaginationPrevious 
                        href={link.url} 
                        className={!link.url ? 'pointer-events-none opacity-50' : ''}
                      />
                    ) : isNext ? (
                      <PaginationNext 
                        href={link.url} 
                        className={!link.url ? 'pointer-events-none opacity-50' : ''}
                      />
                    ) : (
                      <PaginationLink 
                        href={link.url} 
                        isActive={isActive}
                        className={!link.url ? 'pointer-events-none opacity-50' : ''}
                      >
                        {link.label}
                      </PaginationLink>
                    )}
                  </PaginationItem>
                );
              })}
            </PaginationContent>
          </Pagination>
        )}
      </div>
    </AdminLayout>
  );
}
