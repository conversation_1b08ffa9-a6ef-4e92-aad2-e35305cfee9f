<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Category;
use Illuminate\Support\Str;

class CategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            [
                'name' => 'Carros',
                'slug' => 'carros',
                'description' => 'Encontre o carro ideal para você',
                'icon' => 'car',
                'order' => 1,
                'is_active' => true,
            ],
            [
                'name' => 'Motos',
                'slug' => 'motos',
                'description' => 'Motos e scooters de todas as marcas',
                'icon' => 'bike',
                'order' => 2,
                'is_active' => true,
            ],
            [
                'name' => 'Peças',
                'slug' => 'pecas',
                'description' => 'Peças originais e compatíveis',
                'icon' => 'wrench',
                'order' => 3,
                'is_active' => true,
            ],
            [
                'name' => 'Caminhões',
                'slug' => 'caminhoes',
                'description' => 'Veículos comerciais e utilitários',
                'icon' => 'truck',
                'order' => 4,
                'is_active' => true,
            ],
            [
                'name' => 'Elétricos',
                'slug' => 'eletricos',
                'description' => 'Veículos elétricos e híbridos',
                'icon' => 'zap',
                'order' => 5,
                'is_active' => true,
            ],
            [
                'name' => 'Seguros',
                'slug' => 'seguros',
                'description' => 'Proteja seu veículo',
                'icon' => 'shield',
                'order' => 6,
                'is_active' => true,
            ],
        ];

        foreach ($categories as $category) {
            Category::firstOrCreate(
                ['slug' => $category['slug']],
                $category
            );
        }
    }
}
