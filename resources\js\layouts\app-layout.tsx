import AppLayoutTemplate from '@/layouts/app/app-sidebar-layout';
import { type BreadcrumbItem, type Category } from '@/types';
import { type ReactNode } from 'react';

interface AppLayoutProps {
    children: ReactNode;
    breadcrumbs?: BreadcrumbItem[];
    categories?: Category[];
}

export default function AppLayout({ 
    children, 
    breadcrumbs, 
    categories = []
}: AppLayoutProps) {
    return (
        <AppLayoutTemplate breadcrumbs={breadcrumbs} categories={categories}>
            {children}
        </AppLayoutTemplate>
    );
}
