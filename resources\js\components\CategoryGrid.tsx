import { Card, CardContent } from '@/components/ui/card';
import { Link } from '@inertiajs/react';

interface Category {
    id: number;
    name: string;
    slug: string;
    description: string;
    icon?: string;
    image?: string;
    url: string;
    vehicles_count?: number;
    parts_count?: number;
}

interface CategoryGridProps {
    categories: Category[];
    showCounts?: boolean;
}

export default function CategoryGrid({
    categories,
    showCounts = false,
}: CategoryGridProps) {
    const getCategoryIcon = (icon: string | undefined) => {
        const iconMap: Record<string, string> = {
            car: '🚗',
            bike: '🏍️',
            wrench: '🔧',
            truck: '🚚',
            zap: '⚡',
            shield: '🛡️',
        };

        return iconMap[icon || ''] || '📁';
    };

    const getCategoryImage = (category: Category) => {
        if (category.image) {
            return `/storage/${category.image}`;
        }

        // Imagens placeholder baseadas na categoria
        const imageMap: Record<string, string> = {
            carros: '/placeholder.jpg',
            motos: '/placeholder.jpg',
            pecas: '/placeholder.jpg',
            caminhoes: '/placeholder.jpg',
            eletricos: '/placeholder.jpg',
            seguros: '/placeholder.jpg',
        };

        return imageMap[category.slug] || '/placeholder.jpg';
    };

    return (
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
            {categories.map((category) => (
                <Link
                    key={category.id}
                    href={`/pesquisar?category_id=${category.id}`}
                >
                    <Card className="group cursor-pointer overflow-hidden transition-all duration-300 hover:shadow-lg">
                        <div className="relative aspect-video overflow-hidden bg-gray-100">
                            <img
                                src={getCategoryImage(category)}
                                alt={category.name}
                                className="h-full w-full object-cover transition-transform duration-300 group-hover:scale-105"
                            />
                            <div className="bg-opacity-0 group-hover:bg-opacity-20 absolute inset-0 bg-black transition-all duration-300" />
                            <div className="absolute top-4 left-4">
                                <div className="flex h-12 w-12 items-center justify-center rounded-full bg-white shadow-lg">
                                    <span className="text-2xl">
                                        {getCategoryIcon(category.icon)}
                                    </span>
                                </div>
                            </div>
                        </div>
                        <CardContent className="p-4">
                            <h3 className="mb-2 text-lg font-semibold text-gray-900">
                                {category.name}
                            </h3>
                            <p className="mb-3 line-clamp-2 text-sm text-gray-600">
                                {category.description}
                            </p>

                            {showCounts && (
                                <div className="flex justify-between text-sm text-gray-500">
                                    {category.vehicles_count !== undefined && (
                                        <span>
                                            {category.vehicles_count} veículos
                                        </span>
                                    )}
                                    {category.parts_count !== undefined && (
                                        <span>
                                            {category.parts_count} peças
                                        </span>
                                    )}
                                </div>
                            )}

                            <div className="mt-4">
                                <span className="inline-flex items-center text-sm font-medium text-blue-600 hover:text-blue-800">
                                    Ver mais
                                    <svg
                                        className="ml-1 h-4 w-4"
                                        fill="none"
                                        stroke="currentColor"
                                        viewBox="0 0 24 24"
                                    >
                                        <path
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                            strokeWidth={2}
                                            d="M9 5l7 7-7 7"
                                        />
                                    </svg>
                                </span>
                            </div>
                        </CardContent>
                    </Card>
                </Link>
            ))}
        </div>
    );
}
