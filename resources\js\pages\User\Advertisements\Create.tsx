import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import MainLayout from '@/layouts/MainLayout';
import { PageProps } from '@/types';
import { Head, Link, useForm } from '@inertiajs/react';
import { ArrowLeft, Upload, X } from 'lucide-react';
import { useState } from 'react';

interface Brand {
    id: number;
    name: string;
}

interface Category {
    id: number;
    name: string;
}

interface CreateAdvertisementProps extends PageProps {
    brands: Brand[];
    categories: Category[];
}

export default function CreateAdvertisement({
    brands,
    categories,
}: CreateAdvertisementProps) {
    const [selectedImages, setSelectedImages] = useState<File[]>([]);
    const [featuredImageIndex, setFeaturedImageIndex] = useState(0);

    const { data, setData, post, processing, errors } = useForm({
        title: '',
        description: '',
        price: '',
        location: '',
        contact_phone: '',
        vehicle: {
            brand_id: '',
            category_id: '',
            model: '',
            year: '',
            mileage: '',
            fuel_type: '',
            transmission: '',
            condition: '',
            color: '',
            doors: '',
            engine_size: '',
        },
        images: [] as File[],
        featured_image_index: 0,
    });

    const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
        const files = Array.from(e.target.files || []);
        if (files.length + selectedImages.length > 10) {
            alert('Máximo de 10 imagens permitidas');
            return;
        }
        
        const newImages = [...selectedImages, ...files];
        setSelectedImages(newImages);
        setData('images', newImages);
    };

    const removeImage = (index: number) => {
        const newImages = selectedImages.filter((_, i) => i !== index);
        setSelectedImages(newImages);
        setData('images', newImages);
        
        // Adjust featured image index if necessary
        if (featuredImageIndex >= newImages.length) {
            const newFeaturedIndex = Math.max(0, newImages.length - 1);
            setFeaturedImageIndex(newFeaturedIndex);
            setData('featured_image_index', newFeaturedIndex);
        }
    };

    const setFeaturedImage = (index: number) => {
        setFeaturedImageIndex(index);
        setData('featured_image_index', index);
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        post('/minha-conta/anuncios');
    };

    const currentYear = new Date().getFullYear();

    return (
        <MainLayout>
            <Head title="Criar Anúncio" />
            
            <div className="min-h-screen bg-gray-50">
                <div className="container mx-auto px-4 py-6">
                    {/* Header */}
                    <div className="mb-6">
                        <Link href="/minha-conta/anuncios">
                            <Button variant="ghost" className="mb-4">
                                <ArrowLeft className="mr-2 h-4 w-4" />
                                Voltar para meus anúncios
                            </Button>
                        </Link>
                        
                        <h1 className="text-3xl font-bold">Criar Novo Anúncio</h1>
                        <p className="text-gray-600">
                            Preencha as informações do seu veículo para criar um anúncio
                        </p>
                    </div>

                    <form onSubmit={handleSubmit} className="space-y-6">
                        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                            {/* Coluna principal */}
                            <div className="lg:col-span-2 space-y-6">
                                {/* Informações básicas */}
                                <Card>
                                    <CardHeader>
                                        <CardTitle>Informações Básicas</CardTitle>
                                    </CardHeader>
                                    <CardContent className="space-y-4">
                                        <div>
                                            <Label htmlFor="title">Título do anúncio *</Label>
                                            <Input
                                                id="title"
                                                value={data.title}
                                                onChange={(e) => setData('title', e.target.value)}
                                                placeholder="Ex: Honda Civic 2020 LX CVT"
                                                className={errors.title ? 'border-red-500' : ''}
                                            />
                                            {errors.title && (
                                                <p className="text-red-500 text-sm mt-1">{errors.title}</p>
                                            )}
                                        </div>

                                        <div>
                                            <Label htmlFor="description">Descrição *</Label>
                                            <Textarea
                                                id="description"
                                                value={data.description}
                                                onChange={(e) => setData('description', e.target.value)}
                                                placeholder="Descreva seu veículo, incluindo características especiais, histórico de manutenção, etc."
                                                rows={6}
                                                className={errors.description ? 'border-red-500' : ''}
                                            />
                                            {errors.description && (
                                                <p className="text-red-500 text-sm mt-1">{errors.description}</p>
                                            )}
                                        </div>

                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                            <div>
                                                <Label htmlFor="price">Preço *</Label>
                                                <Input
                                                    id="price"
                                                    type="number"
                                                    value={data.price}
                                                    onChange={(e) => setData('price', e.target.value)}
                                                    placeholder="50000"
                                                    className={errors.price ? 'border-red-500' : ''}
                                                />
                                                {errors.price && (
                                                    <p className="text-red-500 text-sm mt-1">{errors.price}</p>
                                                )}
                                            </div>

                                            <div>
                                                <Label htmlFor="location">Localização *</Label>
                                                <Input
                                                    id="location"
                                                    value={data.location}
                                                    onChange={(e) => setData('location', e.target.value)}
                                                    placeholder="São Paulo, SP"
                                                    className={errors.location ? 'border-red-500' : ''}
                                                />
                                                {errors.location && (
                                                    <p className="text-red-500 text-sm mt-1">{errors.location}</p>
                                                )}
                                            </div>
                                        </div>

                                        <div>
                                            <Label htmlFor="contact_phone">Telefone de contato</Label>
                                            <Input
                                                id="contact_phone"
                                                value={data.contact_phone}
                                                onChange={(e) => setData('contact_phone', e.target.value)}
                                                placeholder="(11) 99999-9999"
                                                className={errors.contact_phone ? 'border-red-500' : ''}
                                            />
                                            {errors.contact_phone && (
                                                <p className="text-red-500 text-sm mt-1">{errors.contact_phone}</p>
                                            )}
                                        </div>
                                    </CardContent>
                                </Card>

                                {/* Informações do veículo */}
                                <Card>
                                    <CardHeader>
                                        <CardTitle>Informações do Veículo</CardTitle>
                                    </CardHeader>
                                    <CardContent className="space-y-4">
                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                            <div>
                                                <Label htmlFor="brand">Marca *</Label>
                                                <Select
                                                    value={data.vehicle.brand_id}
                                                    onValueChange={(value) => setData('vehicle', { ...data.vehicle, brand_id: value })}
                                                >
                                                    <SelectTrigger className={errors['vehicle.brand_id'] ? 'border-red-500' : ''}>
                                                        <SelectValue placeholder="Selecione a marca" />
                                                    </SelectTrigger>
                                                    <SelectContent>
                                                        {brands.map((brand) => (
                                                            <SelectItem key={brand.id} value={brand.id.toString()}>
                                                                {brand.name}
                                                            </SelectItem>
                                                        ))}
                                                    </SelectContent>
                                                </Select>
                                                {errors['vehicle.brand_id'] && (
                                                    <p className="text-red-500 text-sm mt-1">{errors['vehicle.brand_id']}</p>
                                                )}
                                            </div>

                                            <div>
                                                <Label htmlFor="category">Categoria *</Label>
                                                <Select
                                                    value={data.vehicle.category_id}
                                                    onValueChange={(value) => setData('vehicle', { ...data.vehicle, category_id: value })}
                                                >
                                                    <SelectTrigger className={errors['vehicle.category_id'] ? 'border-red-500' : ''}>
                                                        <SelectValue placeholder="Selecione a categoria" />
                                                    </SelectTrigger>
                                                    <SelectContent>
                                                        {categories.map((category) => (
                                                            <SelectItem key={category.id} value={category.id.toString()}>
                                                                {category.name}
                                                            </SelectItem>
                                                        ))}
                                                    </SelectContent>
                                                </Select>
                                                {errors['vehicle.category_id'] && (
                                                    <p className="text-red-500 text-sm mt-1">{errors['vehicle.category_id']}</p>
                                                )}
                                            </div>
                                        </div>

                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                            <div>
                                                <Label htmlFor="model">Modelo *</Label>
                                                <Input
                                                    id="model"
                                                    value={data.vehicle.model}
                                                    onChange={(e) => setData('vehicle', { ...data.vehicle, model: e.target.value })}
                                                    placeholder="Civic"
                                                    className={errors['vehicle.model'] ? 'border-red-500' : ''}
                                                />
                                                {errors['vehicle.model'] && (
                                                    <p className="text-red-500 text-sm mt-1">{errors['vehicle.model']}</p>
                                                )}
                                            </div>

                                            <div>
                                                <Label htmlFor="year">Ano *</Label>
                                                <Input
                                                    id="year"
                                                    type="number"
                                                    min="1900"
                                                    max={currentYear + 1}
                                                    value={data.vehicle.year}
                                                    onChange={(e) => setData('vehicle', { ...data.vehicle, year: e.target.value })}
                                                    placeholder="2020"
                                                    className={errors['vehicle.year'] ? 'border-red-500' : ''}
                                                />
                                                {errors['vehicle.year'] && (
                                                    <p className="text-red-500 text-sm mt-1">{errors['vehicle.year']}</p>
                                                )}
                                            </div>
                                        </div>

                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                            <div>
                                                <Label htmlFor="mileage">Quilometragem *</Label>
                                                <Input
                                                    id="mileage"
                                                    type="number"
                                                    min="0"
                                                    value={data.vehicle.mileage}
                                                    onChange={(e) => setData('vehicle', { ...data.vehicle, mileage: e.target.value })}
                                                    placeholder="50000"
                                                    className={errors['vehicle.mileage'] ? 'border-red-500' : ''}
                                                />
                                                {errors['vehicle.mileage'] && (
                                                    <p className="text-red-500 text-sm mt-1">{errors['vehicle.mileage']}</p>
                                                )}
                                            </div>

                                            <div>
                                                <Label htmlFor="fuel_type">Combustível *</Label>
                                                <Select
                                                    value={data.vehicle.fuel_type}
                                                    onValueChange={(value) => setData('vehicle', { ...data.vehicle, fuel_type: value })}
                                                >
                                                    <SelectTrigger className={errors['vehicle.fuel_type'] ? 'border-red-500' : ''}>
                                                        <SelectValue placeholder="Selecione o combustível" />
                                                    </SelectTrigger>
                                                    <SelectContent>
                                                        <SelectItem value="gasoline">Gasolina</SelectItem>
                                                        <SelectItem value="ethanol">Etanol</SelectItem>
                                                        <SelectItem value="flex">Flex</SelectItem>
                                                        <SelectItem value="diesel">Diesel</SelectItem>
                                                        <SelectItem value="electric">Elétrico</SelectItem>
                                                        <SelectItem value="hybrid">Híbrido</SelectItem>
                                                    </SelectContent>
                                                </Select>
                                                {errors['vehicle.fuel_type'] && (
                                                    <p className="text-red-500 text-sm mt-1">{errors['vehicle.fuel_type']}</p>
                                                )}
                                            </div>
                                        </div>

                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                            <div>
                                                <Label htmlFor="transmission">Transmissão *</Label>
                                                <Select
                                                    value={data.vehicle.transmission}
                                                    onValueChange={(value) => setData('vehicle', { ...data.vehicle, transmission: value })}
                                                >
                                                    <SelectTrigger className={errors['vehicle.transmission'] ? 'border-red-500' : ''}>
                                                        <SelectValue placeholder="Selecione a transmissão" />
                                                    </SelectTrigger>
                                                    <SelectContent>
                                                        <SelectItem value="manual">Manual</SelectItem>
                                                        <SelectItem value="automatic">Automático</SelectItem>
                                                        <SelectItem value="cvt">CVT</SelectItem>
                                                    </SelectContent>
                                                </Select>
                                                {errors['vehicle.transmission'] && (
                                                    <p className="text-red-500 text-sm mt-1">{errors['vehicle.transmission']}</p>
                                                )}
                                            </div>

                                            <div>
                                                <Label htmlFor="condition">Condição *</Label>
                                                <Select
                                                    value={data.vehicle.condition}
                                                    onValueChange={(value) => setData('vehicle', { ...data.vehicle, condition: value })}
                                                >
                                                    <SelectTrigger className={errors['vehicle.condition'] ? 'border-red-500' : ''}>
                                                        <SelectValue placeholder="Selecione a condição" />
                                                    </SelectTrigger>
                                                    <SelectContent>
                                                        <SelectItem value="new">Novo</SelectItem>
                                                        <SelectItem value="used">Usado</SelectItem>
                                                        <SelectItem value="certified">Seminovo certificado</SelectItem>
                                                    </SelectContent>
                                                </Select>
                                                {errors['vehicle.condition'] && (
                                                    <p className="text-red-500 text-sm mt-1">{errors['vehicle.condition']}</p>
                                                )}
                                            </div>
                                        </div>

                                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                            <div>
                                                <Label htmlFor="color">Cor *</Label>
                                                <Input
                                                    id="color"
                                                    value={data.vehicle.color}
                                                    onChange={(e) => setData('vehicle', { ...data.vehicle, color: e.target.value })}
                                                    placeholder="Branco"
                                                    className={errors['vehicle.color'] ? 'border-red-500' : ''}
                                                />
                                                {errors['vehicle.color'] && (
                                                    <p className="text-red-500 text-sm mt-1">{errors['vehicle.color']}</p>
                                                )}
                                            </div>

                                            <div>
                                                <Label htmlFor="doors">Portas *</Label>
                                                <Select
                                                    value={data.vehicle.doors}
                                                    onValueChange={(value) => setData('vehicle', { ...data.vehicle, doors: value })}
                                                >
                                                    <SelectTrigger className={errors['vehicle.doors'] ? 'border-red-500' : ''}>
                                                        <SelectValue placeholder="Nº de portas" />
                                                    </SelectTrigger>
                                                    <SelectContent>
                                                        <SelectItem value="2">2 portas</SelectItem>
                                                        <SelectItem value="3">3 portas</SelectItem>
                                                        <SelectItem value="4">4 portas</SelectItem>
                                                        <SelectItem value="5">5 portas</SelectItem>
                                                    </SelectContent>
                                                </Select>
                                                {errors['vehicle.doors'] && (
                                                    <p className="text-red-500 text-sm mt-1">{errors['vehicle.doors']}</p>
                                                )}
                                            </div>

                                            <div>
                                                <Label htmlFor="engine_size">Motor</Label>
                                                <Input
                                                    id="engine_size"
                                                    value={data.vehicle.engine_size}
                                                    onChange={(e) => setData('vehicle', { ...data.vehicle, engine_size: e.target.value })}
                                                    placeholder="2.0"
                                                    className={errors['vehicle.engine_size'] ? 'border-red-500' : ''}
                                                />
                                                {errors['vehicle.engine_size'] && (
                                                    <p className="text-red-500 text-sm mt-1">{errors['vehicle.engine_size']}</p>
                                                )}
                                            </div>
                                        </div>
                                    </CardContent>
                                </Card>
                            </div>

                            {/* Sidebar - Imagens */}
                            <div className="lg:col-span-1">
                                <Card>
                                    <CardHeader>
                                        <CardTitle>Imagens do Veículo</CardTitle>
                                    </CardHeader>
                                    <CardContent className="space-y-4">
                                        <div>
                                            <Label htmlFor="images">Adicionar imagens (máx. 10)</Label>
                                            <div className="mt-2">
                                                <input
                                                    id="images"
                                                    type="file"
                                                    multiple
                                                    accept="image/*"
                                                    onChange={handleImageUpload}
                                                    className="hidden"
                                                />
                                                <Button
                                                    type="button"
                                                    variant="outline"
                                                    className="w-full"
                                                    onClick={() => document.getElementById('images')?.click()}
                                                >
                                                    <Upload className="mr-2 h-4 w-4" />
                                                    Selecionar imagens
                                                </Button>
                                            </div>
                                        </div>

                                        {selectedImages.length > 0 && (
                                            <div className="space-y-2">
                                                <Label>Imagens selecionadas:</Label>
                                                <div className="grid grid-cols-2 gap-2">
                                                    {selectedImages.map((image, index) => (
                                                        <div key={index} className="relative">
                                                            <img
                                                                src={URL.createObjectURL(image)}
                                                                alt={`Preview ${index + 1}`}
                                                                className={`w-full h-20 object-cover rounded border-2 cursor-pointer ${
                                                                    featuredImageIndex === index 
                                                                        ? 'border-blue-500' 
                                                                        : 'border-gray-200'
                                                                }`}
                                                                onClick={() => setFeaturedImage(index)}
                                                            />
                                                            <Button
                                                                type="button"
                                                                variant="destructive"
                                                                size="sm"
                                                                className="absolute -top-2 -right-2 h-6 w-6 p-0"
                                                                onClick={() => removeImage(index)}
                                                            >
                                                                <X className="h-3 w-3" />
                                                            </Button>
                                                            {featuredImageIndex === index && (
                                                                <div className="absolute bottom-0 left-0 right-0 bg-blue-500 text-white text-xs text-center py-1">
                                                                    Principal
                                                                </div>
                                                            )}
                                                        </div>
                                                    ))}
                                                </div>
                                                <p className="text-sm text-gray-600">
                                                    Clique em uma imagem para defini-la como principal
                                                </p>
                                            </div>
                                        )}

                                        <div className="pt-4 border-t">
                                            <Button 
                                                type="submit" 
                                                className="w-full" 
                                                size="lg"
                                                disabled={processing}
                                            >
                                                {processing ? 'Criando...' : 'Criar Anúncio'}
                                            </Button>
                                        </div>
                                    </CardContent>
                                </Card>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </MainLayout>
    );
}
