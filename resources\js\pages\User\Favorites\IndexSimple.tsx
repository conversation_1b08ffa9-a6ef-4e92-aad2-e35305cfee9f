import MainLayout from '@/layouts/MainLayout';
import { PageProps } from '@/types';
import { Head } from '@inertiajs/react';

interface FavoritesIndexProps extends PageProps {
    favorites?: any;
    brands?: any;
    filters?: any;
}

export default function FavoritesIndexSimple({ favorites, brands, filters }: FavoritesIndexProps) {
    return (
        <MainLayout>
            <Head title="Meus Favoritos" />
            <div className="min-h-screen bg-gray-50">
                <div className="container mx-auto px-4 py-6">
                    <h1 className="text-2xl font-bold mb-6">Meus Favoritos</h1>
                    
                    <div className="bg-white p-6 rounded-lg shadow">
                        <h2 className="text-lg font-semibold mb-4">Debug Info:</h2>
                        <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
                            {JSON.stringify({ favorites, brands, filters }, null, 2)}
                        </pre>
                    </div>
                    
                    {favorites?.data && favorites.data.length > 0 ? (
                        <div className="mt-6">
                            <h3 className="text-lg font-semibold mb-4">Favoritos encontrados:</h3>
                            <div className="grid gap-4">
                                {favorites.data.map((favorite: any) => (
                                    <div key={favorite.id} className="bg-white p-4 rounded-lg shadow">
                                        <h4 className="font-semibold">{favorite.advertisement?.title}</h4>
                                        <p className="text-gray-600">R$ {favorite.advertisement?.price}</p>
                                    </div>
                                ))}
                            </div>
                        </div>
                    ) : (
                        <div className="mt-6 bg-white p-6 rounded-lg shadow text-center">
                            <p className="text-gray-600">Nenhum favorito encontrado</p>
                        </div>
                    )}
                </div>
            </div>
        </MainLayout>
    );
}
